# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: vsepc
long_name: Virtual Supervisor Exception Program Counter
address: 0x241
writable: true
virtual_address: 0x141
priv_mode: VS
length: 64
description: |
  Written with the PC of an instruction on an exception or interrupt taken in VS-mode.

  Also controls where the hart jumps on an exception return from VS-mode.
definedBy: H
fields:
  PC:
    location: 63-0
    description: |
      When a trap is taken into VS-mode, `vsepc.PC` is written with the virtual address of the
      instruction that was interrupted or that encountered the exception.
      Otherwise, `vsepc.PC` is never written by the implementation, though it may be explicitly written
      by software.

      On an exception return from VS-mode (from the SRET instruction),
      control transfers to the virtual address read out of `vsepc.PC`.

      Because PCs are always <% if ext?(:C) %>halfword<% else %>word<% end %>-aligned,
      <% if ext?(:C) %>bit 0<% else %>bits 1:0<% end %> of `vsepc.PC` are always
      read-only 0.

      [when,"ext?(:C) && MUTABLE_MISA_C == true"]
      When `misa.C` is clear, bit 1 is masked to zero. Writes to bit 1 are still captured, and
      may be visible on the next read with `misa.C` is set.

      Holds bits 63:<%= ext?(:C) ? 2 : 1 %> of the virtual address associated with an exception.
    type: RW-RH
    sw_write(csr_value): |
      return csr_value.PC & ~64'b1;
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  if (implemented?(ExtensionName::C) && CSR[misa].C == 1'b1) {
    return CSR[sepc].PC & ~64'b1;
  } else {
    return CSR[sepc].PC;
  }
