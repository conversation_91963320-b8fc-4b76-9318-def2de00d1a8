# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.ppregs
long_name: Print all registers pseudo-instruction (hint) working only in simulation environment
description: |
  The print registers instruction calls simulation environment with no explicit arguments.
  Implicit arguments are all general purpose registers.
  Simulation environment expected to print the all registers value on its console or standard output.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: ""
base: 32
encoding:
  match: "01110000000000000010000000010011"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 3;
  XReg arg = 0;
  iss_syscall(func,arg);
