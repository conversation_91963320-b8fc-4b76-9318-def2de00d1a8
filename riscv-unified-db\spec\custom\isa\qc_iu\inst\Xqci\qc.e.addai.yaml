# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.addai
long_name: Add immediate
description: |
  Add a 32-bit immediate `imm` to the value in `rd`, and store the result back in `rd`.
  Instruction encoded in QC.EAI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilia
base: 32
encoding:
  match: --------------------------------0010-----0011111
  variables:
    - name: imm
      location: 47-16
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, imm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = X[rd] + imm;
