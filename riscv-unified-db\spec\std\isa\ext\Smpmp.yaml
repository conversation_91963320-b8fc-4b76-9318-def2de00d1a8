# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Smpmp
type: privileged
long_name: Physical Memory Protection
defined_by: UDB
versions:
  - version: "1.11.0"
    state: ratified
    ratification_date: 2019-12
    changes:
      - Made the unused `pmpaddr` and `pmpcfg` fields *WARL*, rather than *WIRI*.
      - Specified semantics for PMP regions coarser than four bytes.
  - version: "1.12.0"
    state: ratified
    ratification_date: 2021-12
    changes:
      - PMP changes require an SFENCE.VMA on any hart that implements
        page-based virtual memory, even if VM is not currently enabled.
      - PMP reset values are now platform-defined.
      - An additional 48 optional PMP registers have been defined.
  - version: "1.13.0"
    state: frozen
    ratification_date: 2023-12
description: |
  To support secure processing and contain faults, it is desirable to limit the physical addresses
  accessible by software running on a hart. The optional PMP (Physical Memory Protection) unit
  provides per-hart machine-mode control registers to allow physical memory access privileges (read,
  write, execute) to be specified for each physical memory region. The PMP values are checked in
  parallel with the PMA checks.

  [NOTE]
  --
  This is a placeholder extension name defined by the `riscv-unified-db` and isn't considered an extension in
  the Privileged ISA manual. This was chosen as an extension rather than a M-mode parameter since the
  PMP has no visible ISA features (such as read-only-0 CSRs) if not present in an implementation.
  Making it an extension in the database prevents having the PMP CSRs show up in implementations that don't have a PMP.
  --
params:
  NUM_PMP_ENTRIES:
    description: |
      Number of implemented PMP entries. Can be any value between 0-64, inclusive.

      The architecture mandates that the number of implemented PMP registers
      must appear to be 0, 16, or 64.

      Therefore, pmp registers will behave as follows according to NUN_PMP_ENTRIES:

      [separator="!"]
      !===
      ! NUM_PMP_ENTRIES ! pmpaddr<0-15> / pmpcfg<0-3> ! pmpaddr<16-63> / pmpcfg<4-15>
      ! 0               ! N                           ! N
      ! 1-16            ! Y                           ! N
      ! 17-64           ! Y                           ! Y
      !===

      ** N = Not implemented; access will cause `IllegalInstruction`
         if TRAP_ON_UNIMPLEMENTED_CSR is true
      ** Y = Implemented; access will not cause an exception (from M-mode), but register
         may be read-only-zero if NUM_PMP_ENTRIES is less than the corresponding register

      [NOTE]
      `pmpcfgN` for an odd N never exists when XLEN == 64

      When NUM_PMP_ENTRIES is not exactly 0, 16, or 64, some extant pmp registers,
      and associated pmpNcfg, will be read-only zero (but will never cause an exception).
    schema:
      type: integer
      minimum: 0
      maximum: 64
  PMP_GRANULARITY:
    description: |
      log2 of the smallest supported PMP region.

      Generally, for systems with an MMU, should not be smaller than 12,
      as that would preclude caching PMP results in the TLB along with
      virtual memory translations

      Note that PMP_GRANULARITY is equal to G+2 (not G) as described in
      the privileged architecture.
    schema:
      type: integer
      minimum: 2
      maximum: 66
