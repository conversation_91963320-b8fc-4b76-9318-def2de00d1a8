# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zic<PERSON>ss
long_name: Shadow Stack
description: |
  TODO
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2024-07
params:
  REPORT_CAUSE_IN_MTVAL_ON_SHADOW_STACK_SOFTWARE_CHECK:
    description: |
      When true, `mtval` is written with the shadow stack cause (code=3) when a SoftwareCheck exception is raised into M-mode due to a shadow stack pop check instruction.

      When false, `mtval` is written with 0.
    schema:
      type: boolean
  REPORT_CAUSE_IN_STVAL_ON_SHADOW_STACK_SOFTWARE_CHECK:
    description: |
      When true, `stval` is written with the shadow stack cause (code=3) when a SoftwareCheck exception is raised into S-mode due to a shadow stack pop check instruction.

      When false, `stval` is written with 0.
    schema:
      type: boolean
  REPORT_CAUSE_IN_VSTVAL_ON_SHADOW_STACK_SOFTWARE_CHECK:
    description: |
      When true, `vstval` is written with the shadow stack cause (code=3) when a SoftwareCheck exception is raised into VS-mode due to a shadow stack pop check instruction.

      When false, `vstval` is written with 0.
    schema:
      type: boolean
