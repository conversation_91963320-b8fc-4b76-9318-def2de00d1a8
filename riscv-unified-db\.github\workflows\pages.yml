name: Deploy pages
on:
  push:
    branches:
      - main
  workflow_dispatch:
permissions:
  contents: read
  pages: write
  id-token: write
concurrency:
  group: "pages"
  cancel-in-progress: false
jobs:
  pages:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Create many artifacts in the _site directory
        run: /bin/bash tools/scripts/deploy.sh
      - name: Setup Pages
        uses: actions/configure-pages@v5
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: "_site"
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
