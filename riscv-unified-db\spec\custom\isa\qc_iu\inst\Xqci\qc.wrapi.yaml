# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.wrapi
long_name: Wraparound (Unsigned Immediate)
description: |
  If `rs1` >= `imm` perform subtraction between `rs1` and `imm`.
  If `rs1` < 0, perform addition between `rs1` and `imm`,
  else, select `rs1`. The result is stored in `rd`.
  Instruction encoded in I instruction format.
  The `imm` is an unsigned immediate.
definedBy:
  anyOf:
    - Xqci
    - Xqcia
base: 32
encoding:
  match: 0----------------000-----0001011
  variables:
    - name: imm
      location: 30-20
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, imm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg rs1_value = X[rs1];

  # IDL will only compare signed to signed, so $signed({1'b0, imm}) is a way to make the
  # unsigned `imm` into a signed type
  X[rd] = ($signed(rs1_value) >= $signed({1'b0, imm}))
    ? rs1_value - imm
    : (($signed(rs1_value) < 's0)
       ? ($signed(rs1_value) + imm)
       : rs1_value);
