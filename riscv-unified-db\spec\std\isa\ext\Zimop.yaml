# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zimop
long_name: May-be Operations
description: |
  The "Zimop" extension introduces the concept of
  instructions that _may be operations_ (MOPs).  MOPs are initially defined to
  simply write zero to `x[rd]`, but are designed to be redefined by later
  extensions to perform some other action.
  The Zimop extension defines an encoding space for 40 MOPs.

  [NOTE]
  ====
  It is sometimes desirable to define instruction-set extensions whose
  instructions, rather than raising illegal-instruction exceptions when the extension is
  not implemented, take no useful action (beyond writing `x[rd]`).
  For example, programs with control-flow integrity checks can
  execute correctly on implementations without the corresponding extension,
  provided the checks are simply ignored.  Implementing these checks as MOPs
  allows the same programs to run on implementations with or without the
  corresponding extension.

  Although similar in some respects to HINTs, MOPs cannot be encoded as HINTs,
  because unlike HINTs, MOPs are allowed to alter architectural state.

  Because MOPs may be redefined by later extensions, standard software should
  not execute a MOP unless it is deliberately targeting an extension that has
  redefined that MOP.
  ====

  The Zimop extension defines 32 MOP instructions named MOP.R.__n__, where
  __n__ is an integer between 0 and 31, inclusive.
  Unless redefined by another extension, these instructions simply write 0 to
  `x[rd]`.  Their encoding allows future extensions to define them to read `x[rs1]`,
  as well as write `x[rd]`.

  The Zimop extension additionally defines 8 MOP instructions named
  MOP.RR.__n__, where __n__ is an integer between 0 and 7, inclusive.
  Unless redefined by another extension, these instructions simply
  write 0 to `x[rd]`.  Their encoding allows future extensions to define them to
  read `x[rs1]` and `x[rs2]`, as well as write `x[rd]`.

  NOTE: The recommended assembly syntax for MOP.R.__n__ is MOP.R.__n__ rd, rs1,
  with any `x`-register specifier being valid for either argument.  Similarly for
  MOP.RR.__n__, the recommended syntax is MOP.RR.__n__ rd, rs1, rs2.
  The extension that redefines a MOP may define an alternate assembly mnemonic.

  NOTE: These MOPs are encoded in the SYSTEM major opcode in part because it is
  expected their behavior will be modulated by privileged CSR state.

  NOTE: These MOPs are defined to write zero to `x[rd]`, rather than performing
  no operation, to simplify instruction decoding and to allow testing the
  presence of features by branching on the zeroness of the result.

  The MOPs defined in the Zimop extension do not carry a syntactic dependency
  from `x[rs1]` or `x[rs2]` to `x[rd]`, though an extension that redefines the
  MOP may impose such a requirement.

  NOTE: Not carrying a syntactic dependency relieves straightforward
  implementations of reading `x[rs1]` and `x[rs2]`.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
