{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["$schema", "kind", "name", "long_name"], "properties": {"$schema": {"const": "manual_schema.json#"}, "kind": {"const": "manual"}, "name": {"type": "string", "description": "Name (database key) of this manual"}, "long_name": {"type": "string", "description": "One line description of the manual"}, "marketing_name": {"type": "string", "description": "The publicly displayed manual name"}, "state": {"$ref": "schema_defs.json#/$defs/spec_state", "description": "State of this version"}, "url": {"type": "string", "format": "uri", "description": "URL to the repository"}, "license": {"$ref": "schema_defs.json#/$defs/license"}, "$source": {"type": "string", "format": "uri-reference", "description": "Relative (from arch/) path to the original source file"}}, "additionalProperties": false}