# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zcf
long_name: Compressed instructions for single precision floating point
description: |
  Zcf is the existing set of compressed single precision floating point loads and stores (RV32 only):
  `c.flw`, `c.flwsp`, `c.fsw`, `c.fswsp`.

type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-04
    repositories:
      - url: https://github.com/riscv/riscv-code-size-reduction
        branch: main
    contributors:
      - name: <PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
      - name: sinan
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON><PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
    requires:
      allOf:
        - anyOf:
          - { name: <PERSON>ca, version: "= 1.0.0" }
          - { name: C, version: "~> 2.0.0" }
        - { name: F, version: "~> 2.2.0" }
