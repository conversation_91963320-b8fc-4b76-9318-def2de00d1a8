# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: flq
long_name: No synopsis available
description: |
  No description available.
definedBy: Q
assembly: fd, xs1, imm
encoding:
  match: -----------------100-----0000111
  variables:
    - name: imm
      location: 31-20
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
