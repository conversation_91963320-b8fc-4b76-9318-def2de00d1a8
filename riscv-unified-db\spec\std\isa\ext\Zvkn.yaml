# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvkn
long_name: NIST Algorithm Suite
description: |
  This extension is shorthand for the following set of other extensions:

  * `Zvkned`
  * `Zvknhb`
  * `Zvkb`
  * `Zvkt`
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    implies:
      - name: Zvkned
        version: "1.0.0"
      - name: Zvknhb
        version: "1.0.0"
      - name: Zvkb
        version: "1.0.0"
      - name: Zvkt
        version: "1.0.0"
