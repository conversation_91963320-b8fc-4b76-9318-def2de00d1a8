# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.lilt
long_name: Conditional load immediate if less than (Register)
description: |
  Move `simm` to `rd` if the value in `rs1` is less than value `rs2`.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcicli
base: 32
encoding:
  match: -----01----------100-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: simm
      location: 31-27
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2, simm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if ($signed(X[rs1]) < $signed(X[rs2])) {
    X[rd] = sext(simm, 5);
  }
