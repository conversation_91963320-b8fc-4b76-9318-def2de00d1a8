# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: U
long_name: User-mode privilege level
description: |
  User-mode privilege level is supported by an implementation if the U extension is present.
  Note that the RISC-V ISA doesn't formally define a U extension and it is only discussed in the Privileged ISA manual.
type: privileged
versions:
- version: "1.0.0"
  state: ratified
  ratification_date: 2019-12
  defined_by: UDB
params:
  MUTABLE_MISA_U:
    description: |
      Indicates whether or not the `U` extension can be disabled with the `misa.U` bit.
    schema:
      type: boolean
  U_MODE_ENDIANNESS:
    description: |
      Endianness of data in U-mode. Can be one of:

       * little:  U-mode data is always little endian
       * big:     U-mode data is always big endian
       * dynamic: U-mode data can be either little or big endian,
                  depending on the CSR field `mstatus.UBE`
    schema:
      type: string
      enum: [little, big, dynamic]
  UXLEN:
    description: |
      Set of XLENs supported in U-mode. Can be one of:

        * 32:   SXLEN is always 32
        * 64:   SXLEN is always 64
        * 3264: SXLEN can be changed (via mstatus.UXL) between 32 and 64
    schema:
      type: integer
      enum: [32, 64, 3264]
    extra_validation: |
      assert UXLEN == 32 if MXLEN == 32
  TRAP_ON_ECALL_FROM_U:
    description: |
      Whether or not an ECALL-from-U-mode causes a synchronous exception.

      The spec states that implementations may handle ECALLs transparently
      without raising a trap, in which case the EEI must provide a builtin.
    schema:
      type: boolean
      default: true
