# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: sscratch
long_name: Supervisor Scratch Register
address: 0x140
writable: true
priv_mode: S
length: 64
description: Scratch register for software use. Bits are not interpreted by hardware.
definedBy: S # actually, defined by RV64, but must implement U-mode for this CSR to exist
fields:
  SCRATCH:
    location: 63-0
    description: Scratch value
    type: RW
    reset_value: UNDEFINED_LEGAL
