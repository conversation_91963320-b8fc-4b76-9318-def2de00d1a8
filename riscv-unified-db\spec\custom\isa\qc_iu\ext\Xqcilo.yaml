# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqcilo
type: unprivileged
long_name: Qualcomm large offset load/store
versions:
- version: "0.1.0"
  state: development
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Add information about instruction formats of each instruction
  requires: { name: <PERSON><PERSON>, version: ">= 1.0.0" }
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code sign extension logic for qc.e.lb and qc.e.lh instructions
description: |
  The Xqcilo extension includes eight 48-bit load/stores instructions that use an offset
  larger than can be found in the base RISC-V ISA.

doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
