#include "udb/db_data.hxx"

std::map<std::string, std::string> udb::DbData::SCHEMAS = {
      { "schema_defs.json",
  R"SCHEMA(
  <%= File.read("#{$resolver.schemas_path}/schema_defs.json") %>
  )SCHEMA"
      },
      <%- Dir.glob("#{$resolver.schemas_path}/config*").map do |f| -%>
        { "<%= File.basename(f) %>",
  R"SCHEMA(
  <%= File.read(f) %>
  )SCHEMA"
        }
      <%- end.join(", ") -%>
    };
