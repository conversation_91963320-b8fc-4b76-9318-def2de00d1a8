# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: feq.d
long_name: No synopsis available
description: |
  No description available.
definedBy: D
assembly: xd, fs1, fs2
encoding:
  match: 1010001----------010-----1010011
  variables:
    - name: fs2
      location: 24-20
    - name: fs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
