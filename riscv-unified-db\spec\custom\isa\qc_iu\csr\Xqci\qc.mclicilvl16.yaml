# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl16
long_name: IRQ Level 16
address: 0xbd0
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 128-135
fields:
  IRQ128:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ128 level
  IRQ129:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ129 level
  IRQ130:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ130 level
  IRQ131:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ131 level
  IRQ132:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ132 level
  IRQ133:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ133 level
  IRQ134:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ134 level
  IRQ135:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ135 level
