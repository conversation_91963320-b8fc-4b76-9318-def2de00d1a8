<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: DecodeField
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "DecodeField";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (D)</a> &raquo;
    
    
    <span class="title">DecodeField</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: DecodeField
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">DecodeField</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/opcodes.rb</dd>
  </dl>
  
</div>





  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#decode_variable-instance_method" title="#decode_variable (instance method)">#<strong>decode_variable</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>aliases of this field.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the name of the field.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#bits-instance_method" title="#bits (instance method)">#<strong>bits</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns bits of the encoding that make up the field, as an array   Each item of the array is either:     - A number, to represent a single bit     - A range, to represent a continugous range of bits.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#eql%3F-instance_method" title="#eql? (instance method)">#<strong>eql?</strong>(other)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extract-instance_method" title="#extract (instance method)">#<strong>extract</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>return code to extract the field.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#grouped_encoding_fields-instance_method" title="#grouped_encoding_fields (instance method)">#<strong>grouped_encoding_fields</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#hash-instance_method" title="#hash (instance method)">#<strong>hash</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(inst, name, decode_ring_key, field_data)  &#x21d2; DecodeField </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of DecodeField.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#sext%3F-instance_method" title="#sext? (instance method)">#<strong>sext?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>true if the field should be sign extended.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#size-instance_method" title="#size (instance method)">#<strong>size</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the number of bits in the field, _including any implicit ones_.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#size_in_encoding-instance_method" title="#size_in_encoding (instance method)">#<strong>size_in_encoding</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the number of bits in the field, _not including any implicit ones_.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#split%3F-instance_method" title="#split? (instance method)">#<strong>split?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns true if the field is encoded across more than one groups of bits.</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(inst, name, decode_ring_key, field_data)  &#x21d2; <tt><span class='object_link'><a href="" title="DecodeField (class)">DecodeField</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of DecodeField.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


236
237
238
239
240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271
272
273
274
275
276
277
278
279
280
281
282
283
284
285
286
287
288</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 236</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_inst'>inst</span><span class='comma'>,</span> <span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No field &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;, needed by </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_inst'>inst</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>, in Opcodes module</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="Opcodes.html" title="Opcodes (module)">Opcodes</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Opcodes.html#DECODER_RING-constant" title="Opcodes::DECODER_RING (constant)">DECODER_RING</a></span></span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='rparen'>)</span>

  <span class='ivar'>@encoding_fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Split fields &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39; must have an alias</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='symbol'>:decode_variable</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Split fields &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39; must have a group_by field</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='symbol'>:group_by</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='period'>.</span><span class='id identifier rubyid_each_index'>each_index</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_i'>i</span><span class='op'>|</span>
      <span class='id identifier rubyid_range'>range</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:group_by</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='lparen'>(</span><span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:group_by</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='op'>..</span><span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:group_by</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='rparen'>)</span> <span class='op'>:</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:group_by</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>expecting a range</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span>

      <span class='id identifier rubyid_display'>display</span> <span class='op'>=</span>
        <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:display</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
          <span class='kw'>nil</span>
        <span class='kw'>elsif</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:display</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
          <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:display</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span>
        <span class='kw'>else</span>
          <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:display</span><span class='rbracket'>]</span>
        <span class='kw'>end</span>
      <span class='ivar'>@encoding_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_range'>range</span><span class='comma'>,</span> <span class='id identifier rubyid_display'>display</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>decode_ring_key must be an Array or a String</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_range'>range</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:bits</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:bits</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:group_by</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>expecting a range</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span>

    <span class='ivar'>@encoding_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_decode_ring_key'>decode_ring_key</span><span class='comma'>,</span> <span class='id identifier rubyid_range'>range</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:display</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_name'>name</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='symbol'>:decode_variable</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:decode_variable</span><span class='rbracket'>]</span> <span class='op'>!=</span> <span class='id identifier rubyid_name'>name</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:decode_variable</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>
      <span class='ivar'>@alias</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:decode_variable</span><span class='rbracket'>]</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:decode_variable</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>

      <span class='id identifier rubyid_other_aliases'>other_aliases</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='symbol'>:decode_variable</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_reject'>reject</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='op'>|</span> <span class='id identifier rubyid_a'>a</span> <span class='op'>==</span> <span class='ivar'>@name</span> <span class='rbrace'>}</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_other_aliases'>other_aliases</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span>
        <span class='ivar'>@alias</span> <span class='op'>=</span> <span class='id identifier rubyid_other_aliases'>other_aliases</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span>
      <span class='kw'>else</span>
        <span class='ivar'>@alias</span> <span class='op'>=</span> <span class='id identifier rubyid_other_aliases'>other_aliases</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='ivar'>@decode_variable</span> <span class='op'>=</span> <span class='ivar'>@alias</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected: </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>

  <span class='ivar'>@field_data</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span>

<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="decode_variable-instance_method">
  
    #<strong>decode_variable</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>aliases of this field.</p>

<pre class="code ruby"><code class="ruby">if there is one alias, a String
if there is more than one alias, an Array
if there are no aliases, nil
</code></pre>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


234
235
236</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 234</span>

<span class='kw'>def</span> <span class='id identifier rubyid_decode_variable'>decode_variable</span>
  <span class='ivar'>@decode_variable</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the name of the field</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


227
228
229</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 227</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="bits-instance_method">
  
    #<strong>bits</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns bits of the encoding that make up the field, as an array</p>

<pre class="code ruby"><code class="ruby"> Each item of the array is either:
   - A number, to represent a single bit
   - A range, to represent a continugous range of bits

The array is ordered from encoding MSB (at index 0) to LSB (at index n-1)
</code></pre>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


309
310
311</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 309</span>

<span class='kw'>def</span> <span class='id identifier rubyid_bits'>bits</span>
  <span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:bits</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='lbracket'>[</span><span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:bits</span><span class='rbracket'>]</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:bits</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="eql?-instance_method">
  
    #<strong>eql?</strong>(other)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


290
291
292</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 290</span>

<span class='kw'>def</span> <span class='id identifier rubyid_eql?'>eql?</span><span class='lparen'>(</span><span class='id identifier rubyid_other'>other</span><span class='rparen'>)</span>
  <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_eql?'>eql?</span><span class='lparen'>(</span><span class='id identifier rubyid_other'>other</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extract-instance_method">
  
    #<strong>extract</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>return code to extract the field</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357
358
359
360</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 333</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extract'>extract</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>=</span> <span class='int'>0</span>
  <span class='id identifier rubyid_bits'>bits</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_b'>b</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_op'>op</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span>
      <span class='comment'># shamt = size - so_far - 1
</span>      <span class='comment'># op = &quot;(#{op} &lt;&lt; #{shamt})&quot; if shamt != 0
</span>      <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_op'>op</span>
      <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>+=</span> <span class='int'>1</span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_op'>op</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_end'>end</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_begin'>begin</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span>
      <span class='comment'># shamt = size - so_far - b.size
</span>      <span class='comment'># op = &quot;(#{op} &lt;&lt; #{shamt})&quot; if shamt != 0
</span>      <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_op'>op</span>
      <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>+=</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:lshift</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;d0</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:lshift</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_ops'>ops</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>&gt;</span> <span class='int'>1</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>{</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ops'>ops</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>, </span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_ops'>ops</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>sext(</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ops'>ops</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_sext?'>sext?</span>
  <span class='id identifier rubyid_ops'>ops</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="grouped_encoding_fields-instance_method">
  
    #<strong>grouped_encoding_fields</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


313
314
315</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 313</span>

<span class='kw'>def</span> <span class='id identifier rubyid_grouped_encoding_fields'>grouped_encoding_fields</span>
  <span class='ivar'>@encoding_fields</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="hash-instance_method">
  
    #<strong>hash</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


294
295
296</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 294</span>

<span class='kw'>def</span> <span class='id identifier rubyid_hash'>hash</span>
  <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_hash'>hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="sext?-instance_method">
  
    #<strong>sext?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>true if the field should be sign extended</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


328
329
330</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 328</span>

<span class='kw'>def</span> <span class='id identifier rubyid_sext?'>sext?</span>
  <span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:sext</span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='kw'>true</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="size-instance_method">
  
    #<strong>size</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the number of bits in the field, _including any implicit ones_</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


318
319
320</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 318</span>

<span class='kw'>def</span> <span class='id identifier rubyid_size'>size</span>
  <span class='id identifier rubyid_size_in_encoding'>size_in_encoding</span> <span class='op'>+</span> <span class='lparen'>(</span><span class='ivar'>@field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='symbol'>:lshift</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='ivar'>@field_data</span><span class='lbracket'>[</span><span class='symbol'>:lshift</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='int'>0</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="size_in_encoding-instance_method">
  
    #<strong>size_in_encoding</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the number of bits in the field, _not including any implicit ones_</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


323
324
325</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 323</span>

<span class='kw'>def</span> <span class='id identifier rubyid_size_in_encoding'>size_in_encoding</span>
  <span class='id identifier rubyid_bits'>bits</span><span class='period'>.</span><span class='id identifier rubyid_reduce'>reduce</span><span class='lparen'>(</span><span class='int'>0</span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_sum'>sum</span><span class='comma'>,</span> <span class='id identifier rubyid_f'>f</span><span class='op'>|</span> <span class='id identifier rubyid_sum'>sum</span> <span class='op'>+</span> <span class='lparen'>(</span><span class='id identifier rubyid_f'>f</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='int'>1</span> <span class='op'>:</span> <span class='id identifier rubyid_f'>f</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="split?-instance_method">
  
    #<strong>split?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns true if the field is encoded across more than one groups of bits</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


299
300
301</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 299</span>

<span class='kw'>def</span> <span class='id identifier rubyid_split?'>split?</span>
  <span class='id identifier rubyid_encoding_fields'>encoding_fields</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>&gt;</span> <span class='int'>1</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>