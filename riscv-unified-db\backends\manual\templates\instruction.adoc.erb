:tabs-sync-option:

<%= anchor_for_udb_doc_inst(inst.name) %>
= <%= inst.name %>

*<%= inst.long_name %>*

<%= inst.fix_entities(inst.description) %>

<%- if inst.data_independent_timing? -%>
[IMPORTANT]
This instruction must have data-independent timing when extension `Zkt` is enabled.
<%- end -%>

== Assembly format

`<%= inst.name %> <%= inst.assembly.gsub('x', 'r') %>`

== Decode Variables

<%- if inst.multi_encoding? -%>
[tabs]
====
RV32::
+
[source,idl]
----
<%- inst.decode_variables(32).each do |d| -%>
<%= d.sext? ? 'signed ' : '' %>Bits<<%= d.size %>> <%= d.name %> = <%= d.extract %>;
<%- end -%>
----

RV64::
+
[source,idl]
----
<%- inst.decode_variables(64).each do |d| -%>
<%= d.sext? ? 'signed ' : '' %>Bits<<%= d.size %>> <%= d.name %> = <%= d.extract %>;
<%- end -%>
----
====
<%- else -%>
[source,idl]
----
<%- inst.decode_variables(inst.base.nil? ? 32 : inst.base).each do |d| -%>
<%= d.sext? ? 'signed ' : '' %>Bits<<%= d.size %>> <%= d.name %> = <%= d.extract %>;
<%- end -%>
----
<%- end -%>

== Execution

[tabs]
====
<%- if inst.key?("operation()") -%>
IDL::
+
[source,idl,subs="specialchars,macros"]
----
<%= inst.fix_entities(inst.operation_ast.gen_adoc) %>
----
<%- end -%>

<%- if inst.key?("sail()") -%>
Sail::
+
[source,sail]
----
<%= inst.fix_entities(inst.data["sail()"]) %>
----
<%- end -%>
====

<% exception_list = inst.reachable_exceptions_str(inst.base.nil? ? cfg_arch.param_values["MXLEN"] : inst.base) -%>
<%- unless exception_list.empty? -%>
== Exceptions

This instruction may result in the following synchronous exceptions:

  <%- exception_list.sort.each do |etype| -%>
  * <%= inst.fix_entities(etype) %>
  <%- end -%>

<%- end -%>

== Encoding

<%- if inst.multi_encoding? -%>
[NOTE]
This instruction has different encodings in RV32 and RV64.

====
RV32::
+
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= inst.processed_wavedrom_desc(32) %>
....

RV64::
+
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= inst.processed_wavedrom_desc(64) %>
....
====
<%- else -%>
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= inst.processed_wavedrom_desc(inst.base.nil? ? 32 : inst.base) %>
....
<%- end -%>

== Defining extension

<%= inst.fix_entities(inst.defined_by_condition.to_asciidoc) %>

== Access
[cols="^,^,^,^,^"]
|===
| M | HS | U | VS | VU

| [.access-always]#Always#
| [.access-<%=inst.access['s']%>]#<%= inst.access['s'].capitalize %>#
| [.access-<%=inst.access['u']%>]#<%= inst.access['u'].capitalize %>#
| [.access-<%=inst.access['vs']%>]#<%= inst.access['vs'].capitalize %>#
| [.access-<%=inst.access['vu']%>]#<%= inst.access['vu'].capitalize %>#
|===

<%- if inst.access_detail? -%>
<%= inst.fix_entities(inst.access_detail) %>
<%- end -%>

== Containing profiles

<%- inst_mandatory_ext = [] -%>
<%- inst_optional_ext = [] -%>
<%- cfg_arch.profiles.each do |profile| -%>
<%-
  in_profile_mandatory = profile.mandatory_ext_reqs.any? do |ext_req|
    ext_versions = ext_req.satisfying_versions
    ext_versions.any? { |ext_ver| inst.defined_by_condition.possibly_satisfied_by?(ext_ver) }
  end
  in_profile_optional = !in_profile_mandatory && profile.optional_ext_reqs.any? do |ext_req|
    ext_versions = ext_req.satisfying_versions
    ext_versions.any? { |ext_ver| inst.defined_by_condition.possibly_satisfied_by?(ext_ver) }
  end
  if in_profile_mandatory
-%>
<%- inst_mandatory_ext.push(profile.marketing_name) -%>
<%- elsif in_profile_optional -%>
<%- inst_optional_ext.push(profile.marketing_name) -%>
<%- end -%>
<%- end -%>

* Mandatory: <%= inst_mandatory_ext.join(", ") %>
* Optional: <%= inst_optional_ext.join(", ") %>
