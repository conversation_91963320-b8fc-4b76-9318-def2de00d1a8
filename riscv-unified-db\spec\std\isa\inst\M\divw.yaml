# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: divw
long_name: Signed 32-bit division
description: |
  Divide the lower 32-bits of register xs1 by the lower 32-bits of register xs2,
  and store the sign-extended result in xd.

  The remainder is discarded.

  Division by zero will put -1 into xd.

  Division resulting in signed overflow (when most negative number is divided by -1)
  will put the most negative number into xd;
definedBy: M
base: 64
assembly: xd, xs1, xs2
encoding:
  match: 0000001----------100-----0111011
  variables:
    - name: xs2
      location: 24-20
    - name: xs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::M) && (CSR[misa].M == 1'b0)) {
    raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  Bits<32> src1 = X[xs1][31:0];
  Bits<32> src2 = X[xs2][31:0];

  if (src2 == 0) {
    # division by zero. Since RISC-V does not have arithmetic exceptions, the result is defined
    # to be -1
    X[xd] = {MXLEN{1'b1}};

  } else if ((src1 == {33'b1, 31'b0}) && (src2 == 32'b1)) {
    # signed overflow. Since RISC-V does not have arithmetic exceptions, the result is defined
    # to be the most negative number (-2^(31))
    X[xd] = {33'b1, 31'b0};

  } else {
    # no special case, just divide
    Bits<32> result = $signed(src1) / $signed(src2);
    Bits<1> sign_bit = result[31];

    X[xd] = {{32{sign_bit}}, result};
  }

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    if extension("M") then {
      let rs1_val = X(rs1)[31..0];
      let rs2_val = X(rs2)[31..0];
      let rs1_int : int = if s then signed(rs1_val) else unsigned(rs1_val);
      let rs2_int : int = if s then signed(rs2_val) else unsigned(rs2_val);
      let q : int = if rs2_int == 0 then -1 else quot_round_zero(rs1_int, rs2_int);
      /* check for signed overflow */
      let q': int = if s & q > (2 ^ 31 - 1) then  (0 - 2^31) else q;
      X(rd) = sign_extend(to_bits(32, q'));
      RETIRE_SUCCESS
    } else {
      handle_illegal();
      RETIRE_FAIL
    }
  }

# SPDX-SnippetEnd
