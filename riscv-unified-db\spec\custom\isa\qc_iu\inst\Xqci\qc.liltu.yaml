# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.liltu
long_name: Conditional load immediate if less than unsigned (Register)
description: |
  Move `simm` to `rd` if the unsigned value in `rs1` is less than unsigned value `rs2`.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcicli
base: 32
encoding:
  match: -----01----------110-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: simm
      location: 31-27
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2, simm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (X[rs1] < X[rs2]) {
    X[rd] = sext(simm, 5);
  }
