# Copyright (c) 2025 <PERSON><PERSON>
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_subtype_schema.json

$schema: inst_subtype_schema.json#
kind: instruction_subtype
name: I-x-x

data:
  type: { "$ref": "inst_type/I.yaml#" }
  subtype: { "$ref": "inst_subtype/I/I-x-x.yaml#" }
  opcodes:
    $inherits: inst_type/I.yaml#/opcodes
  variables:
    imm:
      $inherits: inst_var/I-imm.yaml#/data
    xs1:
      $inherits: inst_var/xs1.yaml#/data
    xd:
      $inherits: inst_var/xd.yaml#/data
