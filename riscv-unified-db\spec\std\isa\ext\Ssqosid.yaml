# Copyright (c) Syed <PERSON><PERSON>
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ssqosid
type: privileged
long_name: Quality-of-Service Identifiers
description: |
  Quality of Service (QoS) is defined as the minimal end-to-end performance guaranteed in advance by a service level agreement (SLA) to a workload.
  Performance metrics might include measures such as instructions per cycle (IPC), latency of service, etc.

  When multiple workloads execute concurrently on modern processors—equipped with large core counts, multiple cache hierarchies, and multiple memory
  controllers—the performance of any given workload becomes less deterministic, or even non-deterministic, due to shared resource contention.

  To manage performance variability, system software needs resource allocation and monitoring capabilities.
  These capabilities allow for the reservation of resources like cache and bandwidth, thus meeting individual performance targets while minimizing interference.
  For resource management, hardware should provide monitoring features that allow system software to profile workload resource consumption and allocate resources accordingly.

  To facilitate this, the QoS Identifiers extension (Ssqosid) introduces the srmcfg register,
  which configures a hart with two identifiers: a Resource Control ID (RCID) and a Monitoring Counter ID (MCID).
  These identifiers accompany each request issued by the hart to shared resource controllers.

  Additional metadata, like the nature of the memory access and the ID of the originating supervisor domain,
  can accompany RCID and MCID. Resource controllers may use this metadata for differentiated service such as a different capacity allocation for code storage vs. data storage.
  Resource controllers can use this data for security policies such as not exposing statistics of one security domain to another.

  These identifiers are crucial for the RISC-V Capacity and Bandwidth Controller QoS Register Interface (CBQRI) specification,
  which provides methods for setting resource usage limits and monitoring resource consumption.
  The RCID controls resource allocations, while the MCID is used for tracking resource usage.

params:
  RCID_WIDTH:
    description: |
      Number of bits used for the Resource Control ID field (RCID).
      Default is 12.
    schema:
      type: integer
      minimum: 1
      maximum: 12

  MCID_WIDTH:
    description: |
      Number of bits used for the Monitoring Counter ID field (MCID).
      Default is 12.
    schema:
      type: integer
      minimum: 1
      maximum: 12

versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: "2024-06"
    url: "https://github.com/riscv/riscv-isa-manual/releases/tag/riscv-isa-release-5308687-2025-04-22"
    requires: { name: S, version: ~> 1.13 }
