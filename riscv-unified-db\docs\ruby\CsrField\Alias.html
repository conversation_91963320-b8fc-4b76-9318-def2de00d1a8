<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: CsrField::<PERSON>as
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "CsrField::Alias";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (A)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../CsrField.html" title="CsrField (class)">CsrField</a></span></span>
     &raquo; 
    <span class="title">Alias</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: CsrField::Alias
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Struct</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Struct</li>
          
            <li class="next">CsrField::Alias</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>





  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#field-instance_method" title="#field (instance method)">#<strong>field</strong>  &#x21d2; CsrField </a>
    

    
  </span>
  
  
  
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The field being aliased.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#range-instance_method" title="#range (instance method)">#<strong>range</strong>  &#x21d2; Range </a>
    

    
  </span>
  
  
  
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Range of the aliased field that is being pointed to.</p>
</div></span>
  
</li>

    
  </ul>





  
  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id="field=-instance_method"></span>
      <div class="method_details first">
  <h3 class="signature first" id="field-instance_method">
  
    #<strong>field</strong>  &#x21d2; <tt><span class='object_link'><a href="../CsrField.html" title="CsrField (class)">CsrField</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The field being aliased.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="../CsrField.html" title="CsrField (class)">CsrField</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The field being aliased</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


82</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 82</span>

<span class='const'><span class='object_link'><a href="" title="CsrField::Alias (class)">Alias</a></span></span> <span class='op'>=</span> <span class='const'>Struct</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='symbol'>:field</span><span class='comma'>,</span> <span class='symbol'>:range</span><span class='rparen'>)</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id="range=-instance_method"></span>
      <div class="method_details ">
  <h3 class="signature " id="range-instance_method">
  
    #<strong>range</strong>  &#x21d2; <tt>Range</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Range of the aliased field that is being pointed to.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Range</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Range of the aliased field that is being pointed to</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


82</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 82</span>

<span class='const'><span class='object_link'><a href="" title="CsrField::Alias (class)">Alias</a></span></span> <span class='op'>=</span> <span class='const'>Struct</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='symbol'>:field</span><span class='comma'>,</span> <span class='symbol'>:range</span><span class='rparen'>)</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>