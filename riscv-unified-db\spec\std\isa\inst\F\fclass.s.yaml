# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fclass.s
long_name: Single-precision floating-point classify
description: |
  The `fclass.s` instruction examines the value in floating-point register
  _fs1_ and writes to integer register _xd_ a 10-bit mask that indicates
  the class of the floating-point number.
  The format of the mask is described in the table below.
  The corresponding bit in _xd_ will be set if the property is true and
  clear otherwise.
  All other bits in _xd_ are cleared.
  Note that exactly one bit in xd will be set.
  `fclass.s` does not set the floating-point exception flags.

  .Format of result of `fclass` instruction.
  [%autowidth,float="center",align="center",cols="^,<",options="header",]
  |===
  |_xd_ bit |Meaning
  |0 |_fs1_ is latexmath:[$-\infty$].
  |1 |_fs1_ is a negative normal number.
  |2 |_fs1_ is a negative subnormal number.
  |3 |_fs1_ is latexmath:[$-0$].
  |4 |_fs1_ is latexmath:[$+0$].
  |5 |_fs1_ is a positive subnormal number.
  |6 |_fs1_ is a positive normal number.
  |7 |_fs1_ is latexmath:[$+\infty$].
  |8 |_fs1_ is a signaling NaN.
  |9 |_fs1_ is a quiet NaN.
  |===

definedBy: F
assembly: xd, fs1
encoding:
  match: 111000000000-----001-----1010011
  variables:
    - name: fs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
  check_f_ok($encoding);

  Bits<32> sp_value = f[fs1][31:0];

  if (is_sp_neg_inf?(sp_value)) {
    X[xd] = 1 << 0;
  } else if (is_sp_neg_norm?(sp_value)) {
    X[xd] = 1 `<< 1;
  } else if (is_sp_neg_subnorm?(sp_value)) {
    X[xd] = 1 `<< 2;
  } else if (is_sp_neg_zero?(sp_value)) {
    X[xd] = 1 `<< 3;
  } else if (is_sp_pos_zero?(sp_value)) {
    X[xd] = 1 `<< 4;
  } else if (is_sp_pos_subnorm?(sp_value)) {
    X[xd] = 1 `<< 5;
  } else if (is_sp_pos_norm?(sp_value)) {
    X[xd] = 1 `<< 6;
  } else if (is_sp_pos_inf?(sp_value)) {
    X[xd] = 1 `<< 7;
  } else if (is_sp_signaling_nan?(sp_value)) {
    X[xd] = 1 `<< 8;
  } else {
    assert(is_sp_quiet_nan?(sp_value), "Unexpected SP value");
    X[xd] = 1 `<< 9;
  }

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let rs1_val_X            = X(rs1);
    let rd_val_S             = rs1_val_X [31..0];
    F(rd) = nan_box (rd_val_S);
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
