# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.wrap
long_name: Wraparound (Register)
description: |
  If `rs1` >= `rs2` perform subtraction between `rs1` and `rs2`.
  If `rs1` < 0, perform addition between `rs1` and `rs2`,
  else, select `rs1`. The result is stored in `rd`.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcia
base: 32
encoding:
  match: 0010010----------011-----0001011
  variables:
    - name: rs1
      location: 19-15
    - name: rs2
      location: 24-20
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg rs1_value = X[rs1];
  X[rd] = ($signed(rs1_value) >= $signed(X[rs2]))
    ? rs1_value - X[rs2]
    : (($signed(rs1_value) < 0)
       ? (rs1_value + X[rs2])
       : rs1_value);
