# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.clo
long_name: Count leading ones
description: |
  Count the number of ones before the first zero in `rs1`, starting from the MSB
  and progressing to the LSB.
  Accordingly, if the input is ~0, the output is XLEN, and if the most-significant
  bit of the input is a 0, the output is 0.
  output written to the `rd`
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 000010000000-----011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = (xlen() - 1) - $signed(highest_set_bit(~X[rs1]));
