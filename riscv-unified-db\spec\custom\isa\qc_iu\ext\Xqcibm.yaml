# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqcibm
type: unprivileged
long_name: Qualcomm bit manipulation
versions:
- version: "0.1.0"
  state: development
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Add information about instruction formats of each instruction
    - Fix description and functionality of qc.c.extu instruction
  requires: { name: <PERSON><PERSON>, version: ">= 1.0.0" }
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>r
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix description and functionality of qc.c.extu instruction
  requires: { name: Zca, version: ">= 1.0.0" }
- version: "0.4.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix encoding for qc.c.extu
  requires: { name: Zca, version: ">= 1.0.0" }
- version: "0.5.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix qc.cto instruction IDL code
    - Change width calculations for qc.extdpr, qc.extdprh, qc.extdr, qc.extdupr, qc.extduprh, qc.extdur,
    - Change width calculations for qc.insbhr, qc.insbpr, qc.insbprh, qc.insbr, qc.insbri
  requires: { name: Zca, version: ">= 1.0.0" }
- version: "0.6.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix rs1 cannot be 31 for qc.extdu, qc.extd, qc.extdur, instructions
    - Fix rs1 cannot be 31 for qc.extduprh, qc.extdprh, qc.extdupr, qc.extdr qc.extdpr instructions
    - Fix typos in IDL code (missing ')' ) for qc.extdpr, qc.extdr instructions
    - Fix IDL code and description to look correct in PDF for qc.insbhr and qc.insbh instructions
    - Fix IDL code to to match description for qc.insbr instruction
  requires: { name: Zca, version: ">= 1.0.0" }
- version: "0.7.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code sign extension logic for qc.ext and qc.extd instructions
    - Fix IDL code sign extension logic for qc.extdpr, qc.extdprh and qc.extdr instructions
    - Fix IDL code and description increasing shift to 6 bit for qc.extdr and qc.extdur instructions
    - Fix IDL code and description increasing shift to 6 bit for qc.extdpr and qc.extdprh instructions
    - Fix IDL code and description increasing shift to 6 bit for qc.extdupr and qc.extduprh instructions
  requires: { name: Zca, version: ">= 1.0.0" }
- version: "0.8.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code for qc.c.bseti, qc.c.extu, qc.ext and qc.extu instructions because change in IDL '<<' operator
    - Fix IDL code for qc.extd, qc.extdu, qc.extdr and qc.extdur instructions because change in IDL '<<' operator
    - Fix IDL code for qc.extdpr, qc.extdprh, qc.extdupr and qc.extduprh instructions because change in IDL '<<' operator
    - Fix IDL code for qc.insb, qc.insbi, qc.insbr and qc.insbri instructions because change in IDL '<<' operator
    - Fix IDL code for qc.insbh, qc.insbhr, qc.insbpr and qc.insbprh instructions because change in IDL '<<' operator
  requires: { name: Zca, version: ">= 1.0.0" }
description: |
  The Xqcibm extension includes thirty eight instructions that perform bit manipulation,
  include insertion and extraction.

doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
