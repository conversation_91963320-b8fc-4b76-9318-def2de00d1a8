# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: fcsr
long_name: Floating-point control and status register (`frm` + `fflags`)
address: 0x003
writable: true
description: |
  The floating-point control and status register, `fcsr`, is a RISC-V
  control and status register (CSR). It is a 32-bit read/write register
  that selects the dynamic rounding mode for floating-point arithmetic
  operations and holds the accrued exception flags, as shown in <<fcsr>>.

  [[fcsr, Floating-Point Control and Status Register]]
  .Floating-point control and status register
  include::images/wavedrom/float-csr.adoc[]

  The `fcsr` register can be read and written with the FRCSR and FSCSR
  instructions, which are assembler pseudoinstructions built on the
  underlying CSR access instructions. FRCSR reads `fcsr` by copying it
  into integer register _rd_. FSCSR swaps the value in `fcsr` by copying
  the original value into integer register _rd_, and then writing a new
  value obtained from integer register _rs1_ into `fcsr`.

  The fields within the `fcsr` can also be accessed individually through
  different CSR addresses, and separate assembler pseudoinstructions are defined
  for these accesses. The FRRM instruction reads the Rounding Mode field `frm`
  (`fcsr` bits 7--5) and copies it into the least-significant three bits of
  integer register _rd_, with zero in all other bits. FSRM swaps the value in
  `frm` by copying the original value into integer register _rd_, and then
  writing a new value obtained from the three least-significant bits of integer
  register _rs1_ into `frm`. FRFLAGS and FSFLAGS are defined analogously for the
  Accrued Exception Flags field `fflags` (`fcsr` bits 4--0).

  Bits 31--8 of the `fcsr` are reserved for other standard extensions. If
  these extensions are not present, implementations shall ignore writes to
  these bits and supply a zero value when read. Standard software should
  preserve the contents of these bits.

  Floating-point operations use either a static rounding mode encoded in
  the instruction, or a dynamic rounding mode held in `frm`. Rounding
  modes are encoded as shown in <<rm>>. A value of 111 in the
  instruction's _rm_ field selects the dynamic rounding mode held in
  `frm`. The behavior of floating-point instructions that depend on
  rounding mode when executed with a reserved rounding mode is _reserved_, including both static reserved rounding modes (101-110) and dynamic reserved rounding modes (101-111). Some instructions, including widening conversions, have the _rm_ field but are nevertheless mathematically unaffected by the rounding mode; software should set their _rm_ field to
  RNE (000) but implementations must treat the _rm_ field as usual (in
  particular, with regard to decoding legal vs. reserved encodings).

  [NOTE]
  ====
  The C99 language standard effectively mandates the provision of a
  dynamic rounding mode register. In typical implementations, writes to
  the dynamic rounding mode CSR state will serialize the pipeline. Static
  rounding modes are used to implement specialized arithmetic operations
  that often have to switch frequently between different rounding modes.

  The ratified version of the F spec mandated that an illegal-instruction
  exception was raised when an instruction was executed with a reserved
  dynamic rounding mode. This has been weakened to reserved, which matches
  the behavior of static rounding-mode instructions. Raising an
  illegal-instruction exception is still valid behavior when encountering a
  reserved encoding, so implementations compatible with the ratified spec
  are compatible with the weakened spec.
  ====

  The accrued exception flags indicate the exception conditions that have
  arisen on any floating-point arithmetic instruction since the field was
  last reset by software, as shown in <<bitdef>>. The base
  RISC-V ISA does not support generating a trap on the setting of a
  floating-point exception flag.
  (((floating-point, exception flag)))

  [[bitdef]]
  .Accrued exception flag encoding.
  [%autowidth,float="center",align="center",cols="^,<",options="header",]
  |===
  |Flag Mnemonic |Flag Meaning
  |NV |Invalid Operation
  |DZ |Divide by Zero
  |OF |Overflow
  |UF |Underflow
  |NX |Inexact
  |===

  [NOTE]
  ====
  As allowed by the standard, we do not support traps on floating-point
  exceptions in the F extension, but instead require explicit checks of
  the flags in software. We considered adding branches controlled directly
  by the contents of the floating-point accrued exception flags, but
  ultimately chose to omit these instructions to keep the ISA simple.
  ====

priv_mode: U
length: 32
definedBy: F
fields:
  FRM:
    location: 7-5
    description: |
      Rounding modes are encoded as follows:

      [[rm]]
      .Rounding mode encoding.
      [%autowidth,float="center",align="center",cols="^,^,<",options="header"]
      !===
      !Rounding Mode |Mnemonic |Meaning
      !000 !RNE !Round to Nearest, ties to Even
      !001 !RTZ !Round towards Zero
      !010 !RDN !Round Down (towards latexmath:[$-\infty$])
      !011 !RUP !Round Up (towards latexmath:[$+\infty$])
      !100 !RMM !Round to Nearest, ties to Max Magnitude
      !101 ! !_Reserved for future use._
      !110 ! !_Reserved for future use._
      !111 !DYN !In instruction's _rm_ field, selects dynamic rounding mode; In Rounding Mode register, _reserved_.
      !===

      A value of 111 in the
      instruction's _rm_ field selects the dynamic rounding mode held in
      `frm`. The behavior of floating-point instructions that depend on
      rounding mode when executed with a reserved rounding mode is _reserved_,
      including both static reserved rounding modes (101-110) and dynamic reserved
      rounding modes (101-111). Some instructions, including widening conversions,
      have the _rm_ field but are nevertheless mathematically unaffected by the
      rounding mode; software should set their _rm_ field to
      RNE (000) but implementations must treat the _rm_ field as usual (in
      particular, with regard to decoding legal vs. reserved encodings).
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  NV:
    location: 4
    description: |
      *Invalid Operation*

      Cumulative error flag for floating point operations.

      Set by hardware when a floating point operation is invalid and stays set until explicitly
      cleared by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  DZ:
    location: 3
    description: |
      *Divide by zero*

      Cumulative error flag for floating point operations.

      Set by hardware when a floating point divide attempts to divide by zero and stays set until explicitly
      cleared by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  OF:
    location: 2
    description: |
      *Overflow*

      Cumulative error flag for floating point operations.

      Set by hardware when a floating point operation overflows and stays set until explicitly
      cleared by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  UF:
    location: 1
    description: |
      *Underflow*

      Cumulative error flag for floating point operations.

      Set by hardware when a floating point operation underflows and stays set until explicitly
      cleared by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  NX:
    location: 0
    description: |
      *Inexact*

      Cumulative error flag for floating point operations.

      Set by hardware when a floating point operation is inexact and stays set until explicitly
      cleared by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
