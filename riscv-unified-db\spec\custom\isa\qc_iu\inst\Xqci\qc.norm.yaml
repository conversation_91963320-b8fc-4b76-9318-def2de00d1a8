# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.norm
long_name: Signed Normalization
description: |
  Signed normalization of `rs1`.
  Exponent written in bits[7:0] of the result.
  Mantissa written in bits[31:8] of the result.
  Write result to `rd`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcia
base: 32
encoding:
  match: 000011100000-----011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg clz = (xlen() - 1) - $signed(highest_set_bit(X[rs1]));
  XReg clo = (xlen() - 1) - $signed(highest_set_bit(~X[rs1]));
  XReg mnt = (X[rs1][31] == 1) ? (X[rs1] << (clo - 1)) : (X[rs1] << (clz - 1));
  XReg exp = (X[rs1][31] == 1) ? (-(clo - 1)) : (-(clz - 1));
  X[rd] = {mnt[31:8],exp[7:0]};
