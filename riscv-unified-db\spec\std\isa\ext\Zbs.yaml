# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zbs
long_name: Single-bit instructions
description: |
  The single-bit instructions provide a mechanism to set, clear, invert, or extract a single bit
  in a register. The bit is specified by its index
type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
doc_license:
  name: Creative Commons Attribution 4.0 International License (CC-BY 4.0)
  url: https://creativecommons.org/licenses/by/4.0/
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2021-06
    url: https://drive.google.com/drive/u/0/folders/1_wqb-rXOVkGa6rqmugN3kwCftWDf1daU
    repositories:
      - url: https://github.com/riscv/riscv-bitmanip
        branch: main
    contributors:
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON>-wei <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> Mc<PERSON>rary
      - name: <PERSON> <PERSON>
      - name: <PERSON><PERSON> <PERSON><PERSON>
      - name: <PERSON> N<PERSON>
      - name: <PERSON> O<PERSON><PERSON>er
      - name: <PERSON> <PERSON>
      - name: <PERSON>ls <PERSON><PERSON><PERSON><PERSON>
      - name: <PERSON> <PERSON><PERSON><PERSON>
      - name: <PERSON>e Saw
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>sich
      - name: Avis<PERSON> Tvila
      - name: Andrew Waterman
      - name: Thomas Wicki
      - name: Claire Wolf
