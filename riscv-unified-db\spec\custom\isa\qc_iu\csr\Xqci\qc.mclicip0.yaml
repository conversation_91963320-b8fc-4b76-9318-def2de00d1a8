# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicip0
long_name: IRQ Pending 0
address: 0x7f0
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Pending bits for IRQs 0-31
fields:
  IRQ0:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ0 pending
  IRQ1:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ1 pending
  IRQ2:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ2 pending
  IRQ3:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ3 pending
  IRQ4:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ4 pending
  IRQ5:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ5 pending
  IRQ6:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ6 pending
  IRQ7:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ7 pending
  IRQ8:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ8 pending
  IRQ9:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ9 pending
  IRQ10:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ10 pending
  IRQ11:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ11 pending
  IRQ12:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ12 pending
  IRQ13:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ13 pending
  IRQ14:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ14 pending
  IRQ15:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ15 pending
  IRQ16:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ16 pending
  IRQ17:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ17 pending
  IRQ18:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ18 pending
  IRQ19:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ19 pending
  IRQ20:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ20 pending
  IRQ21:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ21 pending
  IRQ22:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ22 pending
  IRQ23:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ23 pending
  IRQ24:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ24 pending
  IRQ25:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ25 pending
  IRQ26:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ26 pending
  IRQ27:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ27 pending
  IRQ28:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ28 pending
  IRQ29:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ29 pending
  IRQ30:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ30 pending
  IRQ31:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ31 pending
