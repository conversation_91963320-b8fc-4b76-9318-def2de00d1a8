# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.blti
long_name: Branch on less than (immediate)
description: |
  Branches to `PC` + `offset` if the value in `rs1` is less than the immediate.
  Instruction encoded in BI instruction format
definedBy:
  anyOf:
    - Xqci
    - Xqcibi
base: 32
encoding:
  match: -----------------100-----1111011
  variables:
    - name: offset
      location: 31|7|30-25|11-8
      left_shift: 1
      sign_extend: true
    - name: imm
      location: 24-20
      not: 0
    - name: rs1
      location: 19-15
      not: 0
assembly: " xs1, imm, offset"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if ($signed(X[rs1]) < $signed(imm)) {
    jump_halfword($pc + $signed(offset));
  }
