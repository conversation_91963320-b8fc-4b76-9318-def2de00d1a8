# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl17
long_name: IRQ Level 17
address: 0xbd1
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 136-143
fields:
  IRQ136:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ136 level
  IRQ137:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ137 level
  IRQ138:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ138 level
  IRQ139:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ139 level
  IRQ140:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ140 level
  IRQ141:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ141 level
  IRQ142:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ142 level
  IRQ143:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ143 level
