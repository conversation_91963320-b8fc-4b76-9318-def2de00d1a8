# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zfa
long_name: Extension for Additional Floating-Point Instructions
description: |
  `<PERSON>fa` adds instructions for immediate loads, IEEE 754-2019 minimum and maximum operations,
  round-to-integer operations, and quiet floating-point comparisons.
  For RV32D, the `Zfa` extension also adds instructions to transfer double-precision floating-point
  values to and from integer registers, and for RV64Q, it adds analogous instructions for
  quad-precision floating-point values.
  The `Zfa` extension depends on the `F` extension.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: F
