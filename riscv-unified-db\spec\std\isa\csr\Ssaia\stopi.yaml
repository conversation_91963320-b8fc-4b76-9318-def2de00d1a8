# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: stopi
long_name: Supervisor Top Interrupt
address: 0x15B
writable: true
priv_mode: S
length: SXLEN
definedBy: Ssaia
description: |
  The `stopi` register provides information about the highest-priority
  interrupt (both local and external) that is currently enabled and pending
  for S-mode.
  
  This register extends beyond external interrupts to include all interrupt
  sources, providing a unified view of the highest-priority pending interrupt.
  
  A read of stopi returns zero if no interrupt is both pending and enabled.
  Otherwise, the value returned encodes the identity and priority of the
  highest-priority pending and enabled interrupt.
fields:
  INTERRUPT_IDENTITY:
    location: 26-16
    long_name: Interrupt Identity
    description: |
      This field contains the identity of the highest-priority pending and
      enabled interrupt. A value of 0 indicates no interrupt is pending.

      The encoding includes both local interrupts (software, timer) and
      external interrupts from the IMSIC.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Ssaia
    sw_write(csr_value): |
      if (csr_value.INTERRUPT_IDENTITY != 0) {
        clear_imsic_interrupt(PrivilegeMode::S, csr_value.INTERRUPT_IDENTITY);
      }
      return csr_value.INTERRUPT_IDENTITY;
  PRIORITY:
    location: 7-0
    long_name: Interrupt Priority
    description: |
      This field contains the priority level of the highest-priority pending
      and enabled interrupt. Higher numerical values indicate higher priority.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Ssaia
