# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: menvcfg
address: 0x30A
writable: true
long_name: Machine Environment Configuration
description: |
  Contains fields that control certain characteristics of the execution environment
  for modes less privileged than M-mode.

  The `menvcfg` CSR controls
  certain characteristics of the execution environment for modes less
  privileged than M.

  If bit FIOM (Fence of I/O implies Memory) is set to one in `menvcfg`,
  FENCE instructions executed in modes less privileged than M are modified
  so the requirement to order accesses to device I/O implies also the
  requirement to order main memory accesses. <<menvcfg-FIOM>>
  details the modified interpretation of FENCE instruction bits PI, PO,
  SI, and SO for modes less privileged than M when FIOM=1.

  Similarly, for modes less privileged than M when FIOM=1, if an atomic
  instruction that accesses a region ordered as device I/O has its _aq_
  and/or _rl_ bit set, then that instruction is ordered as though it
  accesses both device I/O and memory.

  If S-mode is not supported, or if `satp`.MODE is read-only zero (always
  Bare), the implementation may make FIOM read-only zero.

  [[menvcfg-FIOM]]
  .Modified interpretation of FENCE predecessor and successor sets for modes less privileged than M when FIOM=1.
  [separator="!",%autowidth,float="center",align="center",cols="^,<",options="header"]
  !===
  !Instruction bit |Meaning when set
  !PI +
  PO
  !Predecessor device input and memory reads (PR implied) +
  Predecessor device output and memory writes (PW implied)
  !SI +
  SO
  !Successor device input and memory reads (SR implied) +
  Successor device output and memory writes (SW implied)
  !===

  [NOTE]
  ====
  Bit FIOM is needed in `menvcfg` so M-mode can emulate the `H` (hypervisor)
  extension, which has an
  equivalent FIOM bit in the hypervisor CSR `henvcfg`.
  ====

  The PBMTE bit controls whether the Svpbmt extension is available for use
  in S-mode and G-stage address translation (i.e., for page tables pointed
  to by `satp` or `hgatp`). When PBMTE=1, Svpbmt is available for S-mode
  and G-stage address translation. When PBMTE=0, the implementation
  behaves as though Svpbmt were not implemented. If Svpbmt is not
  implemented, PBMTE is read-only zero. Furthermore, for implementations
  with the hypervisor extension, `henvcfg`.PBMTE is read-only zero if
  `menvcfg`.PBMTE is zero.

  After changing `menvcfg`.PBMTE, executing an SFENCE.VMA instruction with
  _rs1_=`x0` and _rs2_=`x0` suffices to synchronize address-translation caches
  with respect to the altered interpretation of page-table entries' PBMT fields.
  See <<hyp-mm-fences>> for additional synchronization requirements when the
  hypervisor extension is implemented.

  If the Svadu extension is implemented, the ADUE bit controls whether hardware
  updating of PTE A/D bits is enabled for S-mode and G-stage address
  translations.
  When ADUE=1, hardware updating of PTE A/D bits is enabled during S-mode
  address translation, and the implementation behaves as though the Svade
  extension were not implemented for S-mode address translation.
  When the hypervisor extension is implemented, if ADUE=1, hardware updating of
  PTE A/D bits is enabled during G-stage address translation, and the
  implementation behaves as though the Svade extension were not implemented for
  G-stage address translation.
  When ADUE=0, the implementation behaves as though Svade were implemented for
  S-mode and G-stage address translation.
  If Svadu is not implemented, ADUE is read-only zero.
  Furthermore, for implementations with the hypervisor extension, `henvcfg`.ADUE
  is read-only zero if `menvcfg`.ADUE is zero.

  NOTE: The Svade extension requires page-fault exceptions be raised when PTE
  A/D bits need be set, hence Svade is implemented when ADUE=0.

  If the Smcdeleg extension is implemented, the CDE (Counter Delegation Enable) bit controls whether Zicntr and Zihpm counters can be delegated to S-mode. When CDE=1, the Smcdeleg extension is enabled, see <<smcdeleg>>. When CDE=0, the Smcdeleg and Ssccfg extensions appear to be not implemented. If Smcdeleg is not implemented, CDE is read-only zero.

  The definition of the STCE field is furnished by the Sstc extension.

  The definition of the CBZE field is furnished by the Zicboz extension.

  The definitions of the CBCFE and CBIE fields are furnished by the Zicbom extension.

  The definition of the PMM field will be furnished by the forthcoming
  Smnpm extension. Its allocation within `menvcfg` may change prior to the
  ratification of that extension.

  The Zicfilp extension adds the `LPE` field in `menvcfg`. When the `LPE` field is
  set to 1 and S-mode is implemented, the Zicfilp extension is enabled in S-mode.
  If `LPE` field is set to 1 and S-mode is not implemented, the Zicfilp extension
  is enabled in U-mode. When the `LPE` field is 0, the Zicfilp extension is not
  enabled in S-mode, and the following rules apply to S-mode. If the `LPE` field
  is 0 and S-mode is not implemented, then the same rules apply to U-mode.

  * The hart does not update the `ELP` state; it remains as `NO_LP_EXPECTED`.
  * The `LPAD` instruction operates as a no-op.

  The Zicfiss extension adds the `SSE` field to `menvcfg`. When the `SSE` field is
  set to 1 the Zicfiss extension is activated in S-mode. When `SSE` field is 0,
  the following rules apply to privilege modes that are less than M:

  * 32-bit Zicfiss instructions will revert to their behavior as defined by Zimop.
  * 16-bit Zicfiss instructions will revert to their behavior as defined by Zcmop.
  * The `pte.xwr=010b` encoding in VS/S-stage page tables becomes reserved.
  * The `henvcfg.SSE` and `senvcfg.SSE` fields will read as zero and are read-only.
  * `SSAMOSWAP.W/D` raises an illegal-instruction exception.

  The Ssdbltrp extension adds the double-trap-enable (`DTE`) field in `menvcfg`.
  When `menvcfg.DTE` is zero, the implementation behaves as though Ssdbltrp is not
  implemented. When Ssdbltrp is not implemented `sstatus.SDT`, `vsstatus.SDT`, and
  `henvcfg.DTE` bits are read-only zero.

  When XLEN=32, `menvcfgh` is a 32-bit read/write register
  that aliases bits 63:32 of `menvcfg`.
  The `menvcfgh` register does not exist when XLEN=64.

  If U-mode is not supported, then registers `menvcfg` and `menvcfgh` do
  not exist.
priv_mode: M
length: 64
definedBy:
  allOf:
    - name: Sm
      version: ">=1.12"
    - name: U
fields:
  STCE:
    location: 63
    description: |
      *STimecmp Enable*

      When set, `stimecmp` is operational.

      When clear, `stimecmp` access in a mode other than M-mode raises an `Illegal Instruction` trap.
      S-mode timer interrupts will not be generated when clear, and `mip` and `sip` revert to their prior behavior without `Sstc`.
    definedBy: Sstc
    type: RW
    reset_value: UNDEFINED_LEGAL
  PBMTE:
    location: 62
    description: |
      *Page Based Memory Type Enable*

      The PBMTE bit controls whether the Svpbmt extension is available for use in S-mode<% if ext?(:H) %>and G-stage<% end %>
      address translation (i.e., for page tables pointed to by satp<% if ext?(:H) %> or hgatp<% end %>). When PBMTE=1, Svpbmt is
      available for S-mode <% if ext?(:H) %> and G-stage <% end %> address translation. When PBMTE=0, the implementation behaves
      as though Svpbmt were not implemented. If Svpbmt is not implemented, PBMTE is read-only zero.

      <% if ext?(:H) %>
      Furthermore, henvcfg.PBMTE is read-only zero if
      menvcfg.PBMTE is zero.
      <% end %>

      After changing `menvcfg.PBMTE`, executing an `sfence.vma` instruction with _rs1_=_x0_ and
      _rs2_=_x0_ suffices to synchronize address-translation caches with respect to the altered
      interpretation of page-table entries' PBMT fields.

    definedBy: Svpbmt
    type: RW
    reset_value: UNDEFINED_LEGAL
  ADUE:
    location: 61
    description: |
      If the Svadu extension is implemented, the ADUE bit controls whether hardware updating of
      PTE A/D bits is enabled for S-mode and G-stage address translations. When ADUE=1, hardware
      updating of PTE A/D bits is enabled during S-mode address translation, and the
      implementation behaves as though the Svade extension were not implemented for S-mode address
      translation.

      When the hypervisor extension is implemented, if ADUE=1, hardware updating of PTE A/D bits
      is enabled during G-stage address translation, and the implementation behaves as though the
      Svade extension were not implemented for G-stage address translation. When ADUE=0, the
      implementation behaves as though Svade were implemented for S-mode and
      G-stage address translation.

      If Svadu is not implemented, ADUE is read-only zero.

      Furthermore, for implementations with the hypervisor extension, henvcfg.ADUE is read-only
      zero if menvcfg.ADUE is zero.
    definedBy: Svadu
    type(): |
      return (implemented?(ExtensionName::Svadu)) ? CsrFieldType::RO : CsrFieldType::RW;
    reset_value(): |
      return (implemented?(ExtensionName::Svadu)) ? UNDEFINED_LEGAL : 0;
  CBZE:
    location: 7
    description: |
      *Cache Block Zero instruction Enable*

      Enables the execution of the cache block zero instruction, `CBO.ZERO`,
      <% if ext?(:S) %>
      in S-mode
      <% elsif ext?(:U) %>
      in U-mode
      <% end %>.

        * `0`: The instruction raises an illegal instruction or virtual instruction exception
        * `1`: The instruction is executed

    definedBy: Zicboz
    type: RW
    reset_value: UNDEFINED_LEGAL
  CBCFE:
    location: 6
    description: |
      *Cache Block Clean and Flush instruction Enable*

      Enables the execution of the cache block clean instruction, `CBO.CLEAN`, and the
      cache block flush instruction, `CBO.FLUSH`,
      <% if ext?(:S) %>
      in S-mode
      <% elsif ext?(:U) %>
      in U-mode
      <% end %>.

        * `0`: The instruction raises an illegal instruction or virtual instruction exception
        * `1`: The instruction is executed

    definedBy: Zicbom
    type: RW
    reset_value: UNDEFINED_LEGAL
  CBIE:
    location: 5-4
    description: |
      *Cache Block Invalidate instruction Enable*

      Enables the execution of the cache block invalidate instruction, `CBO.INVAL`,
      <% if ext?(:S) %>
      in S-mode
      <% elsif ext?(:U) %>
      in U-mode
      <% end %>.

        * `00`: The instruction raises an illegal instruction or virtual instruction exception
        * `01`: The instruction is executed and performs a flush operation
        * `10`: _Reserved_
        * `11`: The instruction is executed and performs an invalidate operation
    definedBy: Zicbom
    type: RW-R
    sw_write(csr_value): |
      if ((csr_value.CBIE == 0) ||
          (csr_value.CBIE == 1) ||
          ((!FORCE_UPGRADE_CBO_INVAL_TO_FLUSH) && (csr_value.CBIE == 3))) {
        return csr_value.CBIE;
      } else {
        return CSR[menvcfg].CBIE;
      }
    reset_value: UNDEFINED_LEGAL
  FIOM:
    location: 0
    description: |
      *Fence of I/O implies Memory*

      When `menvcfg.FIOM` is set,
      FENCE instructions ordering I/O regions also implicitly order memory regions when executed
      in any mode less privileged than M-mode.

      [separator="!",%autowidth,float="center",align="center",cols="^,<",options="header"]
      !===
      !Instruction bit !Meaning when set
      !PI +
      PO
      !Predecessor device input and memory reads (PR implied) +
      Predecessor device output and memory writes (PW implied)
      !SI +
      SO
      !Successor device input and memory reads (SR implied) +
      Successor device output and memory writes (SW implied)
      !===

      Similarly, for modes less privileged than M when FIOM=1, if an atomic
      instruction that accesses a region ordered as device I/O has its _aq_
      and/or _rl_ bit set, then that instruction is ordered as though it
      accesses both device I/O and memory.

    type: RW
    reset_value: UNDEFINED_LEGAL
