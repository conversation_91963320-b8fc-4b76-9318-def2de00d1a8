# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

<%- raise("'hpm_num' must be defined") if hpm_num.nil? -%>

$schema: csr_schema.json#
kind: csr
name: <%= "mhpmcounter#{hpm_num}" %>
long_name: Machine Hardware Performance Counter <%= hpm_num %>
address: <%= "0x" + (0xB00 + hpm_num).to_s(16).upcase %>
priv_mode: M
length: 64
description: Programmable hardware performance counter.
definedBy: Smhpm
fields:
  COUNT:
    location: 63-0
    description: |
      [when="HPM_COUNTER_EN[<%= hpm_num %>] == true"]
      --
      Performance counter for event selected in `mhpmevent<%= hpm_num %>.EVENT`.

      Increments every time event occurs unless:

        * `mcountinhibit.HPM<%= hpm_num %>` <%%- if ext?(:Smcdeleg) -%>or its alias `scountinhibit.HPM<%= hpm_num %>`<%%- end -%> is set
        <%%- if ext?(:Sscofpmf) -%>
        * `mhpmevent<%= hpm_num %>.MINH` is set and the current privilege level is M
        <%%- if ext?(:S) -%>
        * `mhpmevent<%= hpm_num %>.SINH` <%%- if ext?(:Ssccfg) -%>or its alias `hpmevent<%= hpm_num %>..SINH`<%%- end -%> is set and the current privilege level is (H)S
        <%%- end -%>
        <%%- if ext?(:U) -%>
        * `mhpmevent<%= hpm_num %>.UINH` <%%- if ext?(:Ssccfg) -%>or its alias `hpmevent<%= hpm_num %>.SINH`<%%- end -%> is set and the current privilege level is U
        <%%- end -%>
        <%%- if ext?(:H) -%>
        * `mhpmevent<%= hpm_num %>.VSINH` <%%- if ext?(:Ssccfg) -%>or its alias `hpmevent<%= hpm_num %>.SINH`<%%- end -%> is set and the current privilege level is VS
        * `mhpmevent<%= hpm_num %>.VUINH` <%%- if ext?(:Ssccfg) -%>or its alias `hpmevent<%= hpm_num %>.SINH`<%%- end -%> is set and the current privilege level is VU
        <%%- end -%>
        <%%- end -%>
      --

      [when="HPM_COUNTER_EN[<%= hpm_num %>] == false"]
      Unimplemented performance counter. Must be read-only 0 (access does not cause trap).

    type(): "return (HPM_COUNTER_EN[<%= hpm_num %>]) ? CsrFieldType::RWH : CsrFieldType::RO;"
    reset_value(): "return (HPM_COUNTER_EN[<%= hpm_num %>]) ? UNDEFINED_LEGAL : 0;"
sw_read(): |
  if (HPM_COUNTER_EN[<%= hpm_num %>]) {
    return read_hpm_counter(<%= hpm_num %>);
  } else {
    return 0;
  }
