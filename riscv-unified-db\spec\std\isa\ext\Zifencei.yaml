# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zifencei
long_name: Instruction fence
description: |
  This chapter defines the "Zifencei" extension, which includes the
  FENCE.I instruction that provides explicit synchronization between
  writes to instruction memory and instruction fetches on the same hart.
  Currently, this instruction is the only standard mechanism to ensure
  that stores visible to a hart will also be visible to its instruction
  fetches.
  (((store instruction word, not included)))

  [NOTE]
  ====
  We considered but did not include a "store instruction word"
  instruction as in cite:[majc]. JIT compilers may generate a large trace of
  instructions before a single FENCE.I, and amortize any instruction cache
  snooping/invalidation overhead by writing translated instructions to
  memory regions that are known not to reside in the I-cache.
  ====
  '''
  [NOTE]
  ====
  The FENCE.I instruction was designed to support a wide variety of
  implementations. A simple implementation can flush the local instruction
  cache and the instruction pipeline when the FENCE.I is executed. A more
  complex implementation might snoop the instruction (data) cache on every
  data (instruction) cache miss, or use an inclusive unified private L2
  cache to invalidate lines from the primary instruction cache when they
  are being written by a local store instruction. If instruction and data
  caches are kept coherent in this way, or if the memory system consists
  of only uncached RAMs, then just the fetch pipeline needs to be flushed
  at a FENCE.I.

  The FENCE.I instruction was previously part of the base I instruction
  set. Two main issues are driving moving this out of the mandatory base,
  although at time of writing it is still the only standard method for
  maintaining instruction-fetch coherence.

  First, it has been recognized that on some systems, FENCE.I will be
  expensive to implement and alternate mechanisms are being discussed in
  the memory model task group. In particular, for designs that have an
  incoherent instruction cache and an incoherent data cache, or where the
  instruction cache refill does not snoop a coherent data cache, both
  caches must be completely flushed when a FENCE.I instruction is
  encountered. This problem is exacerbated when there are multiple levels
  of I and D cache in front of a unified cache or outer memory system.

  Second, the instruction is not powerful enough to make available at user
  level in a Unix-like operating system environment. The FENCE.I only
  synchronizes the local hart, and the OS can reschedule the user hart to
  a different physical hart after the FENCE.I. This would require the OS
  to execute an additional FENCE.I as part of every context migration. For
  this reason, the standard Linux ABI has removed FENCE.I from user-level
  and now requires a system call to maintain instruction-fetch coherence,
  which allows the OS to minimize the number of FENCE.I executions
  required on current systems and provides forward-compatibility with
  future improved instruction-fetch coherence mechanisms.

  Future approaches to instruction-fetch coherence under discussion
  include providing more restricted versions of FENCE.I that only target a
  given address specified in _rs1_, and/or allowing software to use an ABI
  that relies on machine-mode cache-maintenance operations.
  ====
type: unprivileged
versions:
  - version: "2.0.0"
    state: ratified
    ratification_date: null
