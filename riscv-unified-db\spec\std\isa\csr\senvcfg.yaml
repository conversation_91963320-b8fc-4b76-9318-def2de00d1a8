# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: senvcfg
address: 0x10A
writable: true
long_name: Supervisor Environment Configuration
description: |
  Contains fields that control certain characteristics of the U-mode execution environment.
priv_mode: S
length: 64
definedBy:
  allOf:
    - name: S
      version: ">=1.12"
    - name: U
fields:
  CBZE:
    location: 7
    description: |
      *Cache Block Zero instruction Enable*

      Bit is read-only 0 when `menvcfg.CBZE` is clear.

      Enables the execution of the cache block zero instruction, `cbo.zero`,
      in U-mode<% if ext?(:H) %> and (in conjunction with `henvcfg.CBZE`) VU-mode<% end %>.

        * `0`: The instruction raises an illegal instruction<% if ext?(:H) %> or virtual instruction exception<% end %>
        * `1`: The instruction is executed

      To summarize access:
      [separator="!",%autowidth]
      !===
      ! `menvcfg.CBZE` ! `senvcfg.CBZE` behavior

      ! 0 ! read-only 0
      ! 1
      a! writable, independent bit from `menvcfg.CBZE`
      !===

      See `cbo.zero` for a summary of the effect.

    definedBy: Zicboz
    type: RW
    reset_value: UNDEFINED_LEGAL
  CBCFE:
    location: 6
    description: |
      *Cache Block Clean and Flush instruction Enable*

      Enables the execution of the cache block clean instruction, `cbo.clean`, and the
      cache block flush instruction, `cbo.flush`,
      <% if ext?(:S) %>
      in S-mode
      <% elsif ext?(:U) %>
      in U-mode
      <% end %>.

        * `0`: The instruction raises an illegal instruction <% if ext?(:H) %>or virtual instruction exception<% end %>
        * `1`: The instruction is executed

      To summarize access:
      [separator="!",%autowidth]
      !===
      ! `menvcfg.CBCFE` ! `senvcfg.CBCFE` behavior

      ! 0 ! read-only 0
      ! 1
      a! writable, independent bit from `menvcfg.CBCFE`
      !===

      See `cbo.clean` and/or `cbo.flush` for a summary of the effect.

    definedBy: Zicbom
    type: RW
    reset_value: UNDEFINED_LEGAL
  CBIE:
    location: 5-4
    description: |
      *Cache Block Invalidate instruction Enable*

      This field has restricted values based on the value of `menvcfg.CBIE`.
      When an invalid value is written, it is ignored and the field remains unchanged.

      [separator="!",%autowidth,cols=",.>"]
      !===
      ! [.rotate]#`menvcfg.CBIE`# ! Valid values of `senvcfg.CBIE`

      ! 00 ! 00
      ! 01 ! 00, 01
      ! 11 ! 00, 01, 11
      !===

      Controls execution of the cache block invalidate instruction, `cbo.inval`,
      in U-mode
      <% if ext?(:H) %>
      and VU-mode (together with `henvcfg.CBIE`)
      <% end %>
      .

        * `00`: The instruction raises an illegal instruction or virtual instruction exception
        * `01`: The instruction is executed and performs a flush operation
        * `10`: _Reserved_
        * `11`: The instruction is executed and performs an invalidate operation

      See `cbo.inval` for more details.
    definedBy: Zicbom
    type: RW-R
    sw_write(csr_value): |
      if (csr_value.CBIE == 0 || csr_value.CBIE == 1 || csr_value.CBIE == 3) {
        return csr_value.CBIE;
      } else {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      }
    reset_value: UNDEFINED_LEGAL
  FIOM:
    location: 0
    description: |
      *Fence of I/O implies Memory*

      When either `senvcfg.FIOM` or `menvcfg.FIOM` is set,
      FENCE instructions ordering I/O regions also implicitly order memory regions when executed
      in U-mode as follows:

      [separator="!",%autowidth,float="center",align="center",cols="^,<",options="header"]
      !===
      !Instruction bit !Meaning when set
      !PI +
      PO
      !Predecessor device input and memory reads (PR implied) +
      Predecessor device output and memory writes (PW implied)
      !SI +
      SO
      !Successor device input and memory reads (SR implied) +
      Successor device output and memory writes (SW implied)
      !===

      Similarly, in U-mode when FIOM=1, if an atomic
      instruction that accesses a region ordered as device I/O has its _aq_
      and/or _rl_ bit set, then that instruction is ordered as though it
      accesses both device I/O and memory.

      See `fence` for more details.

    type: RW
    reset_value: 0
