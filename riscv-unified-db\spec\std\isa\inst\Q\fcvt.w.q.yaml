# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fcvt.w.q
long_name: Floating-point Convert Quad-precision to Word
description:
  - id: inst-fcvt.w.q-behaviour
    normative: false
    text: |
      `fcvt.w.q` converts a quad-precision floating-point number to a 32-bit signed integer.
definedBy: Q
assembly: xd, fs1, rm
encoding:
  match: 110001100000-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
