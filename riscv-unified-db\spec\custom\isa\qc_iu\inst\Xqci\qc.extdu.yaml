# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.extdu
long_name: Extract bits from pair unsigned (Immediate)
description: |
  Extract a subset of bits from the register pair [`rs1`, `rs1`+1] into `rd`.
  The width of the subset is determined by (`width_minus1` + 1) (1..32),
  and the offset (into the pair) of the subset is determined by `shamt`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 10---------------010-----0001011
  variables:
    - name: width_minus1
      location: 29-25
    - name: shamt
      location: 24-20
    - name: rs1
      location: 19-15
      not: 31
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, width, shamt"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  Bits<{1'b0, MXLEN}*2> pair = {X[rs1 + 1], X[rs1]};
  XReg width = width_minus1 + 1;
  X[rd] = (pair >> shamt) & ((32'b1 << width) - 1);
