* xref:ROOT:config.adoc[Configuration]

.Extensions
<%- cfg_arch.transitive_implemented_extension_versions.sort { |a, b| a.name <=> b.name }.each do |ext| -%>
* <%= link_to_udb_doc_ext(ext.name) %>
<%- end -%>

.Control and Status Registers
<%- cfg_arch.transitive_implemented_csrs.sort { |a, b| a.name <=> b.name }.each do |csr| -%>
* <%= link_to_udb_doc_csr(csr.name) %>
<%- end -%>

.Instructions
<%- cfg_arch.transitive_implemented_instructions.sort { |a, b| a.name <=> b.name }.each do |inst| -%>
* <%= link_to_udb_doc_inst(inst.name) %>
<%- end -%>

.IDL functions
* xref:funcs:funcs.adoc[Global function definitions]

.Appendix
* xref:prose:idl.adoc[IDL guide]
