# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.pexit
long_name: Exit call with register argument pseudo-instruction (hint) working only in simulation environment
description: |
  The Exit call instruction calls simulation environment with unsigned `rs1` explicit argument.
  Simulation environment is expected to complete its execution and return to the system with exit code provided in `rs1`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: " xs1"
base: 32
encoding:
  match: 101100000000-----010000000010011
  variables:
    - name: rs1
      location: 19-15
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 12;
  XReg arg = X[rs1];
  iss_syscall(func,arg);
