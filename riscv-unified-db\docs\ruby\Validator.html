<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Validator
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Validator";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (V)</a> &raquo;
    
    
    <span class="title">Validator</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Validator
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Validator</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd>Singleton</dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/validate.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>class used to validate schmeas and objects</p>


  </div>
</div>
<div class="tags">
  

</div><h2>Defined Under Namespace</h2>
<p class="children">
  
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="Validator/SchemaError.html" title="Validator::SchemaError (class)">SchemaError</a></span>, <span class='object_link'><a href="Validator/ValidationError.html" title="Validator::ValidationError (class)">ValidationError</a></span>
    
  
</p>

  
    <h2>
      Constant Summary
      <small><a href="#" class="constants_summary_toggle">collapse</a></small>
    </h2>

    <dl class="constants">
      
        <dt id="SCHEMA_PATHS-constant" class="">SCHEMA_PATHS =
          <div class="docstring">
  <div class="discussion">
    
<p>map of type to schema filesystem path</p>


  </div>
</div>
<div class="tags">
  

</div>
        </dt>
        <dd><pre class="code"><span class='lbrace'>{</span>
  <span class='label'>config:</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>config_schema.json</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
  <span class='label'>arch:</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch_schema.json</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
  <span class='label'>inst:</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>inst_schema.json</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
  <span class='label'>ext:</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ext_schema.json</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
  <span class='label'>csr:</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>csr_schema.json</span><span class='tstring_end'>&quot;</span></span>
<span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_freeze'>freeze</span></pre></dd>
      
        <dt id="TYPES-constant" class="">TYPES =
          <div class="docstring">
  <div class="discussion">
    
<p>types of objects that can be validated</p>


  </div>
</div>
<div class="tags">
  

</div>
        </dt>
        <dd><pre class="code"><span class='const'><span class='object_link'><a href="#SCHEMA_PATHS-constant" title="Validator::SCHEMA_PATHS (constant)">SCHEMA_PATHS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_keys'>keys</span><span class='period'>.</span><span class='id identifier rubyid_freeze'>freeze</span></pre></dd>
      
    </dl>
  







  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>  &#x21d2; Validator </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>iniailize a new Validator.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#validate-instance_method" title="#validate (instance method)">#<strong>validate</strong>(path, type: nil)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>validate a YAML file.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#validate_str-instance_method" title="#validate_str (instance method)">#<strong>validate_str</strong>(str, type: nil)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>validate a YAML string of a given type.</p>
</div></span>
  
</li>

      
    </ul>
  


  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>  &#x21d2; <tt><span class='object_link'><a href="" title="Validator (class)">Validator</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>iniailize a new Validator</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Validator/SchemaError.html" title="Validator::SchemaError (class)">SchemaError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if a schema is ill-formed</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


89
90
91
92
93
94
95
96
97
98
99
100</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/validate.rb', line 89</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span>
  <span class='ivar'>@schemas</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='const'><span class='object_link'><a href="#SCHEMA_PATHS-constant" title="Validator::SCHEMA_PATHS (constant)">SCHEMA_PATHS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_type'>type</span><span class='comma'>,</span> <span class='id identifier rubyid_path'>path</span><span class='op'>|</span>
    <span class='comment'># resolve refs as a relative path from the schema file
</span>    <span class='id identifier rubyid_ref_resolver'>ref_resolver</span> <span class='op'>=</span> <span class='id identifier rubyid_proc'>proc</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_pattern'>pattern</span><span class='op'>|</span>
      <span class='const'>JSON</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>schemas</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='id identifier rubyid_pattern'>pattern</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>
    <span class='kw'>end</span>

    <span class='ivar'>@schemas</span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='const'>JSONSchemer</span><span class='period'>.</span><span class='id identifier rubyid_schema'>schema</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='comma'>,</span> <span class='label'>regexp_resolver:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ecma</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='label'>ref_resolver:</span> <span class='id identifier rubyid_ref_resolver'>ref_resolver</span><span class='comma'>,</span> <span class='label'>insert_property_defaults:</span> <span class='kw'>true</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'><span class='object_link'><a href="Validator/SchemaError.html" title="Validator::SchemaError (class)">SchemaError</a></span></span><span class='comma'>,</span> <span class='ivar'>@schemas</span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_validate_schema'>validate_schema</span> <span class='kw'>unless</span> <span class='ivar'>@schemas</span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_valid_schema?'>valid_schema?</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="validate-instance_method">
  
    #<strong>validate</strong>(path, type: nil)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>validate a YAML file</p>

<p>The type of the file is infered from its path unless type is provided</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>path</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Path to a YAML document</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>type</span>
      
      
        <span class='type'>(<tt>Symbol</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Type of the object (One of TYPES). If nil, type will be inferred from path</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Validator/ValidationError.html" title="Validator::ValidationError (class)">ValidationError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if the str is not valid against the type schema</p>
</div>
      
    </li>
  
</ul>

  <p class="tag_title">See Also:</p>
  <ul class="see">
    
      <li><span class='object_link'><a href="#TYPES-constant" title="Validator::TYPES (constant)">TYPES</a></span></li>
    
  </ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/validate.rb', line 134</span>

<span class='kw'>def</span> <span class='id identifier rubyid_validate'>validate</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='kw'>case</span> <span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
    <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>%r{</span><span class='tstring_content'>.*cfgs/params\.yaml$</span><span class='regexp_end'>}</span></span>
      <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='symbol'>:config</span>
    <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>%r{</span><span class='tstring_content'>.*arch/arch_def\.yaml$</span><span class='regexp_end'>}</span></span>
      <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='symbol'>:arch</span>
    <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>%r{</span><span class='tstring_content'>.*arch/inst/.*/.*\.yaml$</span><span class='regexp_end'>}</span></span>
      <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='symbol'>:instruction</span>
    <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>%r{</span><span class='tstring_content'>.*arch/ext/.*/.*\.yaml$</span><span class='regexp_end'>}</span></span>
      <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='symbol'>:ext</span>
    <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>%r{</span><span class='tstring_content'>.*arch/csr/.*/.*\.yaml$</span><span class='regexp_end'>}</span></span>
      <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='symbol'>:csr</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Cannot determine type from YAML path &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_path'>path</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='kw'>begin</span>
    <span class='id identifier rubyid_validate_str'>validate_str</span><span class='lparen'>(</span><span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>type:</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'>Psych</span><span class='op'>::</span><span class='const'>SyntaxError</span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>While parsing </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_path'>path</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="validate_str-instance_method">
  
    #<strong>validate_str</strong>(str, type: nil)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>validate a YAML string of a given type</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>str</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A YAML document</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>type</span>
      
      
        <span class='type'>(<tt>Symbol</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Type of the object (One of TYPES)</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Object</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The object represented by str</p>
</div>
      
    </li>
  
</ul>
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Validator/ValidationError.html" title="Validator::ValidationError (class)">ValidationError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if the str is not valid against the type schema</p>
</div>
      
    </li>
  
</ul>

  <p class="tag_title">See Also:</p>
  <ul class="see">
    
      <li><span class='object_link'><a href="#TYPES-constant" title="Validator::TYPES (constant)">TYPES</a></span></li>
    
  </ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/validate.rb', line 109</span>

<span class='kw'>def</span> <span class='id identifier rubyid_validate_str'>validate_str</span><span class='lparen'>(</span><span class='id identifier rubyid_str'>str</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Invalid type </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type'>type</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#TYPES-constant" title="Validator::TYPES (constant)">TYPES</a></span></span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>

  <span class='kw'>begin</span>
    <span class='id identifier rubyid_obj'>obj</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_safe_load'>safe_load</span><span class='lparen'>(</span><span class='id identifier rubyid_str'>str</span><span class='comma'>,</span> <span class='label'>permitted_classes:</span> <span class='lbracket'>[</span><span class='const'>Symbol</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'>Psych</span><span class='op'>::</span><span class='const'>SyntaxError</span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>While parsing: </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_str'>str</span><span class='embexpr_end'>}</span><span class='tstring_content'>\n\n</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span>
  <span class='kw'>end</span>
  <span class='comment'># convert through JSON to handle anything supported in YAML but not JSON
</span>  <span class='comment'># (e.g., integer object keys will be coverted to strings)
</span>  <span class='id identifier rubyid_jsonified_obj'>jsonified_obj</span> <span class='op'>=</span> <span class='const'>JSON</span><span class='period'>.</span><span class='id identifier rubyid_parse'>parse</span><span class='lparen'>(</span><span class='const'>JSON</span><span class='period'>.</span><span class='id identifier rubyid_generate'>generate</span><span class='lparen'>(</span><span class='id identifier rubyid_obj'>obj</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='const'><span class='object_link'><a href="Validator/ValidationError.html" title="Validator::ValidationError (class)">ValidationError</a></span></span><span class='comma'>,</span> <span class='ivar'>@schemas</span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_validate'>validate</span><span class='lparen'>(</span><span class='id identifier rubyid_jsonified_obj'>jsonified_obj</span><span class='rparen'>)</span> <span class='kw'>unless</span> <span class='ivar'>@schemas</span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_valid?'>valid?</span><span class='lparen'>(</span><span class='id identifier rubyid_jsonified_obj'>jsonified_obj</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_jsonified_obj'>jsonified_obj</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>