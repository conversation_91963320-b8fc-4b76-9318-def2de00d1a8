{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Instruction Type Schema", "description": "Schema for instruction type definitions", "type": "object", "properties": {"$schema": {"const": "inst_type_schema.json#"}, "kind": {"const": "instruction_type"}, "name": {"$ref": "schema_defs.json#/$defs/inst_type_name"}, "description": {"type": "string"}, "length": {"description": "Length, in bits, of the encoding", "enum": [16, 32]}, "opcodes": {"type": "object", "additionalProperties": false, "patternProperties": {"^\\$parent_of$": {"$ref": "schema_defs.json#/$defs/ref_url_list"}, "^[a-z][a-zA-Z0-9]*$": {"type": "object", "required": ["location"], "additionalProperties": false, "properties": {"location": {"$ref": "schema_defs.json#/$defs/field_location"}}}}}, "variables": {"type": "object", "additionalProperties": false, "patternProperties": {"^[a-z][a-zA-Z0-9]*$": {"type": "object", "required": ["location"], "additionalProperties": false, "properties": {"location": {"$ref": "schema_defs.json#/$defs/possibly_split_field_location"}, "$parent_of": {"$ref": "schema_defs.json#/$defs/ref_url_list"}}}}}, "$source": {"description": "Path to the source file. Used by downstream tooling; not expected to be found in handwritten files", "type": "string"}}, "required": ["$schema", "kind", "name", "description", "length", "opcodes"], "additionalProperties": false}