# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: hsv.h
long_name: No synopsis available
description: |
  No description available.
definedBy: H
assembly: xs1, xs2
encoding:
  match: 0110011----------100000001110011
  variables:
    - name: xs2
      location: 24-20
    - name: xs1
      location: 19-15
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
