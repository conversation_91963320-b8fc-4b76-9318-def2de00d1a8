# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl02
long_name: IRQ Level 2
address: 0xbc2
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 16-23
fields:
  IRQ16:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ16 level
  IRQ17:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ17 level
  IRQ18:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ18 level
  IRQ19:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ19 level
  IRQ20:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ20 level
  IRQ21:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ21 level
  IRQ22:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ22 level
  IRQ23:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ23 level
