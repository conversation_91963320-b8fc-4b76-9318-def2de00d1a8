<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  File: README
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "README";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index</a> &raquo; 
    <span class="title">File: README</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><div id='filecontents'><div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>The RISC-V Unified Database is intended to hold <strong>all</strong> the information needed to describe RISC-V,
including a list of extensions, instruction specifications, CSR specifications, and documentation prose. The vision is that anything one would need for RISC-V can be generated from the information in this repository.</p>
</div>
<div class="paragraph">
<p>This repository contains:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>A (eventually complete) description of the RISC-V specification in a machine-readable format.</p>
</li>
<li>
<p>A tool to generate multiple views of that spec, including:</p>
<div class="ulist">
<ul>
<li>
<p>A configuration-specific, human-readable documentation webpage</p>
</li>
<li>
<p>[COMING SOON] A configuration-specific Instruction Set Simulator</p>
</li>
<li>
<p>More backends are planned</p>
</li>
</ul>
</div>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_prerequisites">Prerequisites</h2>
<div class="sectionbody">
<div class="paragraph">
<p>The only requirement is the <code>Singularity CE</code> or <code>Apptainer</code> container system. Either one will work (they are forks).</p>
</div>
<div class="paragraph">
<p>If it is not installed, either as your IT admin or:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>For Apptainer, see <a href="https://apptainer.org/docs/admin/main/installation.html">Apptainer Installation</a>.</p>
</li>
<li>
<p>For Singularity CE, see <a href="https://docs.sylabs.io/guides/latest/admin-guide/installation.html">Singularity CE Installation</a>.</p>
</li>
</ul>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
You do <strong>not</strong> need root privileges to download or use the container. However, to modify/build the container,
you will need either root privileges or the ability to run in <code>fakeroot</code> mode. See <a href="https://docs.sylabs.io/guides/4.1/user-guide/fakeroot.html">Singularity Fakeroot Documentation</a> for more information.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_setup">Setup</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Do once:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="code bash"><code class="bash">./bin/setup

# or, if you also want development tools (:development group in Gemfile) installed
# DEVELOPMENT=1 ./bin/setup</code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
If you forget, don&#8217;t worry. Setup will be invoked by any program in bin/ if necessary.
</td>
</tr>
</table>
</div>
<div class="sect2">
<h3 id="_vscode">VSCode</h3>
<div class="paragraph">
<p>If using Visual Studio Code and want to use development tools, you will need to restart the editor
after setup.</p>
</div>
<div class="paragraph">
<p>Helpful extensions are</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="https://marketplace.visualstudio.com/items?itemName=asciidoctor.asciidoctor-vscode">AsciiDoc</a></p>
</li>
<li>
<p><a href="https://marketplace.visualstudio.com/items?itemName=redhat.vscode-yaml">YAML</a></p>
</li>
<li>
<p><a href="https://marketplace.visualstudio.com/items?itemName=castwide.solargraph">Solargraph</a> (for a Ruby Language Server)</p>
</li>
<li>
<p><a href="https://marketplace.visualstudio.com/items?itemName=onnovalkering.vscode-singularity">Singularity</a> (if you plan on working on the container)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The <code>.vscode/settings.json</code> file in the repo will ensure that Solargraph works without any additional
configuration (assuming you&#8217;ve already run ./bin/setup).</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_tasks">Tasks</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Quick start:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="code bash"><code class="bash">./do --tasks                 # list all documented tasks

# examples
./do gen:arch[generic_rv64]  # generate arch spec for the &#39;generic_rv64&#39; config
./do validate                # validate against the schema</code></pre>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_more_info">More info</h2>
<div class="sectionbody">
<div class="ulist">
<ul>
<li>
<p><a href="arch/README.html">Architecture specification format</a></p>
</li>
<li>
<p><a href="arch/README.html">Documentation for the generator tool and IDL</a></p>
<div class="ulist">
<ul>
<li>
<p><a href="https://riscv-software-src.github.io/riscv-unified-db/ruby/index.html">YARD docs for the generator tool and IDL</a></p>
</li>
</ul>
</div>
</li>
</ul>
</div>
</div>
</div></div></div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>