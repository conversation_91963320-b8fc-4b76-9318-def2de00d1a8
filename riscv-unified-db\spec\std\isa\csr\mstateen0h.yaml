# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mstateen0h
long_name: Upper 32 bits of Machine State Enable 0 Register
address: 0x31C
priv_mode: M
length: 32
base: 32
description:
  - id: csr-mstateen0h-purpose
    normative: true
    text: |
      For RV64 harts, the Smstateen extension adds four new 64-bit CSRs at machine level: `mstateen0` (Machine State
      Enable 0), `mstateen1`, `mstateen2`, and `mstateen3`. For RV32, the registers listed above are 32-bit, and for the
      machine-level CSRs there is a corresponding set of high-half CSRs for the upper 32 bits of each register:
      `mstateen0h`, `mstateen1h`, `mstateen2h`, `mstateen3h`.

definedBy: Smstateen
fields:
  SE0:
    long_name: hstateen0, hstateen0h, and sstateen0 access control
    location: 31
    alias: mstateen0.SE0
    sw_write(csr_value): |
      CSR[mstateen0].SE0 = csr_value.SE0;
      return csr_value.SE0;
    description: |
      The SE0 bit in `mstateen0h` controls access to the `hstateen0`, `hstateen0h`, and the `sstateen0` CSRs.
    type: RW
    reset_value: 0
  ENVCFG:
    long_name: henvcfg, henvcfgh, and senvcfg access control
    location: 30
    definedBy:
      name: S
      version: ">= 1.11"
    alias: mstateen0.ENVCFG
    sw_write(csr_value): |
      CSR[mstateen0].ENVCFG = csr_value.ENVCFG;
      return csr_value.ENVCFG;
    description: |
      The ENVCFG bit in `mstateen0h` controls access to the `henvcfg`, `henvcfgh`, and the `senvcfg` CSRs.
    type: RW
    reset_value: 0
  CSRIND:
    long_name: siselect, sireg*, vsiselect, and vsireg* access control
    location: 28
    definedBy: Sscsrind
    alias: mstateen0.CSRIND
    sw_write(csr_value): |
      CSR[mstateen0].CSRIND = csr_value.CSRIND;
      return csr_value.CSRIND;
    description: |
      The CSRIND bit in `mstateen0h` controls access to the `siselect`, `sireg*`, `vsiselect`, and the `vsireg*`
      CSRs provided by the Sscsrind extensions.
    type: RW
    reset_value: 0
  AIA:
    long_name: Ssaia state access control
    location: 27
    definedBy: Ssaia
    alias: mstateen0.AIA
    sw_write(csr_value): |
      CSR[mstateen0].AIA = csr_value.AIA;
      return csr_value.AIA;
    description: |
      The AIA bit in `mstateen0h` controls access to all state introduced by the Ssaia extension and is not
      controlled by either the CSRIND or the IMSIC bits.
    type: RW
    reset_value: 0
  IMSIC:
    long_name: IMSIC state access control
    location: 26
    definedBy: Ssaia
    alias: mstateen0.IMSIC
    sw_write(csr_value): |
      CSR[mstateen0].IMSIC = csr_value.IMSIC;
      return csr_value.IMSIC;
    description: |
      The IMSIC bit in `mstateen0h` controls access to the IMSIC state, including CSRs `stopei` and `vstopei`,
      provided by the Ssaia extension.
    type: RW
    reset_value: 0
  CONTEXT:
    long_name: scontext and hcontext access control
    location: 25
    definedBy: Sdtrig
    alias: mstateen0.CONTEXT
    sw_write(csr_value): |
      CSR[mstateen0].CONTEXT = csr_value.CONTEXT;
      return csr_value.CONTEXT;
    description: |
      The CONTEXT bit in `mstateen0h` controls access to the `scontext` and `hcontext` CSRs provided by the
      Sdtrig extension.
    type: RW
    reset_value: 0
  P1P13:
    long_name: hedelegh access control
    location: 24
    alias: mstateen0.P1P13
    sw_write(csr_value): |
      CSR[mstateen0].P1P13 = csr_value.P1P13;
      return csr_value.P1P13;
    description: |
      The P1P13 bit in `mstateen0h` controls access to the `hedelegh` introduced by Privileged Specification
      Version 1.13.
    type: RW
    reset_value: 0
  SRMCFG:
    long_name: srmcfg access control
    location: 23
    definedBy: Ssqosid
    alias: mstateen0.SRMCFG
    sw_write(csr_value): |
      CSR[mstateen0].SRMCFG = csr_value.SRMCFG;
      return csr_value.SRMCFG;
    description: |
      The SRMCFG bit in `mstateen0h` controls access to the `srmcfg`` CSR introduced by the Ssqosid Chapter 18
      extension.
    type: RW
    reset_value: 0
  CTR:
    long_name: ctr access control
    location: 22
    alias: mstateen0.CTR
    sw_write(csr_value): |
      CSR[mstateen0].CTR = csr_value.CTR;
      return csr_value.CTR;
    description: |
      When Smstateen is implemented, the `mstateen0.CTR` bit controls access to CTR register state from
      privilege modes less privileged than M-mode.
    type: RW
    reset_value: 0
sw_read(): return $bits(CSR[mstateen0])[63:32];
