name: Regression test

on:
  schedule:
    - cron: "30 2 * * *"
  workflow_dispatch:

jobs:
  # check_date from: https://stackoverflow.com/questions/63014786/how-to-schedule-a-github-actions-nightly-build-but-run-it-only-when-there-where
  check_date:
    runs-on: ubuntu-latest
    name: Check latest commit
    outputs:
      should_run: ${{ steps.should_run.outputs.should_run }}
    steps:
      - uses: actions/checkout@v4
      - name: print latest_commit
        run: echo ${{ github.sha }}

      - id: should_run
        continue-on-error: true
        name: check latest commit is less than a day
        if: ${{ github.event_name == 'schedule' }}
        run: test -z "$(git rev-list  --after='24 hours'  ${{ github.sha }})" && echo "name=should_run::false" >> "$GITHUB_OUTPUT"
  nightly:
    needs: check_date
    if: ${{ needs.check_date.outputs.should_run != 'false' }}
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: <PERSON><PERSON>ithub Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Run regression
        run: ./do test:nightly
