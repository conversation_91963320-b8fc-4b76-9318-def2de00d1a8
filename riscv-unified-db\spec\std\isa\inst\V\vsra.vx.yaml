# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: vsra.vx
long_name: No synopsis available
description: |
  No description available.
definedBy: V
assembly: vd, vs2, xs1, vm
encoding:
  match: 101001-----------100-----1010111
  variables:
    - name: vm
      location: 25-25
    - name: vs2
      location: 24-20
    - name: xs1
      location: 19-15
    - name: vd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let SEW      = get_sew();
    let LMUL_pow = get_lmul_pow();
    let num_elem = get_num_elem(LMUL_pow, SEW);

    if illegal_normal(vd, vm) then { handle_illegal(); return RETIRE_FAIL };

    let 'n = num_elem;
    let 'm = SEW;

    let vm_val  : vector('n, dec, bool)     = read_vmask(num_elem, vm, 0b00000);
    let rs1_val : bits('m)                  = get_scalar(rs1, SEW);
    let vs2_val : vector('n, dec, bits('m)) = read_vreg(num_elem, SEW, LMUL_pow, vs2);
    let vd_val  : vector('n, dec, bits('m)) = read_vreg(num_elem, SEW, LMUL_pow, vd);
    result      : vector('n, dec, bits('m)) = undefined;
    mask        : vector('n, dec, bool)     = undefined;

    (result, mask) = init_masked_result(num_elem, SEW, LMUL_pow, vd_val, vm_val);

    foreach (i from 0 to (num_elem - 1)) {
      if mask[i] then {
        result[i] = match funct6 {
          VX_VADD    => vs2_val[i] + rs1_val,
          VX_VSUB    => vs2_val[i] - rs1_val,
          VX_VRSUB   => rs1_val - vs2_val[i],
          VX_VAND    => vs2_val[i] & rs1_val,
          VX_VOR     => vs2_val[i] | rs1_val,
          VX_VXOR    => vs2_val[i] ^ rs1_val,
          VX_VSADDU  => unsigned_saturation('m, zero_extend('m + 1, vs2_val[i]) + zero_extend('m + 1, rs1_val) ),
          VX_VSADD   => signed_saturation('m, sign_extend('m + 1, vs2_val[i]) + sign_extend('m + 1, rs1_val) ),
          VX_VSSUBU  => {
                          if unsigned(vs2_val[i]) < unsigned(rs1_val) then zeros()
                          else unsigned_saturation('m, zero_extend('m + 1, vs2_val[i]) - zero_extend('m + 1, rs1_val) )
                        },
          VX_VSSUB   => signed_saturation('m, sign_extend('m + 1, vs2_val[i]) - sign_extend('m + 1, rs1_val) ),
          VX_VSMUL   => {
                          let result_mul = to_bits('m * 2, signed(vs2_val[i]) * signed(rs1_val));
                          let rounding_incr = get_fixed_rounding_incr(result_mul, 'm - 1);
                          let result_wide = (result_mul >> ('m - 1)) + zero_extend('m * 2, rounding_incr);
                          signed_saturation('m, result_wide['m..0])
                        },
          VX_VSLL    => {
                          let shift_amount = get_shift_amount(rs1_val, SEW);
                          vs2_val[i] << shift_amount
                        },
          VX_VSRL    => {
                          let shift_amount = get_shift_amount(rs1_val, SEW);
                          vs2_val[i] >> shift_amount
                        },
          VX_VSRA    => {
                          let shift_amount = get_shift_amount(rs1_val, SEW);
                          let v_double : bits('m * 2) = sign_extend(vs2_val[i]);
                          slice(v_double >> shift_amount, 0, SEW)
                        },
          VX_VSSRL   => {
                          let shift_amount = get_shift_amount(rs1_val, SEW);
                          let rounding_incr = get_fixed_rounding_incr(vs2_val[i], shift_amount);
                          (vs2_val[i] >> shift_amount) + zero_extend('m, rounding_incr)
                        },
          VX_VSSRA   => {
                          let shift_amount = get_shift_amount(rs1_val, SEW);
                          let rounding_incr = get_fixed_rounding_incr(vs2_val[i], shift_amount);
                          let v_double : bits('m * 2) = sign_extend(vs2_val[i]);
                          slice(v_double >> shift_amount, 0, SEW) + zero_extend('m, rounding_incr)
                        },
          VX_VMINU   => to_bits(SEW, min(unsigned(vs2_val[i]), unsigned(rs1_val))),
          VX_VMIN    => to_bits(SEW, min(signed(vs2_val[i]), signed(rs1_val))),
          VX_VMAXU   => to_bits(SEW, max(unsigned(vs2_val[i]), unsigned(rs1_val))),
          VX_VMAX    => to_bits(SEW, max(signed(vs2_val[i]), signed(rs1_val)))
        }
      }
    };

    write_vreg(num_elem, SEW, LMUL_pow, vd, result);
    vstart = zeros();
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
