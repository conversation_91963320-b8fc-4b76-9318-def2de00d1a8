name: Me<PERSON> Queue Tests
permissions:
  contents: read
  pull-requests: write
on:
  merge_group:
    types: [checks_requested]
  workflow_dispatch:
env:
  SINGULARITY: 1
jobs:
  build-reuse-manifest:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - name: run reuse
        run: ./bin/reuse spdx
  build-udb-api-doc:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate UDB API Docs
        run: ./do gen:udb:api_doc
  build-isa-explorer-csr:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate ISA Explorer CSR
        run: ./do gen:isa_explorer_browser_csr
  build-isa-explorer-ext:
    runs-on: ubuntu-latest
    steps:
      - name: <PERSON><PERSON> Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate ISA Explorer Extension
        run: ./do gen:isa_explorer_browser_ext
  build-isa-explorer-inst:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate ISA Explorer Instructions
        run: ./do gen:isa_explorer_browser_insts
  build-isa-explorer-spreadsheet:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate ISA Explorer Spreadsheet
        run: ./do gen:isa_explorer_browser_spreadsheet
  build-html-isa-manual:
    runs-on: ubuntu-latest
    env:
      MANUAL_NAME: isa
      VERSIONS: all
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate HTML ISA manual
        run: ./do gen:html_manual
  build-html-cfg-isa-manual:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate HTML ISA manual for a config
        run: ./do gen:html[example_rv64_with_overlay]
  build-instruction-appendix:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate instruction appendix
        run: ./do gen:instruction_appendix
  build-rvi20-profile:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate RVI20
        run: ./do gen:profile_release_pdf[RVI20]
  build-rva20-profile:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate RVA20
        run: ./do gen:profile_release_pdf[RVA20]
  build-rva22-profile:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate RVA22
        run: ./do gen:profile_release_pdf[RVA22]
  build-rva23-profile:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate RVA23
        run: ./do gen:profile_release_pdf[RVA23]
  build-rvb23-profile:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate RVB23
        run: ./do gen:profile_release_pdf[RVB23]
  build-ac100-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate AC100 CRD
        run: ./do gen:proc_crd_pdf[AC100]
  build-ac200-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate AC200 CRD
        run: ./do gen:proc_crd_pdf[AC200]
  build-mc100-32-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC100-32 CRD
        run: ./do gen:proc_crd_pdf[MC100-32]
  build-mc100-64-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC100-64 CRD
        run: ./do gen:proc_crd_pdf[MC100-64]
  build-mc200-32-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC200-32 CRD
        run: ./do gen:proc_crd_pdf[MC200-32]
  build-mc200-64-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC200-64 CRD
        run: ./do gen:proc_crd_pdf[MC200-64]
  build-mc300-32-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC300-32 CRD
        run: ./do gen:proc_crd_pdf[MC300-32]
  build-mc300-64-crd:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC300-64 CRD
        run: ./do gen:proc_crd_pdf[MC300-64]
  build-mc100-32-ctp:
    runs-on: ubuntu-latest
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate MC100-32 CTP
        run: ./do gen:proc_ctp_pdf[MC100-32]
