# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
kind: csr
name: vsireg5
long_name: Virtual Supervisor Indirect Register Alias 5
address: 0x256
virtual_address: 0x256
priv_mode: VS
length: VSXLEN
definedBy: Smcsrind
description:
  - id: csr-vsireg5-purpose
    normative: true
    text: |
      The `vsireg5` CSR is one of several alias registers used to indirectly access
      virtual supervisor-level CSRs in VS-mode or VU-mode.

  - id: csr-vsireg5-selection
    normative: true
    text: |
      The register addressed by `vsireg5` is selected by the current value of the `vsiselect` CSR.

  - id: csr-vsireg5-benefits
    normative: false
    text: |
      The alias mechanism allows indirect CSR access, which helps in virtualization and future extensibility.

  - id: csr-vsireg5-access-traps
    normative: true
    text: |
      A virtual instruction exception is raised for attempts from VS-mode or VU-mode
      to directly access `vsiselect` or `vsireg5`, or from VU-mode to access `siselect` or `siregX`.

  - id: csr-vsireg5-unimplemented
    normative: true
    text: |
      The behavior of accesses to `vsireg5` when `vsiselect` holds a value that is
      not implemented at the HS level is UNSPECIFIED.

  - id: csr-vsireg5-recommendation
    normative: false
    text: |
      Implementations are recommended to raise an illegal instruction exception for
      such accesses to unimplemented targets.

  - id: csr-vsireg5-width
    normative: true
    text: |
      The width of `vsireg5` is always the current `XLEN`, not `VSXLEN`.
      For example, if `HSXLEN = 64` and `VSXLEN = 32`, then `vsireg5` is 64 bits wide
      when accessed from HS-mode (RV64), but 32 bits when accessed from VS-mode (RV32).

fields:
  VALUE:
    long_name: Indirectly Selected Register Value
    location_rv32: 31-0
    location_rv64: 63-0
    type: RW
    description:
      - id: csr-vsireg5-value
        normative: true
        text: |
          The data read or written from the register selected by the value in `vsiselect`.
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 5);
      if (!handle.valid) {
        unimplemented_csr($encoding);
      }
      if (!handle.writable) {
        raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
      csr_sw_write(handle, csr_value.VALUE);
      return csr_hw_read(handle);
sw_read(): |
  Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 5);
  if (!handle.valid) {
    unimplemented_csr($encoding);
  }
  return csr_sw_read(handle);
