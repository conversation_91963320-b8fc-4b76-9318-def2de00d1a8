# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fcvt.l.s
long_name: Floating-point Convert Single-precision to Long
description:
  - id: inst-fcvt.l.s-behaviour
    normative: false
    text: |
      `fcvt.l.s` converts a floating-point number in floating-point register `fs1` to a signed
      64-bit integer, in integer register `xd`.
definedBy: F
base: 64
assembly: xd, fs1, rm
encoding:
  match: 110000000010-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    assert(sizeof(xlen) >= 64);
    let rs1_val_LU = X(rs1)[63..0];
    match (select_instr_or_fcsr_rm (rm)) {
      None() => { handle_illegal(); RETIRE_FAIL },
      Some(rm') => {
        let rm_3b = encdec_rounding_mode(rm');
        let (fflags, rd_val_S) = riscv_ui64ToF32 (rm_3b, rs1_val_LU);

        accrue_fflags(fflags);
        F_or_X_S(rd) = rd_val_S;
        RETIRE_SUCCESS
      }
    }
  }

# SPDX-SnippetEnd
