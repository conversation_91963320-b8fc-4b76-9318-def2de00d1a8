# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: vwmul.vx
long_name: No synopsis available
description: |
  No description available.
definedBy: V
assembly: vd, vs2, xs1, vm
encoding:
  match: 111011-----------110-----1010111
  variables:
    - name: vm
      location: 25-25
    - name: vs2
      location: 24-20
    - name: xs1
      location: 19-15
    - name: vd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let SEW      = get_sew();
    let LMUL_pow = get_lmul_pow();
    let num_elem = get_num_elem(LMUL_pow, SEW);
    let SEW_widen      = SEW * 2;
    let LMUL_pow_widen = LMUL_pow + 1;

    if  illegal_variable_width(vd, vm, SEW_widen, LMUL_pow_widen) |
        not(valid_reg_overlap(vs2, vd, LMUL_pow, LMUL_pow_widen))
    then { handle_illegal(); return RETIRE_FAIL };

    let 'n = num_elem;
    let 'm = SEW;
    let 'o = SEW_widen;

    let vm_val  : vector('n, dec, bool)     = read_vmask(num_elem, vm, 0b00000);
    let vd_val  : vector('n, dec, bits('o)) = read_vreg(num_elem, SEW_widen, LMUL_pow_widen, vd);
    let rs1_val : bits('m)                  = get_scalar(rs1, SEW);
    let vs2_val : vector('n, dec, bits('m)) = read_vreg(num_elem, SEW, LMUL_pow, vs2);
    result      : vector('n, dec, bits('o)) = undefined;
    mask        : vector('n, dec, bool)     = undefined;

    (result, mask) = init_masked_result(num_elem, SEW_widen, LMUL_pow_widen, vd_val, vm_val);

    foreach (i from 0 to (num_elem - 1)) {
      if mask[i] then {
        result[i] = match funct6 {
          WVX_VADD    => to_bits(SEW_widen, signed(vs2_val[i]) + signed(rs1_val)),
          WVX_VSUB    => to_bits(SEW_widen, signed(vs2_val[i]) - signed(rs1_val)),
          WVX_VADDU   => to_bits(SEW_widen, unsigned(vs2_val[i]) + unsigned(rs1_val)),
          WVX_VSUBU   => to_bits(SEW_widen, unsigned(vs2_val[i]) - unsigned(rs1_val)),
          WVX_VWMUL   => to_bits(SEW_widen, signed(vs2_val[i]) * signed(rs1_val)),
          WVX_VWMULU  => to_bits(SEW_widen, unsigned(vs2_val[i]) * unsigned(rs1_val)),
          WVX_VWMULSU => to_bits(SEW_widen, signed(vs2_val[i]) * unsigned(rs1_val))
        }
      }
    };

    write_vreg(num_elem, SEW_widen, LMUL_pow_widen, vd, result);
    vstart = zeros();
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
