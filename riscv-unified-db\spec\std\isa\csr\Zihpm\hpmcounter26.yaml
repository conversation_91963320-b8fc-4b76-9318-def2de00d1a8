# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/hpmcounterN.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: hpmcounter26
long_name: User-mode Hardware Performance Counter 23
address: 0xC1A
description: |
  Alias for M-mode CSR `mhpmcounter26`.

  Privilege mode access is controlled with `mcounteren.HPM26`
  <%- if ext?(:S) -%>
  , `scounteren.HPM26`
  <%- if ext?(:H) -%>
  , and `hcounteren.HPM26`
  <%- end -%>
  <%- end -%>
  as follows:

  <%- if ext?(:H) -%>
  [%autowidth,cols="1,1,1,1,1,1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.HPM26`# .2+h! [.rotate]#`scounteren.HPM26`# .2+h! [.rotate]#`hcounteren.HPM26`#
  4+^.>h! `hpmcounter26` behavior
  .^h! S-mode .^h! U-mode .^h! VS-mode .^h! VU-mode

  ! 0 ! - ! - ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction`
  ! 1 ! 0 ! 0 ! read-only ! `IllegalInstruction` ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 1 ! 0 ! read-only ! read-only ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 0 ! 1 ! read-only ! `IllegalInstruction` ! read-only ! `VirtualInstruction`
  ! 1 ! 1 ! 1 ! read-only ! read-only ! read-only ! read-only
  !===
  <%- elsif ext?(:S) -%>
  [%autowidth,cols="1,1,1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.HPM26`# .2+h! [.rotate]#`scounteren.HPM26`#
  2+^.>h! `hpmcounter26` behavior
  .^h! S-mode .^h! U-mode

  ! 0 ! - ! `IllegalInstruction` ! `IllegalInstruction`
  ! 1 ! 0 ! read-only ! `IllegalInstruction`
  ! 1 ! 1 ! read-only ! read-only
  !===
  <%- else -%>
  [%autowidth,cols="1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.HPM26`#
  ^.>h! `hpmcounter26` behavior
  .^h! U-mode

  ! 0 ! `IllegalInstruction`
  ! 1 ! read-only
  !===
  <%- end -%>
priv_mode: U
length: 64
definedBy: Zihpm
fields:
  COUNT:
    location: 63-0
    alias: mhpmcounter26.COUNT
    description: Alias of `mhpmcounter26.COUNT`.
    type: RO-H
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  # access is determined by *counteren CSRs
  if (mode() == PrivilegeMode::S) {
    # S-mode is present ->
    #   mcounteren determines access in S-mode
    if (CSR[mcounteren].HPM26 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::U) {
    if (CSR[misa].S == 1'b1) {
      # S-mode is present ->
      #   mcounteren and scounteren together determine access in U-mode
      if ((CSR[mcounteren].HPM26 & CSR[scounteren].HPM26) == 1'b0) {
        raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
    } else if (CSR[mcounteren].HPM26 == 1'b0) {
      # S-mode is not present ->
      #   mcounteren determines access in U-mode
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VS) {
    # access in VS mode
    if (CSR[hcounteren].HPM26 == 1'b0 && CSR[mcounteren].HPM26 == 1'b1) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].HPM26 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VU) {
    # access in VU mode
    if (((CSR[hcounteren].HPM26 & CSR[scounteren].HPM26) == 1'b0) && (CSR[mcounteren].HPM26 == 1'b1)) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].HPM26 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  }

  return read_hpm_counter(26);
