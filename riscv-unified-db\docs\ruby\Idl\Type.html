<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::Type
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::Type";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (T)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">Type</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::Type
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Idl::Type</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/type.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Data types</p>


  </div>
</div>
<div class="tags">
  

</div><div id="subclasses">
  <h2>Direct Known Subclasses</h2>
  <p class="children"><span class='object_link'><a href="BitfieldType.html" title="Idl::BitfieldType (class)">BitfieldType</a></span>, <span class='object_link'><a href="CsrType.html" title="Idl::CsrType (class)">CsrType</a></span>, <span class='object_link'><a href="EnumerationType.html" title="Idl::EnumerationType (class)">EnumerationType</a></span>, <span class='object_link'><a href="FunctionType.html" title="Idl::FunctionType (class)">FunctionType</a></span>, <span class='object_link'><a href="XregType.html" title="Idl::XregType (class)">XregType</a></span></p>
</div>

  
    <h2>
      Constant Summary
      <small><a href="#" class="constants_summary_toggle">collapse</a></small>
    </h2>

    <dl class="constants">
      
        <dt id="KINDS-constant" class="">KINDS =
          
        </dt>
        <dd><pre class="code"><span class='lbracket'>[</span>
  <span class='symbol'>:void</span><span class='comma'>,</span>     <span class='comment'># empty
</span>  <span class='symbol'>:boolean</span><span class='comma'>,</span>  <span class='comment'># true or false, not compatible with bits/int/xreg
</span>  <span class='symbol'>:bits</span><span class='comma'>,</span>     <span class='comment'># integer with compile-time-known bit width
</span>  <span class='symbol'>:enum</span><span class='comma'>,</span>     <span class='comment'># enumeration class
</span>  <span class='symbol'>:enum_ref</span><span class='comma'>,</span> <span class='comment'># reference to an enumeration element, convertable to int and/or Bits&lt;bit_width(MAX_ENUM_VALUE)&gt;
</span>  <span class='symbol'>:bitfield</span><span class='comma'>,</span> <span class='comment'># bitfield, convertable to int and/or Bits&lt;width&gt;
</span>  <span class='symbol'>:array</span><span class='comma'>,</span>    <span class='comment'># array of other types
</span>  <span class='symbol'>:tuple</span><span class='comma'>,</span>    <span class='comment'># tuple of other disimilar types
</span>  <span class='symbol'>:function</span><span class='comma'>,</span> <span class='comment'># function
</span>  <span class='symbol'>:template_function</span><span class='comma'>,</span> <span class='comment'># template function, where the template arguments are known but template values need to be applied to become a full function
</span>  <span class='symbol'>:csr</span><span class='comma'>,</span>      <span class='comment'># a CSR register type
</span>  <span class='symbol'>:dontcare</span>  <span class='comment'># matches everything
</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_freeze'>freeze</span></pre></dd>
      
        <dt id="QUALIFIERS-constant" class="">QUALIFIERS =
          
        </dt>
        <dd><pre class="code"><span class='lbracket'>[</span>
  <span class='symbol'>:const</span><span class='comma'>,</span>
  <span class='symbol'>:signed</span>
<span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_freeze'>freeze</span></pre></dd>
      
    </dl>
  




  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#arguments-instance_method" title="#arguments (instance method)">#<strong>arguments</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute arguments.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#enum_class-instance_method" title="#enum_class (instance method)">#<strong>enum_class</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute enum_class.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#kind-instance_method" title="#kind (instance method)">#<strong>kind</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute kind.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#qualifiers-instance_method" title="#qualifiers (instance method)">#<strong>qualifiers</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute qualifiers.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#return_type-instance_method" title="#return_type (instance method)">#<strong>return_type</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute return_type.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#sub_type-instance_method" title="#sub_type (instance method)">#<strong>sub_type</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute sub_type.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#tuple_types-instance_method" title="#tuple_types (instance method)">#<strong>tuple_types</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute tuple_types.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#width-instance_method" title="#width (instance method)">#<strong>width</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute width.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Class Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#from_typename-class_method" title="from_typename (class method)">.<strong>from_typename</strong>(type_name, arch_def)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#ary%3F-instance_method" title="#ary? (instance method)">#<strong>ary?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#ary_type-instance_method" title="#ary_type (instance method)">#<strong>ary_type</strong>(ary)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>given an N-dimensional array type, return the primitive type.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#clone-instance_method" title="#clone (instance method)">#<strong>clone</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#comparable_to%3F-instance_method" title="#comparable_to? (instance method)">#<strong>comparable_to?</strong>(type)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns true if ‘type’ can be compared (e.g., &gt;=, &lt;, etc) to self ‘type’ can be a Type object or a kind (as a Symbol).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#const%3F-instance_method" title="#const? (instance method)">#<strong>const?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#convertable_to%3F-instance_method" title="#convertable_to? (instance method)">#<strong>convertable_to?</strong>(type)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns true if self can be converted to ‘type’ ‘type’ can be a Type object or a kind (as a Symbol).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#default-instance_method" title="#default (instance method)">#<strong>default</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#equal_to%3F-instance_method" title="#equal_to? (instance method)">#<strong>equal_to?</strong>(type)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns true if identical to type, excluding qualifiers.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(kind, qualifiers: [], width: nil, sub_type: nil, name: nil, tuple_types: nil, return_type: nil, arguments: nil, enum_class: nil, csr: nil)  &#x21d2; Type </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Type.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#integral%3F-instance_method" title="#integral? (instance method)">#<strong>integral?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>true for any type that can generally be treated as a scalar integer.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#make_const-instance_method" title="#make_const (instance method)">#<strong>make_const</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#make_signed-instance_method" title="#make_signed (instance method)">#<strong>make_signed</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#mutable%3F-instance_method" title="#mutable? (instance method)">#<strong>mutable?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#qualify-instance_method" title="#qualify (instance method)">#<strong>qualify</strong>(qualifier)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#signed%3F-instance_method" title="#signed? (instance method)">#<strong>signed?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_cxx-instance_method" title="#to_cxx (instance method)">#<strong>to_cxx</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_cxx_no_qualifiers-instance_method" title="#to_cxx_no_qualifiers (instance method)">#<strong>to_cxx_no_qualifiers</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_s-instance_method" title="#to_s (instance method)">#<strong>to_s</strong>  &#x21d2; Object </a>
    

    
      (also: #fully_qualified_name)
    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(kind, qualifiers: [], width: nil, sub_type: nil, name: nil, tuple_types: nil, return_type: nil, arguments: nil, enum_class: nil, csr: nil)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Type.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 62</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_kind'>kind</span><span class='comma'>,</span> <span class='label'>qualifiers:</span> <span class='lbracket'>[</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>sub_type:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>name:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>tuple_types:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>return_type:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>arguments:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>enum_class:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>csr:</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Invalid kind &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_kind'>kind</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#KINDS-constant" title="Idl::Type::KINDS (constant)">KINDS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_kind'>kind</span><span class='rparen'>)</span>

  <span class='ivar'>@kind</span> <span class='op'>=</span> <span class='id identifier rubyid_kind'>kind</span>
  <span class='id identifier rubyid_qualifiers'>qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_q'>q</span><span class='op'>|</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>Invalid qualifier</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#QUALIFIERS-constant" title="Idl::Type::QUALIFIERS (constant)">QUALIFIERS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_q'>q</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@qualifiers</span> <span class='op'>=</span> <span class='id identifier rubyid_qualifiers'>qualifiers</span>
  <span class='comment'># raise &quot;#{width.class.name}&quot; if (kind == :bits &amp;&amp; !width.is_a?(Integer))
</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Should be a FunctionType</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:function</span> <span class='op'>&amp;&amp;</span> <span class='op'>!</span><span class='kw'>self</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="FunctionType.html" title="Idl::FunctionType (class)">FunctionType</a></span></span><span class='rparen'>)</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Width must be an Integer, is a </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
  <span class='ivar'>@width</span> <span class='op'>=</span> <span class='id identifier rubyid_width'>width</span>
  <span class='ivar'>@sub_type</span> <span class='op'>=</span> <span class='id identifier rubyid_sub_type'>sub_type</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Tuples need a type list</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:tuple</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_tuple_types'>tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
  <span class='ivar'>@tuple_types</span> <span class='op'>=</span> <span class='id identifier rubyid_tuple_types'>tuple_types</span>
  <span class='ivar'>@return_type</span> <span class='op'>=</span> <span class='id identifier rubyid_return_type'>return_type</span>
  <span class='ivar'>@arguments</span> <span class='op'>=</span> <span class='id identifier rubyid_arguments'>arguments</span>
  <span class='ivar'>@enum_class</span> <span class='op'>=</span> <span class='id identifier rubyid_enum_class'>enum_class</span>
  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_name'>name</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Bits type must have width</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@width</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Bits type must have positive width</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_positive?'>positive?</span>
  <span class='kw'>end</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Enum type must have width</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@width</span>
  <span class='kw'>end</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:array</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Array must have a subtype</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@sub_type</span>
  <span class='kw'>end</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>CSR type must have a csr argument</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

    <span class='ivar'>@csr</span> <span class='op'>=</span> <span class='id identifier rubyid_csr'>csr</span>

    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>CSR types get width from csr argument; width should not be specified</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='id identifier rubyid_width'>width</span> <span class='op'>==</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span>

    <span class='ivar'>@width</span> <span class='op'>=</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_dynamic_length?'>dynamic_length?</span>
        <span class='kw'>nil</span>
      <span class='kw'>else</span>
        <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span>
      <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="arguments-instance_method">
  
    #<strong>arguments</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute arguments.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_arguments'>arguments</span>
  <span class='ivar'>@arguments</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="enum_class-instance_method">
  
    #<strong>enum_class</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute enum_class.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_enum_class'>enum_class</span>
  <span class='ivar'>@enum_class</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="kind-instance_method">
  
    #<strong>kind</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute kind.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_kind'>kind</span>
  <span class='ivar'>@kind</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="qualifiers-instance_method">
  
    #<strong>qualifiers</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute qualifiers.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_qualifiers'>qualifiers</span>
  <span class='ivar'>@qualifiers</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="return_type-instance_method">
  
    #<strong>return_type</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute return_type.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_return_type'>return_type</span>
  <span class='ivar'>@return_type</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="sub_type-instance_method">
  
    #<strong>sub_type</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute sub_type.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_sub_type'>sub_type</span>
  <span class='ivar'>@sub_type</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="tuple_types-instance_method">
  
    #<strong>tuple_types</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute tuple_types.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_tuple_types'>tuple_types</span>
  <span class='ivar'>@tuple_types</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="width-instance_method">
  
    #<strong>width</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute width.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


41
42
43</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 41</span>

<span class='kw'>def</span> <span class='id identifier rubyid_width'>width</span>
  <span class='ivar'>@width</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="class_method_details" class="method_details_list">
    <h2>Class Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="from_typename-class_method">
  
    .<strong>from_typename</strong>(type_name, arch_def)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


49
50
51
52
53
54
55
56
57
58
59
60</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 49</span>

<span class='kw'>def</span> <span class='kw'>self</span><span class='period'>.</span><span class='id identifier rubyid_from_typename'>from_typename</span><span class='lparen'>(</span><span class='id identifier rubyid_type_name'>type_name</span><span class='comma'>,</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='rparen'>)</span>
  <span class='kw'>case</span> <span class='id identifier rubyid_type_name'>type_name</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>XReg</span><span class='tstring_end'>&#39;</span></span>
    <span class='kw'>return</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>FReg</span><span class='tstring_end'>&#39;</span></span>
    <span class='kw'>return</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:freg</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='int'>32</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>DReg</span><span class='tstring_end'>&#39;</span></span>
    <span class='kw'>return</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:dreg</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='int'>64</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>Bits&lt;((?:0x)?[0-9a-fA-F]+)&gt;</span><span class='regexp_end'>/</span></span>
    <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='backref'>$1</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="ary?-instance_method">
  
    #<strong>ary?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


326
327
328</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 326</span>

<span class='kw'>def</span> <span class='id identifier rubyid_ary?'>ary?</span>
  <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:array</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="ary_type-instance_method">
  
    #<strong>ary_type</strong>(ary)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>given an N-dimensional array type, return the primitive type</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


178
179
180
181
182
183
184</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 178</span>

<span class='kw'>def</span> <span class='id identifier rubyid_ary_type'>ary_type</span><span class='lparen'>(</span><span class='id identifier rubyid_ary'>ary</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_ary'>ary</span><span class='period'>.</span><span class='id identifier rubyid_sub_type'>sub_type</span> <span class='op'>==</span> <span class='symbol'>:array</span>
    <span class='id identifier rubyid_ary_type'>ary_type</span><span class='lparen'>(</span><span class='id identifier rubyid_ary'>ary</span><span class='period'>.</span><span class='id identifier rubyid_sub_type'>sub_type</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_ary'>ary</span><span class='period'>.</span><span class='id identifier rubyid_sub_type'>sub_type</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="clone-instance_method">
  
    #<strong>clone</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


109
110
111
112
113
114
115
116
117
118
119
120
121
122</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 109</span>

<span class='kw'>def</span> <span class='id identifier rubyid_clone'>clone</span>
  <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span>
    <span class='ivar'>@kind</span><span class='comma'>,</span>
    <span class='label'>qualifiers:</span> <span class='ivar'>@qualifiers</span><span class='op'>&amp;.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:clone</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>width:</span> <span class='ivar'>@width</span><span class='comma'>,</span>
    <span class='label'>sub_type:</span> <span class='ivar'>@sub_type</span><span class='op'>&amp;.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span>
    <span class='label'>name:</span> <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_dup'>dup</span><span class='comma'>,</span>
    <span class='label'>tuple_types:</span> <span class='ivar'>@tuple_types</span><span class='op'>&amp;.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:clone</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>return_type:</span> <span class='ivar'>@return_type</span><span class='op'>&amp;.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span>
    <span class='label'>arguments:</span> <span class='ivar'>@arguments</span><span class='op'>&amp;.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:clone</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>enum_class:</span> <span class='ivar'>@enum_class</span><span class='op'>&amp;.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span>
    <span class='label'>csr:</span> <span class='ivar'>@csr</span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="comparable_to?-instance_method">
  
    #<strong>comparable_to?</strong>(type)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns true if ‘type’ can be compared (e.g., &gt;=, &lt;, etc) to self ‘type’ can be a Type object or a kind (as a Symbol)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 126</span>

<span class='kw'>def</span> <span class='id identifier rubyid_comparable_to?'>comparable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Symbol</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type'>type</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not a kind</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#KINDS-constant" title="Idl::Type::KINDS (constant)">KINDS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='kw'>case</span> <span class='ivar'>@kind</span>
  <span class='kw'>when</span> <span class='symbol'>:boolean</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:boolean</span>
  <span class='kw'>when</span> <span class='symbol'>:enum_ref</span>
    <span class='kw'>return</span> \
      <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_enum_class'>enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span> \
      <span class='op'>||</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:bits</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='kw'>self</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:enum</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:function</span>
    <span class='comment'># functions are not comparable to anything
</span>    <span class='kw'>return</span> <span class='kw'>false</span>
  <span class='kw'>when</span> <span class='symbol'>:csr</span>
    <span class='kw'>return</span> <span class='lparen'>(</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span><span class='rparen'>)</span> <span class='op'>||</span>
          <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_width'>width</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unimplemented </span><span class='embexpr_beg'>#{</span><span class='ivar'>@kind</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="const?-instance_method">
  
    #<strong>const?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


330
331
332</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 330</span>

<span class='kw'>def</span> <span class='id identifier rubyid_const?'>const?</span>
  <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='symbol'>:const</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="convertable_to?-instance_method">
  
    #<strong>convertable_to?</strong>(type)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns true if self can be converted to ‘type’ ‘type’ can be a Type object or a kind (as a Symbol)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


188
189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236
237
238
239
240
241
242
243
244</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 188</span>

<span class='kw'>def</span> <span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Symbol</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type'>type</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not a kind</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#KINDS-constant" title="Idl::Type::KINDS (constant)">KINDS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='kw'>case</span> <span class='ivar'>@kind</span>
  <span class='kw'>when</span> <span class='symbol'>:boolean</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:boolean</span>
  <span class='kw'>when</span> <span class='symbol'>:enum_ref</span>
    <span class='kw'>return</span> \
      <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span> <span class='op'>||</span> \
      <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_enum_class'>enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:dontcare</span>
    <span class='kw'>return</span> <span class='kw'>true</span>
  <span class='kw'>when</span> <span class='symbol'>:bits</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>!=</span> <span class='symbol'>:boolean</span>
  <span class='kw'>when</span> <span class='symbol'>:function</span>
    <span class='kw'>return</span> <span class='ivar'>@return_type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:enum</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
      <span class='kw'>return</span> <span class='id identifier rubyid_width'>width</span> <span class='op'>&lt;=</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_width'>width</span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>
      <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_enum_class'>enum_class</span> <span class='op'>==</span> <span class='id identifier rubyid_enum_class'>enum_class</span>
    <span class='kw'>else</span>
      <span class='kw'>return</span> <span class='kw'>false</span>
    <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='symbol'>:tuple</span>
    <span class='id identifier rubyid_is_tuple_of_same_size'>is_tuple_of_same_size</span> <span class='op'>=</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:tuple</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='ivar'>@tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_tuple_types'>tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_is_tuple_of_same_size'>is_tuple_of_same_size</span>
      <span class='ivar'>@tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_each_index'>each_index</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_i'>i</span><span class='op'>|</span>
        <span class='kw'>unless</span> <span class='ivar'>@tuple_types</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_tuple_types'>tuple_types</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='rparen'>)</span>
          <span class='kw'>return</span> <span class='kw'>false</span>
        <span class='kw'>end</span>
      <span class='kw'>end</span>
      <span class='kw'>return</span> <span class='kw'>true</span>
    <span class='kw'>else</span>
      <span class='kw'>return</span> <span class='kw'>false</span>
    <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='symbol'>:csr</span>
    <span class='kw'>return</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='symbol'>:bitfield</span>
    <span class='kw'>if</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:bitfield</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
      <span class='kw'>return</span> <span class='kw'>true</span>
    <span class='kw'>elsif</span> <span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_width'>width</span> <span class='op'>==</span> <span class='ivar'>@width</span><span class='rparen'>)</span>
      <span class='kw'>return</span> <span class='kw'>true</span>
    <span class='kw'>else</span>
      <span class='comment'># be strict with bitfields -- only accept integrals that are exact width Bit types
</span>      <span class='kw'>return</span> <span class='kw'>false</span>
    <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='symbol'>:array</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:array</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_sub_type'>sub_type</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_sub_type'>sub_type</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_width'>width</span> <span class='op'>==</span> <span class='ivar'>@width</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unimplemented type &#39;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@kind</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="default-instance_method">
  
    #<strong>default</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


30
31
32
33
34
35
36
37
38
39</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 30</span>

<span class='kw'>def</span> <span class='id identifier rubyid_default'>default</span>
  <span class='kw'>case</span> <span class='ivar'>@kind</span>
  <span class='kw'>when</span> <span class='symbol'>:bits</span><span class='comma'>,</span> <span class='symbol'>:enum_ref</span><span class='comma'>,</span> <span class='symbol'>:bitfield</span>
    <span class='int'>0</span>
  <span class='kw'>when</span> <span class='symbol'>:boolean</span>
    <span class='kw'>false</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No default for </span><span class='embexpr_beg'>#{</span><span class='ivar'>@kind</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="equal_to?-instance_method">
  
    #<strong>equal_to?</strong>(type)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns true if identical to type, excluding qualifiers</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


156
157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 156</span>

<span class='kw'>def</span> <span class='id identifier rubyid_equal_to?'>equal_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Symbol</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type'>type</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not a kind</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'><span class='object_link'><a href="#KINDS-constant" title="Idl::Type::KINDS (constant)">KINDS</a></span></span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_type'>type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_type'>type</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='kw'>case</span> <span class='ivar'>@kind</span>
  <span class='kw'>when</span> <span class='symbol'>:boolean</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:boolean</span>
  <span class='kw'>when</span> <span class='symbol'>:enum_ref</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span>
  <span class='kw'>when</span> <span class='symbol'>:dontcare</span>
    <span class='kw'>return</span> <span class='kw'>true</span>
  <span class='kw'>when</span> <span class='symbol'>:bits</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_width'>width</span> <span class='op'>==</span> <span class='ivar'>@width</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unimplemented type &#39;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@kind</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="integral?-instance_method">
  
    #<strong>integral?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>true for any type that can generally be treated as a scalar integer</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


26
27
28</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 26</span>

<span class='kw'>def</span> <span class='id identifier rubyid_integral?'>integral?</span>
  <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="make_const-instance_method">
  
    #<strong>make_const</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


347
348
349
350</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 347</span>

<span class='kw'>def</span> <span class='id identifier rubyid_make_const'>make_const</span>
  <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_append'>append</span><span class='lparen'>(</span><span class='symbol'>:const</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_uniq!'>uniq!</span>
  <span class='kw'>self</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="make_signed-instance_method">
  
    #<strong>make_signed</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


342
343
344
345</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 342</span>

<span class='kw'>def</span> <span class='id identifier rubyid_make_signed'>make_signed</span>
  <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_append'>append</span><span class='lparen'>(</span><span class='symbol'>:signed</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_uniq!'>uniq!</span>
  <span class='kw'>self</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="mutable?-instance_method">
  
    #<strong>mutable?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


334
335
336</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 334</span>

<span class='kw'>def</span> <span class='id identifier rubyid_mutable?'>mutable?</span>
  <span class='kw'>return</span> <span class='op'>!</span><span class='id identifier rubyid_const?'>const?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


308
309
310
311
312
313
314
315
316
317
318
319
320
321
322
323
324</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 308</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='kw'>if</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Bits&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@width</span><span class='embexpr_end'>}</span><span class='tstring_content'>&gt;</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>
    <span class='ivar'>@name</span>
  <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bitfield</span>
    <span class='ivar'>@name</span>
  <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:function</span> <span class='op'>||</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:template_function</span>
    <span class='ivar'>@name</span>
  <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span>
    <span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span>
  <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span>
    <span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='ivar'>@kind</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="qualify-instance_method">
  
    #<strong>qualify</strong>(qualifier)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


43
44
45
46
47</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 43</span>

<span class='kw'>def</span> <span class='id identifier rubyid_qualify'>qualify</span><span class='lparen'>(</span><span class='id identifier rubyid_qualifier'>qualifier</span><span class='rparen'>)</span>
  <span class='ivar'>@qualifiers</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_qualifier'>qualifier</span>
  <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_uniq!'>uniq!</span>
  <span class='kw'>self</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="signed?-instance_method">
  
    #<strong>signed?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


338
339
340</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 338</span>

<span class='kw'>def</span> <span class='id identifier rubyid_signed?'>signed?</span>
  <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='symbol'>:signed</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_cxx-instance_method">
  
    #<strong>to_cxx</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


303
304
305
306</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 303</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_cxx'>to_cxx</span>
  <span class='lparen'>(</span><span class='lparen'>(</span><span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_end'>&#39;</span></span> <span class='op'>:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='symbol'>:const</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>const</span><span class='tstring_end'>&#39;</span></span> <span class='op'>:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_end'>&#39;</span></span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>+</span> \
  <span class='id identifier rubyid_to_cxx_no_qualifiers'>to_cxx_no_qualifiers</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_cxx_no_qualifiers-instance_method">
  
    #<strong>to_cxx_no_qualifiers</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


274
275
276
277
278
279
280
281
282
283
284
285
286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 274</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_cxx_no_qualifiers'>to_cxx_no_qualifiers</span>
    <span class='kw'>if</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>@width is a </span><span class='embexpr_beg'>#{</span><span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_signed?'>signed?</span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SignedBits&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='ivar'>@width</span> <span class='op'>:</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_to_cxx'>to_cxx</span><span class='embexpr_end'>}</span><span class='tstring_content'>&gt;</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>else</span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Bits&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='ivar'>@width</span> <span class='op'>:</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_to_cxx'>to_cxx</span><span class='embexpr_end'>}</span><span class='tstring_content'>&gt;</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>end</span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:boolean</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bool</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:function</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>std::function&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@return_type</span><span class='period'>.</span><span class='id identifier rubyid_to_cxx'>to_cxx</span><span class='embexpr_end'>}</span><span class='tstring_content'>(...)&gt;</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:tuple</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>std::tuple&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_t'>t</span><span class='op'>|</span> <span class='id identifier rubyid_t'>t</span><span class='period'>.</span><span class='id identifier rubyid_to_cxx'>to_cxx</span> <span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>,</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>&gt;</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bitfield</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:array</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@sub_type</span><span class='embexpr_end'>}</span><span class='tstring_content'>[]</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_downcase'>downcase</span><span class='period'>.</span><span class='id identifier rubyid_capitalize'>capitalize</span><span class='embexpr_end'>}</span><span class='tstring_content'>Csr</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='ivar'>@kind</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
    <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_s-instance_method">
  
    #<strong>to_s</strong>  &#x21d2; <tt>Object</tt> 
  

  
    <span class="aliases">Also known as:
    <span class="names"><span id='fully_qualified_name-instance_method'>fully_qualified_name</span></span>
    </span>
  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268
269
270
271</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 246</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_s'>to_s</span>
  <span class='lparen'>(</span><span class='lparen'>(</span><span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_end'>&#39;</span></span> <span class='op'>:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@qualifiers</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:to_s</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'> </span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>+</span> \
    <span class='kw'>if</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bits</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Bits&lt;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@width</span><span class='embexpr_end'>}</span><span class='tstring_content'>&gt;</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>enum definition </span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:boolean</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Boolean</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:function</span>
      <span class='ivar'>@return_type</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:enum_ref</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>enum </span><span class='embexpr_beg'>#{</span><span class='ivar'>@enum_class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:tuple</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>(</span><span class='embexpr_beg'>#{</span><span class='ivar'>@tuple_types</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_t'>t</span><span class='op'>|</span> <span class='id identifier rubyid_t'>t</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span> <span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>,</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:bitfield</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bitfield </span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:array</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>array of </span><span class='embexpr_beg'>#{</span><span class='ivar'>@sub_type</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:csr</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[</span><span class='embexpr_beg'>#{</span><span class='ivar'>@csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='ivar'>@kind</span> <span class='op'>==</span> <span class='symbol'>:void</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>void</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='ivar'>@kind</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
    <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>