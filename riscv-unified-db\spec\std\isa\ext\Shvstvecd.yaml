# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Shvstvecd
long_name: vstvec profile requirements
description: |
  `vstvec.MODE` must be capable of holding the value 0 (Direct).
  When `vstvec.MODE`=Direct, `vstvec.BASE` must be capable of holding
  any valid four-byte-aligned address.

  [NOTE]
  This extension was ratified with the RVA22 profiles.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    param_constraints:
      VSTVEC_MODE_DIRECT:
        schema:
          const: true
