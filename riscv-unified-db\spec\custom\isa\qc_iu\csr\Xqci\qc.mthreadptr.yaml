# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

$schema: csr_schema.json#
kind: csr
name: qc.mthreadptr
long_name: Machine Thread Pointer Register
address: 0x7c8
base: 32
priv_mode: M
length: MXLEN
description: |
  Thread pointer register for software use in RTOS. Bits are not interpreted by hardware.
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
fields:
  THREADPTR:
    location: 31-0
    description: Thread pointer value
    type: RW
    reset_value: 0
