{"$schema": "http://json-schema.org/draft-07/schema#", "$defs": {"implies_entry": {"oneOf": [{"$ref": "schema_defs.json#/$defs/extension_with_version"}, {"type": "object", "required": ["if", "then"], "additionalProperties": false, "properties": {"if": {"$ref": "schema_defs.json#/$defs/requires_entry"}, "then": {"oneOf": [{"$ref": "schema_defs.json#/$defs/extension_with_version"}, {"type": "array", "items": {"$ref": "schema_defs.json#/$defs/extension_with_version"}}]}}}]}, "param_data": {"type": "object", "required": ["description", "schema"], "properties": {"description": {"$ref": "schema_defs.json#/$defs/spec_text", "description": "Parameter description, including list of valid values"}, "also_defined_in": {"oneOf": [{"$ref": "schema_defs.json#/$defs/extension_name"}, {"type": "array", "items": {"$ref": "schema_defs.json#/$defs/extension_name"}}], "description": "When a parameter is defined by multiple extensions, declare the other extensions here. The parameter *must* mean the same thing in all extensions."}, "schema": {"$ref": "json-schema-draft-07.json#"}, "when": {"description": "Extension requirement condition that must be met for parameter to exist. The condition that the defining extension is implemented is implicit, and does not need to be explicitly listed", "$ref": "schema_defs.json#/$defs/requires_entry"}, "extra_validation": {"description": "Ruby code to perform extra validation, when it is not easily expressed with JSON Schema (_e.g._, because it depends on the value of another parameter)", "type": "string"}}, "additionalProperties": false}, "ext_data": {"type": "object", "required": ["$schema", "kind", "name", "type", "description", "long_name", "versions"], "properties": {"$schema": {"type": "string", "format": "uri-reference", "const": "ext_schema.json#", "description": "Path to schema, relative to <UDB ROOT>/schemas"}, "kind": {"type": "string", "const": "extension", "description": "Object type"}, "name": {"$ref": "schema_defs.json#/$defs/extension_name"}, "long_name": {"type": "string", "description": "One line description for the extension"}, "description": {"$ref": "schema_defs.json#/$defs/spec_text", "description": "Full documentation of the extension"}, "rvi_jira_issue": {"type": "string", "description": "JIRA issue number for the RVI issue that tracks this extension"}, "company": {"description": "The company that developed this extension", "$ref": "schema_defs.json#/$defs/company"}, "doc_license": {"$ref": "schema_defs.json#/$defs/license"}, "type": {"enum": ["unprivileged", "privileged"], "description": "Either unprivileged or privileged"}, "defined_by": {"type": "string", "enum": ["ISA Manual", "UDB"], "default": "ISA Manual", "description": "Who defines this extension: 'ISA Manual' for RISC-V International Privileged or Unprivileged ISA Manual, 'UDB' for UDB-defined extensions"}, "conflicts": {"description": "Extension(s) that conflict with this extension; both cannot be implemented at the same time", "$ref": "schema_defs.json#/$defs/requires_entry"}, "versions": {"type": "array", "items": {"type": "object", "required": ["version", "state"], "if": {"properties": {"state": {"const": "ratified"}}}, "then": {"required": ["ratification_date"]}, "properties": {"version": {"$ref": "schema_defs.json#/$defs/extension_version"}, "state": {"$ref": "schema_defs.json#/$defs/spec_state", "description": "Current state of this version"}, "repositories": {"description": "Repositories associated with this extension", "type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "format": "uri"}, "branch": {"type": "string", "description": "Branch/tag where the work is done"}}, "additionalProperties": false}}, "ratification_date": {"oneOf": [{"type": "string", "pattern": "^20[0-9][0-9]-(0[1-9]|1[0-2])$", "$comment": "When ratification date is known", "description": "A specific year and month in YYYY-MM format", "examples": ["2019-01", "2024-12"]}, {"type": "string", "pattern": "^unknown$", "$comment": "When ratification date is unknown"}, {"type": "null", "$comment": "When version isn't ratified"}]}, "changes": {"type": "array", "items": {"type": "string"}, "description": "Changes since last version"}, "url": {"type": "string", "format": "uri", "description": "Link to ratified document"}, "implies": {"description": "Extension(s) implied by this extension (i.e., any subextensions)", "oneOf": [{"$ref": "#/$defs/implies_entry"}, {"type": "array", "items": {"$ref": "#/$defs/implies_entry"}}]}, "requires": {"description": "Extension(s) required by this extension", "$ref": "schema_defs.json#/$defs/requires_entry"}, "contributors": {"description": "List of contributors to this version of the extension", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Contributor name, in 'GIVEN_NAME SURNAME' format"}, "company": {"type": "string", "description": "Company the contributor worked for, or 'Individual'"}, "email": {"type": "string", "format": "email", "description": "E-mail address for the contributor"}}}}, "param_constraints": {"type": "object", "patternProperties": {"[A-Z][a-zA-Z0-9_]": {"type": "object", "properties": {"schema": {"$ref": "json-schema-draft-07.json#", "description": "Extra schema constraints for the parameter"}, "extra_validation": {"type": "string", "description": "Extra validation to be performed in Ruby after JSON schema validation. Useful for complex conditions JSON Schema cannot handle (e.g., cross-parameter, data-dependent validation)"}}, "additionalProperties": false}}, "additionalProperties": false}}, "additionalProperties": false}}, "exception_codes": {"type": "array", "items": {"type": "object", "description": "Exceptions defined by this extension", "required": ["num", "name", "var"], "properties": {"num": {"type": "integer"}, "name": {"type": "string", "description": "Long-form name (can have special characters)"}, "var": {"type": "string", "description": "Field name for the InterruptCode enum in IDL"}, "when": {"type": "object", "properties": {"version": {"$ref": "schema_defs.json#/$defs/version_requirements"}}, "additionalProperties": false}}, "additionalProperties": false}}, "interrupt_codes": {"type": "array", "items": {"type": "object", "description": "Interrupts defined by this extension", "properties": {"num": {"type": "integer"}, "name": {"type": "string", "description": "Long-form name (can have special characters)"}, "var": {"type": "string", "description": "Field name for the InterruptCode enum in IDL"}}, "additionalProperties": false}}, "params": {"type": "object", "patternProperties": {"^[A-Z][A-Z_0-9]*$": {"$ref": "#/$defs/param_data"}}, "additionalProperties": false}, "$source": {"type": "string", "description": "Source file where this extension was defined"}, "cert_normative_rules": {"$ref": "schema_defs.json#/$defs/cert_normative_rules"}, "cert_test_procedures": {"$ref": "schema_defs.json#/$defs/cert_test_procedures"}}, "additionalProperties": false}}, "$ref": "#/$defs/ext_data"}