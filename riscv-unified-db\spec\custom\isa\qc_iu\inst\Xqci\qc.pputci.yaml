# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.pputci
long_name: Print character passed in the immediate argument pseudo-instruction (hint) working only in simulation environment
description: |
  The print character instruction calls simulation environment with unsigned `imm` explicit argument.
  Simulation environment expected to print the 8-bit character on its console or standard output.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: " imm"
base: 32
encoding:
  match: 0100--------00000010000000010011
  variables:
    - name: imm
      location: 27-20
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 5;
  XReg arg = imm;
  iss_syscall(func,arg);
