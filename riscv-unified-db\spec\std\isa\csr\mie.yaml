# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mie
long_name: Machine Interrupt Enable
address: 0x304
writable: true
priv_mode: M
length: MXLEN
definedBy: Sm
description: "mip.yaml#/description"
fields:
  SSIE:
    location: 1
    alias:
      - sie.SSIE
    description: |
      Enables Supervisor Software Interrupts.

      Alias of `sie.SSIE` when `mideleg.SSI` is set. Otherwise, `sie.SSIE` is read-only 0.
    type: RW
    definedBy: S
    reset_value: 0
  VSSIE:
    location: 2
    alias:
      - hie.VSSIE
      - vsie.SSIE
      - sie.SSIE
    description: |
      Enables Virtual Supervisor Software Interrupts.

      Alias of `hie.VSSIE`.

      Alias of `vsie.SSIE` when `hideleg.VSSI` is set. Otherwise, `vseie.SSIE` is read-only 0.

      <PERSON><PERSON> of `sie.SSIE` when `hideleg.VSSI` is set and the current mode is VS or VU
      (Because `mie` is inaccessible in VS or VU mode, this alias can never be observed by software).
    type: RW
    definedBy: H
    reset_value: 0
  MSIE:
    location: 3
    description: Enables Machine Software Interrupts.
    type: RW
    reset_value: 0
  STIE:
    location: 5
    alias: sip.STIE
    description: |
      Enables Supervisor Timer Interrupts.

      Alias of `sip.STIE` when `mideleg.STI` is set. Otherwise, `sip.STIE` is read-only 0.
    type: RW
    definedBy: S
    reset_value: 0
  VSTIE:
    location: 6
    alias:
      - hie.VSTIE
      - vsie.STIE
      - sie.STIE
    description: |
      Enables Virtual Supervisor Timer Interrupts.

      Alias of `hie.VSTIE`.

      Alias of `vsie.STIE` when `hideleg.VSTI` is set. Otherwise, `vseie.STIE` is read-only 0.

      Alias of `sie.STIE` when `hideleg.VSTI` is set and the current mode is VS or VU
      (Because `mie` is inaccessible in VS or VU mode, this alias can never be observed by software).
    type: RW
    definedBy: H
    reset_value: 0
  MTIE:
    location: 7
    description: Enables Machine Timer Interrupts.
    type: RW
    reset_value: 0
  SEIE:
    location: 9
    alias: sip.SEIE
    description: |
      Enables Supervisor External Interrupts.

      Alias of `sie.SEIE` when `mideleg.SEI` is set. Otherwise, `sie.SEIE` is read-only 0.
    type: RW
    definedBy: S
    reset_value: 0
  VSEIE:
    location: 10
    alias:
      - hie.VSEIE
      - vsie.SEIE
      - sie.SEIE
    description: |
      Enables Virtual Supervisor External Interrupts.

      Alias of `hie.VSEIE`.

      Alias of `vsie.SEIE` when `hideleg.VSEI` is set. Otherwise, `vseie.SEIE` is read-only 0.

      Alias of `sie.SEIE` when `hideleg.VSEI` is set and the current mode is VS or VU
      (Because `mie` is inaccessible in VS or VU mode, this alias can never be observed by software).
    type: RW
    definedBy: H
    reset_value: 0
  MEIE:
    location: 11
    description: Enables Machine External Interrupts.
    type: RW
    reset_value: 0
  SGEIE:
    location: 12
    alias: hie.SGEIE
    description: |
      Enables Supervisor Guest External Interrupts

      Alias of `hie.SGEIE`.
    type: RW
    definedBy: H
    reset_value: 0
  LCOFIE:
    location: 13
    alias:
      - sie.LCOFIE
      - vsie.LCOFIE
    description: |
      Enables Local Counter Overflow Interrupts.

      Alias of `sie.LCOFIE` when `mideleg.LCOFI` is set. Otherwise, `sie.LCOFIE` is an independent writable bit when `mvien.LCOFI` is set or is read-only 0.

      Alias of `vsip.LCOFIE` when `hideleg.LCOFI` is set. Otherwise, `vsip.LCOFIE` is read-only 0.
    type: RW
    definedBy: Sscofpmf
    reset_value: 0
