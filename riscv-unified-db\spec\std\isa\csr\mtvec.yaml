# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mtvec
long_name: Machine Trap Vector Control
address: 0x305
writable: true
priv_mode: M
length: MXLEN
description: Controls where traps jump.
definedBy: Sm
fields:
  BASE:
    location_rv64: 63-2
    location_rv32: 31-2
    description: |
      Bits [MXLEN-1:2] of the exception vector physical address for any trap taken in M-mode.

      The implementation physical memory map may resitrict which values are legal in this field.
    type: RW-R
    sw_write(csr_value): |
      # Base spec says that BASE must be 4-byte aligned, which will always be the case
      # implementations may put further constraints on BASE when MODE != Direct
      # If that is the case, stvec should have an override for the implementation
      return csr_value.BASE;
    reset_value: 0
  MODE:
    location: 1-0
    description: |
      Vectoring mode for asynchronous interrupts.

      0 - Direct, 1 - Vectored

      When Direct, all synchronous exceptions and asynchronous interrupts jump to (`mtvec.BASE` << 2).

      When Vectored, asynchronous interrupts jump to (`mtvec.BASE` << 2 + `mcause.CAUSE`*4) while synchronous exceptions continue to jump to (`mtvec.BASE` << 2).
    type(): |
      return ($array_size(MTVEC_MODES) == 1) ? CsrFieldType::RO : CsrFieldType::RWR;
    sw_write(csr_value): |
      if (csr_value.MODE == 0) {
        if (ary_includes?<$array_size(MTVEC_MODES), 2>(MTVEC_MODES, 0)) {
          return csr_value.MODE;
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      } else if (csr_value.MODE == 1) {
        if (ary_includes?<$array_size(MTVEC_MODES), 2>(MTVEC_MODES, 1)) {
          return csr_value.MODE;
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      } else {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      }
    reset_value(): |
      return ($array_size(MTVEC_MODES) == 1) ? MTVEC_MODES[0] : UNDEFINED_LEGAL;
