# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.syncr
long_name: Non-memory-mapped device read-after-write synchronization
description: |
  This synchronization instruction delays execution till last read (input)
  from non-memory-mapped device transactions are started for specified devices.
  As well, instruction implies fence.tso functionality for all memory accesses.
  Immediate argument is 5-bit bitmask field that specifies up to five pre-defined devices.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisync
assembly: " imm"
base: 32
encoding:
  match: 0010000-----00000011000000010011
  variables:
    - name: imm
      location: 24-20
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  fence_tso();
  sync_read_after_write_device(false,imm);
