# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
kind: csr
name: vsireg6
long_name: Virtual Supervisor Indirect Register Alias 6
address: 0x257
virtual_address: 0x257
priv_mode: VS
length: VSXLEN
definedBy: Smcsrind
description:
  - id: csr-vsireg6-purpose
    normative: true
    text: |
      The `vsireg6` CSR is one of several alias registers used to indirectly access
      virtual supervisor-level CSRs in VS-mode or VU-mode.

  - id: csr-vsireg6-selection
    normative: true
    text: |
      The register addressed by `vsireg6` is selected by the current value of the `vsiselect` CSR.

  - id: csr-vsireg6-benefits
    normative: false
    text: |
      The alias mechanism allows indirect CSR access, which helps in virtualization and future extensibility.

  - id: csr-vsireg6-access-traps
    normative: true
    text: |
      A virtual instruction exception is raised for attempts from VS-mode or VU-mode
      to directly access `vsiselect` or `vsireg6`, or from VU-mode to access `siselect` or `siregX`.

  - id: csr-vsireg6-unimplemented
    normative: true
    text: |
      The behavior of accesses to `vsireg6` when `vsiselect` holds a value that is
      not implemented at the HS level is UNSPECIFIED.

  - id: csr-vsireg6-recommendation
    normative: false
    text: |
      Implementations are recommended to raise an illegal instruction exception for
      such accesses to unimplemented targets.

  - id: csr-vsireg6-width
    normative: true
    text: |
      The width of `vsireg6` is always the current `XLEN`, not `VSXLEN`.
      For example, if `HSXLEN = 64` and `VSXLEN = 32`, then `vsireg6` is 64 bits wide
      when accessed from HS-mode (RV64), but 32 bits when accessed from VS-mode (RV32).

fields:
  VALUE:
    long_name: Indirectly Selected Register Value
    location_rv32: 31-0
    location_rv64: 63-0
    type: RW
    description:
      - id: csr-vsireg6-value
        normative: true
        text: |
          The data read or written from the register selected by the current value of `vsiselect`.
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 6);
      if (!handle.valid) {
        unimplemented_csr($encoding);
      }
      if (!handle.writable) {
        raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
      csr_sw_write(handle, csr_value.VALUE);
      return csr_hw_read(handle);
sw_read(): |
  Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 6);
  if (!handle.valid) {
    unimplemented_csr($encoding);
  }
  return csr_sw_read(handle);
