# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicie1
long_name: IRQ Enable 1
address: 0x7f9
length: 32
base: 32
priv_mode: M
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Enable bits for IRQs 32-63
fields:
  IRQ32:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ32 enabled
  IRQ33:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ33 enabled
  IRQ34:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ34 enabled
  IRQ35:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ35 enabled
  IRQ36:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ36 enabled
  IRQ37:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ37 enabled
  IRQ38:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ38 enabled
  IRQ39:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ39 enabled
  IRQ40:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ40 enabled
  IRQ41:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ41 enabled
  IRQ42:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ42 enabled
  IRQ43:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ43 enabled
  IRQ44:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ44 enabled
  IRQ45:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ45 enabled
  IRQ46:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ46 enabled
  IRQ47:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ47 enabled
  IRQ48:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ48 enabled
  IRQ49:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ49 enabled
  IRQ50:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ50 enabled
  IRQ51:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ51 enabled
  IRQ52:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ52 enabled
  IRQ53:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ53 enabled
  IRQ54:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ54 enabled
  IRQ55:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ55 enabled
  IRQ56:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ56 enabled
  IRQ57:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ57 enabled
  IRQ58:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ58 enabled
  IRQ59:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ59 enabled
  IRQ60:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ60 enabled
  IRQ61:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ61 enabled
  IRQ62:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ62 enabled
  IRQ63:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ63 enabled
