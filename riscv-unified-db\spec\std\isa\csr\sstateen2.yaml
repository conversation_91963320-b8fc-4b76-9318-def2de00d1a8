# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: sstateen2
long_name: Supervisor State Enable 2 Register
address: 0x10E
priv_mode: S
length: MXLEN
description:
  - id: csr-sstateen2-purpose
    normative: true
    text: |
      Each bit of a `stateen` CSR controls less-privileged access to an extension’s state,
      for an extension that was not deemed "worthy" of a full XS field in `sstatus` like the
      FS and VS fields for the F and V extensions.
  - id: csr-sstateen2-num-justification
    normative: false
    text: |
      The number of registers provided at each level is four because it is believed that
      4 * 64 = 256 bits for machine and hypervisor levels, and 4 * 32 = 128 bits for
      supervisor level, will be adequate for many years to come, perhaps for as long as
      the RISC-V ISA is in use.
      The exact number four is an attempted compromise between providing too few bits on
      the one hand and going overboard with CSRs that will never be used on the other.
  - id: csr-sstateen2-scope
    normative: true
    text: |
      The `stateen` registers at each level control access to state at all less-privileged
      levels, but not at its own level.
  - id: csr-sstateen2-effect
    normative: true
    text: |
      When a `stateen` CSR prevents access to state for a privilege mode, attempting to execute
      in that privilege mode an instruction that implicitly updates the state without reading
      it may or may not raise an illegal instruction or virtual instruction exception.
      Such cases must be disambiguated by being explicitly specified one way or the other.
      In some cases, the bits of the `stateen` CSRs will have a dual purpose as enables for the
      ISA extensions that introduce the controlled state.
  - id: csr-sstateen2-bit-allocation
    normative: true
    text: |
      For every bit with a defined purpose in an `sstateen` CSR, the same bit is defined in the matching
      `mstateen` CSR to control access below machine level to the same state. The upper 32 bits of an
      `mstateen` CSR (or for RV32, the corresponding high-half CSR) control access to state that is inherently
      inaccessible to user level, so no corresponding enable bits in the supervisor-level `sstateen` CSR are
      applicable. The intention is to allocate bits for this purpose starting at the most-significant end, bit 63,
      through to bit 32, and then on to the next-higher `mstateen` CSR. If the rate that bits are being allocated
      from the least-significant end for `sstateen` CSRs is sufficiently low, allocation from the most-significant
      end of `mstateen` CSRs may be allowed to encroach on the lower 32 bits before jumping to the next-higher
      `mstateen` CSR. In that case, the bit positions of "encroaching" bits will remain forever read-only zeros in
      the matching `sstateen` CSRs.
  - id: csr-sstateen2-zero
    normative: true
    text: |
      For every bit in an `mstateen` CSR that is zero (whether read-only zero or set to zero),
      the same bit appears as read-only zero in the matching `hstateen` and `sstateen` CSRs.
      For every bit in an `hstateen` CSR that is zero (whether read-only zero or set to zero),
      the same bit appears as read-only zero in `sstateen` when accessed in VS-mode.
  - id: csr-sstateen2-read-only
    normative: true
    text: |
      A bit in a supervisor-level `sstateen` CSR cannot be read-only one unless the same bit is read-only one in the matching
      `mstateen` CSR and, if it exists, in the matching `hstateen` CSR. Bit 63 of each `mstateen` CSR may be read-only zero
      only if the hypervisor extension is not implemented and the matching supervisor-level `sstateen` CSR is all read-only zeros.

definedBy:
  allOf:
    - Smstateen
    - Ssstateen

fields:
  DATA:
    location_rv64: 63-0
    location_rv32: 31-0
    description: Data value
    type: RW
    sw_write(csr_value): |
      # For every bit in an mstateen CSR that is zero, the same bit
      # appears as read-only zero in the matching sstateen CSR.
      Bits<64> mstateen2_mask = $bits(CSR[mstateen2]);
      Bits<64> data_value = csr_value.DATA & mstateen2_mask;

      # For every bit in an hstateen CSR that is zero, the same bit
      # appears as read-only zero in sstateen when accessed in VS-mode.
      if (mode() == PrivilegeMode::VS) {
        Bits<64> hstateen2_mask = $bits(CSR[hstateen2]);
        data_value = data_value & hstateen2_mask;
      }

      return data_value;
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  # For every bit in an mstateen CSR that is zero, the same bit
  # appears as read-only zero in the matching sstateen CSR.
  Bits<64> mstateen2_mask = $bits(CSR[mstateen2]);
  Bits<64> sstateen2_value = $bits(CSR[sstateen2]) & mstateen2_mask;

  # For every bit in an hstateen CSR that is zero, the same bit
  # appears as read-only zero in sstateen when accessed in VS-mode.
  if (mode() == PrivilegeMode::VS) {
    Bits<64> hstateen2_mask = $bits(CSR[hstateen2]);
    sstateen2_value = sstateen2_value & hstateen2_mask;
  }

  return sstateen2_value;
