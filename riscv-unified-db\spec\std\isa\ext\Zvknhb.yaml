# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvknhb
long_name: "NIST Suite: Vector SHA-2 Secure Hash (SHA-256 + SHA-512)"
description: |
  Instructions for accelerating 256-bit/512-bit SHA-2 as defined in FIPS PUB 180-4 Secure Hash Standard (SHS)

type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    implies:
      name: Zvknha
      version: "1.0.0"
