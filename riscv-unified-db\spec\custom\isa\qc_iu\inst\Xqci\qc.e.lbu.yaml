# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.lbu
long_name: Load byte unsigned
description: |
  Load 8 bits of data into register `rd` from an
  address formed by adding `rs1` to a signed offset `imm`.
  Zero extend the result.
  Instruction encoded in QC.EI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilo
assembly: " xd, imm(xs1)"
base: 32
encoding:
  match: ----------------01---------------101-----0011111
  variables:
    - name: imm
      location: 47-32|29-20
    - name: rs1
      location: 19-15
    - name: rd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg virtual_address = X[rs1] + $signed(imm);
  X[rd] = read_memory<8>(virtual_address, $encoding);
