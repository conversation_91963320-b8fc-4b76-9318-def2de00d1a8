# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: instreth
long_name: Instructions retired counter, high bits
address: 0xC82
writable: false
base: 32
description: |
  Alias for high bits of M-mode CSR `minstret`[63:32].

  Privilege mode access is controlled with `mcounteren.IR`, `scounteren.IR`, and `hcounteren.IR` as follows:

  [%autowidth,cols="1,1,1,1,1,1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.IR`# .2+h! [.rotate]#`scounteren.IR`# .2+h! [.rotate]#`hcounteren.IR`#
  4+^.>h! `instret` behavior
  .^h! S-mode .^h! U-mode .^h! VS-mode .^h! VU-mode

  ! 0 ! - ! - ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction`
  ! 1 ! 0 ! 0 ! read-only ! `IllegalInstruction` ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 1 ! 0 ! read-only ! read-only ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 0 ! 1 ! read-only ! `IllegalInstruction` ! read-only ! `VirtualInstruction`
  ! 1 ! 1 ! 1 ! read-only ! read-only ! read-only ! read-only
  !===
priv_mode: U
length: 32
definedBy: Zicntr
fields:
  COUNT:
    location: 31-0
    alias: minstret.COUNT[63:32]
    description: Alias of `minstret.COUNT`[63:32].
    type: RO-H
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  # access is determined by *counteren CSRs
  if (mode() == PrivilegeMode::S) {
    # S-mode is present ->
    #   mcounteren determines access in S-mode
    if (CSR[mcounteren].IR == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::U) {
    if (CSR[misa].S == 1'b1) {
      # S-mode is present ->
      #   mcounteren and scounteren together determine access in U-mode
      if ((CSR[mcounteren].IR & CSR[scounteren].IR) == 1'b0) {
        raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
    } else if (CSR[mcounteren].IR == 1'b0) {
      # S-mode is not present ->
      #   mcounteren determines access in U-mode
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VS) {
    # access in VS mode
    if (CSR[hcounteren].IR == 1'b0 && CSR[mcounteren].IR == 1'b1) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].IR == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VU) {
    # access in VU mode
    if (((CSR[hcounteren].IR & CSR[scounteren].IR) == 1'b0) && (CSR[mcounteren].IR == 1'b1)) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].IR == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  }

  # since the counter may be shared among harts, reads must be handled
  # as a builtin function
  return read_mcycle();
