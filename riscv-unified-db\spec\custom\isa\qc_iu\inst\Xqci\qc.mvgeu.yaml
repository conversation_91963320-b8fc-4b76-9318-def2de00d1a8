# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.mvgeu
long_name: Conditional move if great or equal than unsigned (Register)
description: |
  Move `rs3` to `rd` if the unsigned value in `rs1` is great or equal than unsigned value `rs2`.
  Instruction encoded in R4 instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcicm
base: 32
encoding:
  match: -----00----------111-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: rs3
      location: 31-27
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2, xs3"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (X[rs1] >= X[rs2]) {
    X[rd] = X[rs3];
  }
