{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["$schema", "kind", "name", "long_name", "base"], "properties": {"$schema": {"type": "string", "const": "profile_schema.json#"}, "kind": {"type": "string", "const": "profile"}, "name": {"type": "string", "description": "Name (database key) of this Profile"}, "long_name": {"type": "string", "description": "One line description of profile"}, "base": {"type": "integer", "description": "32 for RV32I or 64 for RV64I"}, "cert_normative_rules": {"$ref": "schema_defs.json#/$defs/cert_normative_rules"}, "cert_test_procedures": {"$ref": "schema_defs.json#/$defs/cert_test_procedures"}}}