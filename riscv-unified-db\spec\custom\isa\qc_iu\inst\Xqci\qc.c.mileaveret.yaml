# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.c.mileaveret
long_name: Machine mode interrupt exit
description: |
  Machine mode interrupt exit.
  Interrupt frame is restored from the stack.
  Instruction encoded in CI instruction format.
assembly: ""
definedBy:
  anyOf:
    - Xqci
    - Xqciint
access:
  s: never
  u: never
  vs: never
  vu: never
base: 32
encoding:
  match: "0001101000010010"
operation(): |
  XReg virtual_address_sp = get_and_validate_stack_pointer(X[2], $encoding);
  XReg virtual_address = virtual_address_sp + 96;
  XReg prev_retpc = read_memory<32>(virtual_address -  4, $encoding);
  XReg qc_mcause_val = read_memory<32>(virtual_address - 12, $encoding);
  Bits<1> nmie_val = CSR[mnstatus].NMIE;
  XReg qc_mcause_prev_val = CSR[qc.mcause].sw_read();
  XReg qc_mcause_nmip_excp_mask = (32'b1 << 24) | (32'b1 << 25);
  XReg qc_mcause_new_val = (qc_mcause_val & ~qc_mcause_nmip_excp_mask) | (qc_mcause_prev_val & qc_mcause_nmip_excp_mask);
  CSR[qc.mcause].sw_write(qc_mcause_new_val);
  X[ 8] = read_memory<32>(virtual_address -  8, $encoding);
  X[ 1] = read_memory<32>(virtual_address - 16, $encoding);
  X[ 5] = read_memory<32>(virtual_address - 24, $encoding);
  X[ 6] = read_memory<32>(virtual_address - 28, $encoding);
  X[ 7] = read_memory<32>(virtual_address - 32, $encoding);
  X[10] = read_memory<32>(virtual_address - 36, $encoding);
  X[11] = read_memory<32>(virtual_address - 40, $encoding);
  X[12] = read_memory<32>(virtual_address - 44, $encoding);
  X[13] = read_memory<32>(virtual_address - 48, $encoding);
  X[14] = read_memory<32>(virtual_address - 52, $encoding);
  X[15] = read_memory<32>(virtual_address - 56, $encoding);
  X[16] = read_memory<32>(virtual_address - 60, $encoding);
  X[17] = read_memory<32>(virtual_address - 64, $encoding);
  X[28] = read_memory<32>(virtual_address - 68, $encoding);
  X[29] = read_memory<32>(virtual_address - 72, $encoding);
  X[30] = read_memory<32>(virtual_address - 76, $encoding);
  X[31] = read_memory<32>(virtual_address - 80, $encoding);
  X[2] = X[2] + 96;
  if (nmie_val == 1'b1) {
    XReg qc_mcause_val_masked = qc_mcause_new_val & ~(32'b1<<26) & ~(32'b1<<27) & ~(32'b1<<29) & ~(32'hFF<<12);
    Bits<1> mpie_val = (qc_mcause_val >> 27) & 1;
    Bits<1> mpdt_val = (qc_mcause_val >> 29) & 1;
    Bits<4> mpil_val = (qc_mcause_val >> 16) & 0xF;
    CSR[mstatus].MIE = mpie_val;
    CSR[mstatus].MPIE = 1'b1;
    if (implemented?(ExtensionName::Smdbltrp)) {
      CSR[mstatush].MDT = mpdt_val;
    }
    CSR[qc.mcause].sw_write(qc_mcause_val_masked |
                            (32'b1<<27) | (mpie_val`<<26) | (32'b0<<29) |
                            (mpil_val `<< 12) | (32'b1111 << 16));
    if (mpdt_val == 1'b0) {
      if (CSR[mstatus].MPP != 2'b11) {
        CSR[mstatus].MPRV = 0;
      }
      if (CSR[mstatus].MPP == 2'b00) {
        set_mode(PrivilegeMode::U);
      } else if (CSR[mstatus].MPP == 2'b01) {
        set_mode(PrivilegeMode::S);
      } else if (CSR[mstatus].MPP == 2'b11) {
        set_mode(PrivilegeMode::M);
      }
      CSR[mstatus].MPP = implemented?(ExtensionName::U) ? 2'b00 : 2'b11;
    }
    $pc = CSR[mepc].sw_read();
  } else {
    XReg qc_mcause_val_masked = qc_mcause_new_val & ~(32'b1<<26) & ~(32'b1<<28) & ~(32'b1<<30) & ~(32'b1111<<12) & ~(32'b1111<<20);
    Bits<1> mnpie_val = (qc_mcause_val >> 28) & 1;
    Bits<4> mnpil_val = (qc_mcause_val >> 20) & 0xF;
    CSR[mstatus].MIE = mnpie_val;
    CSR[mnstatus].NMIE = 1'b1;
    CSR[qc.mcause].sw_write(qc_mcause_val_masked |
                            (32'b1<<28) | (mnpie_val`<<26) | (32'b1<<30) |
                            (mnpil_val `<< 12) | (32'b1111 << 20));
    if (CSR[mnstatus].MNPP != 2'b11) {
      CSR[mstatus].MPRV = 0;
      if (implemented?(ExtensionName::Smdbltrp)) {
        CSR[mstatush].MDT = 1'b0;
      }
    }
    if (CSR[mnstatus].MNPP == 2'b00) {
      set_mode(PrivilegeMode::U);
    } else if (CSR[mnstatus].MNPP == 2'b01) {
      set_mode(PrivilegeMode::S);
    } else if (CSR[mnstatus].MNPP == 2'b11) {
      set_mode(PrivilegeMode::M);
    }
    CSR[mnstatus].MNPP = implemented?(ExtensionName::U) ? 2'b00 : 2'b11;
    $pc = CSR[mnepc].sw_read();
  }
