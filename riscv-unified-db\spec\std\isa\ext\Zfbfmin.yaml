# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zfbfmin
long_name: Scalar BF16 Converts
description: |
  The minimal set of instructions needed to enable scalar support of the BF16 format.
  It enables BF16 as an interchange format as it provides conversion between BF16 values and
  FP32 values.

  This extension depends upon the single-precision floating-point extension `F`,
  and the `flh`, `fsh`, `fmv.x.h`, and `fmv.h.x` instructions as defined in the `Zfh` extension.

type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires:
      allOf:
        - name: F
        - name: Zfh
