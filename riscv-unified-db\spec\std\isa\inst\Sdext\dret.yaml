# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: dret
long_name: No synopsis available
description: |
  No description available.
definedBy: Sdext
assembly: dret
encoding:
  match: "01111011001000000000000001110011"
  variables: []
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
