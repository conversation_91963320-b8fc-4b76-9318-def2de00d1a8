# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Xmock
type: privileged
long_name: Mock Extension (for testing database)
description: This is just for testing
defined_by: UDB
versions:
  - version: "0.9.9"
    state: development
  - version: "1.0.0"
    state: ratified
    ratification_date: 2024-04
params:
  MOCK_ENUM_2_INTS:
    description: foo
    schema:
      type: integer
      enum: [32, 64]
  MOCK_ENUM_2_STRINGS:
    description: foo
    schema:
      type: string
      enum: ["low", "high"]
  MOCK_BOOL_1:
    description: foo
    schema:
      type: boolean
  MOCK_BOOL_2:
    description: foo
    schema:
      type: boolean
  MOCK_32_BIT_INT:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 0xffffffff
  MOCK_64_BIT_INT:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 0xffffffffffffffff
  MOCK_1_BIT_INT:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 1
  MOCK_2_BIT_INT:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 3
  MOCK_25_BIT_INT:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 33554431
  MOCK_INT_RANGE_0_TO_2:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 2
  MOCK_INT_RANGE_0_TO_127:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 127
  MOCK_INT_RANGE_0_TO_999:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 999
  MOCK_INT_RANGE_0_TO_1023:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 1023
  MOCK_INT_RANGE_1000_TO_2048:
    description: foo
    schema:
      type: integer
      minimum: 1000
      maximum: 2048
  MOCK_INT_RANGE_0_TO_128:
    description: foo
    schema:
      type: integer
      minimum: 0
      maximum: 128
  MOCK_INT_RANGE_1_TO_128:
    description: foo
    schema:
      type: integer
      minimum: 1
      maximum: 128
  MOCK_ARRAY_INT_ENUM:
    description: foo
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1]
      minItems: 1
      maxItems: 2
      uniqueItems: true
  MOCK_ARRAY_MIN_ONLY:
    description: foo
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1]
      minItems: 3
  MOCK_ARRAY_MAX_ONLY:
    description: foo
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1]
      maxItems: 10
  MOCK_ARRAY_STRING_ENUM1:
    description: foo
    schema:
      type: array
      items:
        type: string
        enum: [ABC, DEF, GHI]
  MOCK_ARRAY_STRING_ENUM2:
    description: foo
    schema:
      type: array
      items:
        type: string
        enum: [ABC, DEF, GHI]
  MOCK_ARRAY_BOOL_ARRAY_OF_8_FIRST_2_FALSE:
    description: foo
    schema:
      type: array
      items:
        - const: false
        - const: false
      additionalItems:
        type: boolean
      maxItems: 8
      minItems: 8
cert_normative_rules:
  - id: ext.Xmock.cov1
    name: Mock normative rule 1
    description: Let's have fun with the `Xmock` extension
    doc_links:
      - manual:inst:mul:encoding
      - udb:doc:inst:mock
  - id: ext.Xmock.cov2
    name: Mock normative rule 2
    description: And some more fun!
    doc_links:
      - manual:csr:misa:disabling-extension
cert_test_procedures:
  - id: ext.Xmock.my_first
    test_file_name: mock.S
    description: Verify that when it rains in Spain, it rains mainly on the plains!
    normative_rules: [ext.Xmock.cov1, ext.Xmock.cov2]
    steps: |
      . Wait for rain
      .. First we need some rain
      +
      [NOTE]
      This is getting silly. Very unprofessional. This note is preceded by a + instead of a blank line.
      . Measure rainfall
      .. Get a bunch of buckets around Spain
      +
      [NOTE]
      Yup, pretty silly. This note also has to be preceded by a + so that the ordered list
      number continues rather than being reset to 1.
      . Compare rainful. See this number is continued, not 1.
      .. Fail unless more rain falls on the plains than other regions
