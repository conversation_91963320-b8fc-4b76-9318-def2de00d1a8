# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ssccptr
long_name: Cacheable and coherent main memory page table reads
description: |
  Main memory regions with both the cacheability and coherence PMAs must support hardware page-table reads.

  [NOTE]
  This extension was ratified with the RVA20 profiles.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    url: https://github.com/riscv/riscv-profiles/releases/tag/v1.0
    repositories:
      - url: https://github.com/riscv/riscv-profiles
        branch: main
    contributors:
      - name: Krste <PERSON>ano<PERSON>
        company: SiFive, Inc.
