{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Common patterns used by all schemas", "$defs": {"$source": {"type": "string", "format": "uri-reference", "description": "Path to the source file containing this object"}, "semantic_version": {"type": "string", "pattern": "^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$"}, "rvi_version": {"type": "string", "pattern": "^[0-9]+(\\.[0-9]+(\\.[0-9]+(-pre)?)?)?$"}, "csr_name": {"type": "string", "pattern": "^[a-z][a-z0-9_.]+$", "description": "CSR name"}, "csr_field": {"type": "string", "pattern": "^[a-z][a-z0-9_.]+\\.[A-Z0-9]+$", "description": "CSR field"}, "csr_field_bits": {"type": "string", "pattern": "^[a-z][a-z0-9_.]+\\.[A-Z0-9]+\\[[0-9]+(:[0-9]+)?\\]$", "description": "CSR field"}, "field_location": {"oneOf": [{"type": "integer", "description": "Location of a single bit"}, {"type": "string", "pattern": "^[0-9]+-[0-9]+$", "description": "Location range of a multi-bit field"}], "description": "Location of a field in a register"}, "possibly_split_field_location": {"description": "Location specifier for a field", "oneOf": [{"description": "bit range location, possibly split", "type": "string", "pattern": "^(([0-9]+)|([0-9]+-[0-9]+))(\\|(([0-9]+)|([0-9]+-[0-9]+)))*$"}, {"description": "Single bit location", "type": "integer", "minimum": 0, "maximum": 31}]}, "revision_history_entry": {"type": "object", "properties": {"revision": {"$ref": "#/$defs/semantic_version", "description": "Revision number"}, "date": {"type": "string", "format": "date", "description": "The date of the change"}, "changes": {"type": "array", "items": {"type": "string"}, "description": "List of changes"}}, "required": ["date", "revision", "changes"], "additionalProperties": false}, "spec_state": {"type": "string", "enum": ["development", "frozen", "public-review", "ratification-ready", "ratified", "nonstandard-released"]}, "spec_text": {"oneOf": [{"type": "string", "description": "Asciidoctor source"}, {"type": "array", "items": {"$ref": "#/$defs/tagged_text"}}]}, "tagged_text": {"type": "object", "required": ["id", "text", "normative"], "properties": {"id": {"type": "string", "description": "Unique identifier for the statement"}, "text": {"type": "string", "description": "Asciidoctor source"}, "normative": {"type": "boolean"}, "when()": {"type": "string", "description": "IDL boolean expression. When true, the text applies"}}, "additionalProperties": false}, "license": {"description": "License that applies to the textual documentation for this extension", "type": "object", "properties": {"name": {"type": "string", "description": "License name"}, "id": {"type": "string", "description": "License identifier"}, "url": {"type": "string", "format": "uri", "description": "Link to license text"}, "text_url": {"type": "string", "format": "uri", "description": "Link to license text"}}, "additionalProperties": false}, "company": {"description": "A company", "type": "object", "properties": {"name": {"type": "string", "description": "Name of the company. Should be \"RISC-V International\" for standard extensions"}, "url": {"type": "string", "format": "uri", "description": "Website of the company. Should be \"https://riscv.org\" for standard extensions"}}}, "extension_presence": {"oneOf": [{"type": "string", "enum": ["mandatory", "optional", "prohibited"]}, {"type": "object", "required": ["optional"], "properties": {"optional": {"type": "string", "enum": ["localized", "development", "expansion", "transitory"]}}, "additionalProperties": false}]}, "date": {"type": "string", "format": "date", "description": "A specific day in YYYY-MM-DD format", "examples": ["2018-11-13", "2024-12-31"]}, "extension_name": {"type": "string", "pattern": "^(([A-WY])|([SXZ][a-z0-9]+))$"}, "extension_version": {"$ref": "#/$defs/rvi_version"}, "requirement_string": {"type": "string", "pattern": "^((>=)|(>)|(~>)|(<)|(<=)|(=))?\\s*[0-9]+(\\.[0-9]+(\\.[0-9]+(-[a-fA-F0-9]+)?)?)?$"}, "version_requirements": {"description": "A (set of) version requirements", "oneOf": [{"$ref": "#/$defs/requirement_string"}, {"type": "array", "items": {"$ref": "#/$defs/requirement_string"}}]}, "extension_requirement": {"description": "A requirement on an extension. Can either specify just an extension name, in which case version '>= 0' is implied, or both a name and a requirement", "oneOf": [{"$ref": "#/$defs/extension_name"}, {"type": "object", "properties": {"name": {"$ref": "#/$defs/extension_name"}, "version": {"$ref": "#/$defs/version_requirements"}}, "required": ["name"], "additionalProperties": false}]}, "requires_entry": {"oneOf": [{"$ref": "#/$defs/extension_requirement"}, {"type": "object", "properties": {"anyOf": {"type": "array", "items": {"$ref": "#/$defs/requires_entry"}}, "allOf": {"type": "array", "items": {"$ref": "#/$defs/requires_entry"}}, "oneOf": {"type": "array", "items": {"$ref": "#/$defs/requires_entry"}}, "not": {"type": "object", "$ref": "#/$defs/requires_entry"}}, "additionalProperties": false}]}, "extension_with_version": {"type": "object", "required": ["name", "version"], "properties": {"name": {"$ref": "#/$defs/extension_name"}, "version": {"$ref": "#/$defs/extension_version"}}}, "author": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Author's full name"}, "email": {"type": "string", "format": "email", "description": "Author's email address"}, "organization": {"$ref": "#/$defs/organization", "description": "Author's organization, which should be an RVI member (or individual)"}}, "additionalProperties": false}, "organization": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Organization name"}, "url": {"type": "string", "format": "uri", "description": "Organization URL"}}, "additionalProperties": false}, "when_condition": {"type": "object", "properties": {"version": {"$ref": "#/$defs/version_requirements"}}, "additionalProperties": false}, "parameter_constraint": {"type": "object", "properties": {"schema": {"$ref": "json-schema-draft-07.json#"}, "when": {"$ref": "#/$defs/when_condition"}}}, "cert_normative_rules": {"description": "Architecturally visible behaviors requiring validation by certification tests", "type": "array", "required": ["id", "name", "doc_links", "description"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "doc_links": {"description": "Link to UDB documentation, ISA manual, Sail code, or IDL code", "type": "array", "items": {"type": "string"}}, "description": {"type": "string"}}, "additionalProperties": false}, "cert_test_procedures": {"description": "Procedure test must follow to test certification normative rules", "type": "array", "required": ["id", "name", "description", "normative_rules"], "properties": {"id": {"type": "string"}, "test_file_name": {"type": "string"}, "description": {"type": "string"}, "normative_rules": {"type": "array", "description": "List of certification normative rule IDs to be validated", "items": {"type": "string"}}, "steps": {"description": "List of steps typically using Asciidoc unordered lists", "type": "string"}, "additionalProperties": false}}, "encoding_match": {"oneOf": [{"type": "string", "pattern": "^[01-]{43}11111$", "description": "48-bit encoding"}, {"type": "string", "pattern": "^[01-]{30}11$", "description": "32-bit encoding"}, {"type": "string", "pattern": "^[01-]{14}((00)|(01)|(10))$", "description": "16-bit encoding"}]}, "inst_type_name": {"type": "string", "pattern": "[A-Z][A-Za-z0-9\\-]*"}, "inst_subtype_name": {"type": "string", "pattern": "[A-Z][A-Za-z0-9\\-]*-[A-Za-z0-9\\-]*"}, "reference": {"type": "string", "pattern": "^.+\\.yaml#(/.*)?$", "description": "refrence to another database object, as a JSON Reference"}, "integer": {"description": "An integer, either native to JSON or a number-like string", "oneOf": [{"type": "integer"}, {"type": "string", "pattern": "^0b[01]+$"}, {"type": "string", "pattern": "^0x[a-fA-F0-9]+$"}]}, "ref_url": {"type": "string", "pattern": "^.*/.*\\.yaml#.*$"}, "ref_url_list": {"oneOf": [{"$ref": "#/$defs/ref_url"}, {"type": "array", "items": {"$ref": "#/$defs/ref_url"}}]}}}