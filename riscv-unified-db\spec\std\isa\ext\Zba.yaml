# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: <PERSON>ba
long_name: Address generation instructions
description: |
  The Zba instructions can be used to accelerate the generation of addresses that index into
  arrays of basic types (halfword, word, doubleword) using both unsigned word-sized and
  XLEN-sized indices: a shifted index is added to a base address.

  The shift and add instructions do a left shift of 1, 2, or 3 because these are commonly found
  in real-world code and because they can be implemented with a minimal amount of additional
  hardware beyond that of the simple adder. This avoids lengthening the critical path in
  implementations.

  While the shift and add instructions are limited to a maximum left shift of 3, the `slli`
  instruction (from the base ISA) can be used to perform similar shifts for indexing into arrays
  of wider elements. The `slli.uw` -- added in this extension -- can be used when the index is to
  be interpreted as an unsigned word.
type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2021-06
    repositories:
      - url: https://github.com/riscv/riscv-bitmanip
        branch: main
    contributors:
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON><PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON><PERSON>
      - name: <PERSON> <PERSON><PERSON>r
      - name: <PERSON><PERSON>ier <PERSON><PERSON>ee
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>ser
      - name: <PERSON> <PERSON>ahu<PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>ies<PERSON>
      - name: <PERSON> <PERSON><PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON>-wei <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON> <PERSON>c<PERSON>rary
      - name: <PERSON> <PERSON>
      - name: <PERSON><PERSON> <PERSON>ravec
      - name: Samuel Neves
      - name: Markus Oberhumer
      - name: Christopher Olson
      - name: Nils Pipenbrinck
      - name: Joseph Rahmeh
      - name: Xue Saw
      - name: Tommy Thorn
      - name: Philipp Tomsich
      - name: Avishai Tvila
      - name: Andrew Waterman
      - name: Thomas Wicki
      - name: Claire Wolf
