# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mstateen3h
long_name: Upper 32 bits of Machine State Enable 3 Register
address: 0x31F
priv_mode: M
length: 32
base: 32
description:
  - id: csr-mstateen3h-purpose
    normative: true
    text: |
      For RV64 harts, the Smstateen extension adds four new 64-bit CSRs at machine level: `mstateen0` (Machine State
      Enable 0), `mstateen1`, `mstateen2`, and `mstateen3`. For RV32, the registers listed above are 32-bit, and for the
      machine-level CSRs there is a corresponding set of high-half CSRs for the upper 32 bits of each register:
      `mstateen0h`, `mstateen1h`, `mstateen2h`, `mstateen3h`.

definedBy: Smstateen
fields:
  SE0:
    long_name: hstateen3, hstateen3h, and sstateen3 access control
    location: 31
    alias: mstateen3.SE0
    sw_write(csr_value): |
      CSR[mstateen3].SE0 = csr_value.SE0;
      return csr_value.SE0;
    description: |
      The SE0 bit in `mstateen3h` controls access to the `hstateen3`, `hstateen3h`, and the `sstateen3` CSRs.
    type: RW
    reset_value: 0
sw_read(): return $bits(CSR[mstateen3])[63:32];
