# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl05
long_name: IRQ Level 5
address: 0xbc5
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 40-47
fields:
  IRQ40:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ40 level
  IRQ41:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ41 level
  IRQ42:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ42 level
  IRQ43:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ43 level
  IRQ44:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ44 level
  IRQ45:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ45 level
  IRQ46:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ46 level
  IRQ47:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ47 level
