# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zama16b
long_name: Misaligned load/store/AMO within aligned 16-byte address are atomic
type: unprivileged
description: |
  Misaligned loads, stores, and AMOs to main memory regions that do not cross a
  naturally-aligned 16-byte boundary are atomic.

  [NOTE]
  Zama16b is a new RVA23 profile-defined extension that represents
  the presence of the new Misaligned Atomicity Granule feature added in
  Sm1p13.  The extension will be added to the PMA section of the
  privileged architecture manual.

versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
