# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.insbi
long_name: Insert bits (Immediate)
description: |
  Insertion of a subset of bits of an `imm` into `rd`.
  The width of the subset is determined by (`width_minus1` + 1) (1..32),
  and the offset of the subset is determined by `shamt`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 00---------------001-----0001011
  variables:
    - name: width_minus1
      location: 29-25
    - name: shamt
      location: 24-20
    - name: imm
      location: 19-15
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, imm, width, shamt"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg width = width_minus1 + 1;
  XReg mask = ((32'b1 << width) - 1) << shamt;
  XReg orig_val = X[rd];
  XReg src = $signed(imm);
  X[rd] = (orig_val & ~mask) | ((src << shamt) & mask);
