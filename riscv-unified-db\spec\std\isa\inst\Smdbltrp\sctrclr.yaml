# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: sctrclr
long_name: No synopsis available
description: |
  No description available.
definedBy: Smdbltrp
assembly: sctrclr
encoding:
  match: "00010000010000000000000001110011"
  variables: []
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
