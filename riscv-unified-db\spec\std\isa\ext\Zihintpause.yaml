# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zihintpause
long_name: PAUSE instruction
description: |
  The PAUSE instruction is a HINT that indicates the current hart's rate
  of instruction retirement should be temporarily reduced or paused. The
  duration of its effect must be bounded and may be zero.
  (((PAUSE, HINT)))
  (((HINT, PAUSE)))

  [NOTE]
  ====
  Software can use the PAUSE instruction to reduce energy consumption
  while executing spin-wait code sequences. Multithreaded cores might
  temporarily relinquish execution resources to other harts when PAUSE is
  executed. It is recommended that a PAUSE instruction generally be
  included in the code sequence for a spin-wait loop.
  (((PAUSE, energy consumption)))

  A future extension might add primitives similar to the x86 MONITOR/MWAIT
  instructions, which provide a more efficient mechanism to wait on writes
  to a specific memory location. However, these instructions would not
  supplant PAUSE. PAUSE is more appropriate when polling for non-memory
  events, when polling for multiple events, or when software does not know
  precisely what events it is polling for.

  The duration of a PAUSE instruction's effect may vary significantly
  within and among implementations. In typical implementations this
  duration should be much less than the time to perform a context switch,
  probably more on the rough order of an on-chip cache miss latency or a
  cacheless access to main memory.
  (((PAUSE, duration)))

  A series of PAUSE instructions can be used to create a cumulative delay
  loosely proportional to the number of PAUSE instructions. In spin-wait
  loops in portable code, however, only one PAUSE instruction should be
  used before re-evaluating loop conditions, else the hart might stall
  longer than optimal on some implementations, degrading system
  performance.
  ====

  PAUSE is encoded as a FENCE instruction with _pred_=`W`, _succ_=`0`, _fm_=`0`,
  _rd_=`x0`, and _rs1_=`x0`.

  //include::images/wavedrom/zihintpause-hint.edn[]
  //[zihintpause-hint]
  //.Zihintpause fence instructions

  [NOTE]
  ====
  PAUSE is encoded as a hint within the FENCE opcode because some
  implementations are expected to deliberately stall the PAUSE instruction
  until outstanding memory transactions have completed. Because the
  successor set is null, however, PAUSE does not _mandate_ any particular
  memory ordering--hence, it truly is a HINT.
  (((PAUSE, encoding)))

  Like other FENCE instructions, PAUSE cannot be used within LR/SC
  sequences without voiding the forward-progress guarantee.
  (((PAUSE, LR/RC sequences)))

  The choice of a predecessor set of W is arbitrary, since the successor
  set is null. Other HINTs similar to PAUSE might be encoded with other
  predecessor sets.
  ====
type: unprivileged
versions:
  - version: "2.0.0"
    state: ratified
    ratification_date: null
