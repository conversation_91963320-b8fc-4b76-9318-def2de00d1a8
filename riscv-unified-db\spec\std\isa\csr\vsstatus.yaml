# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: vsstatus
long_name: Virtual Supervisor Status
address: 0x200
writable: true
virtual_address: 0x100
priv_mode: VS
length: VSXLEN
description: |
  The vsstatus register tracks and controls the hart's current operating state.

  It is VS-mode's version of `sstatus`, and substitutes for it when in VS-mode
  (_i.e._, in VS-mode CSR address 0x100 is `vsstatus`, not `sstatus`).

  Unlike the relationship between `sstatus` and `mstatus`, none of the bits in `vsstatus` are
  aliases of another field.
definedBy: H
fields:
  SD:
    location_rv64: 63
    location_rv32: 31
    description: |
      *State Dirty*

      Read-only bit that summarizes whether any of the
      `vsstatus.FS`, <% if ext?(:V) %> `vsstatus.VS`, <% end %> or `vsstatus.XS`
      fields signal the presence of some dirty state
      (_i.e._, any of them hold the value `11`).

      This bit is _not_ an alias of `mstatus.SD` since
      it only reflects the state visible to VS-mode
      (_e.g._, `status.FS` does not affect `vsstatus.SD`).
    type: RO-H
    reset_value: UNDEFINED_LEGAL
    affectedBy: [F, D, V]
  UXL:
    location: 33-32
    base: 64
    description: |
      *VU-mode XLEN*

      Sets the effective XLEN for VU-mode (0 = 32-bit, 1 = 64-bit, 2 = 128-bit).

      [when,"VUXLEN == 32"]
      Since the hart only supports VUXLEN==32, this is hardwired to 0.

      [when,"VUXLEN == 64"]
      Since the hart only supports VUXLEN==64, this is hardwired to 1.

    type(): |
      return (VUXLEN == 3264) ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return (VUXLEN == 3264) ? UNDEFINED_LEGAL : VUXLEN;
  MXR:
    alias: mstatus.MXR
    location: 19
    description: |
      *Make eXecutable Readable*

      Makes it possible to read executable pages when loading from effective VU-mode or VS-mode
      (normally, executable pages are not readable).

      * When 1, load in effective VU-mode or VS-mode from pages marked readable *or executable*
        are allowed as long as the page is marked readable (or executable and `status.MXR` is set) in the
        G-stage translation.
      * When 0, load in effective VU-mode or VS-mode from pages marked executable raise a
        Page Fault exception (unless `sstatus.MXR` is also set, in which case the above applies).

      'vsstatus.MXR' affects all loads that operate under effective VU-mode or VS-mode:

      * All loads in VU-mode
      * All loads in VS-mode
      * Loads in M-mode when `mstatus.MPRV` == 1, `mstatus.MPV` == 1, and `mstatus.MPP`[1] == 0
      * Loads generated by one of the `hlv.*` instructions.

    type: RW
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  SUM:
    location: 18
    description: |
      *permit Supervisor Memory Access*

      Allows VS-mode to read user pages.

      Applies to the following loads and stores:

      * All loads and stores in VS-mode.
      * All loads and stores in M-mode when `mstatus.MPRV` == 1, `mstatus.MPP` == 1, and `mstatus.MPV` == 1
      * Loads and stores generated by one of the `hlv.*`, `hlvx.*`, or `hsv.*` instructions.

      When `vsstatus.SUM` is 0, the loads and stores from the above categories cause an
      `Illegal Instruction` exception if they access a user page during VS-level translation.
      Otherwise, a load or store from the above categories is permitted to access a user page
      during VS-level translation.

    type: RW
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  XS:
    alias: mstatus.XS
    location: 16-15
    description: |
      *Custom (X) extension context Status*

      Summarizes the current state of any custom extension state.
      Either 0 - Off, 1 - Initial, 2 - Clean, 3 - Dirty.
      Since there are no custom extensions, this field is read-only 0.
    type: RO
    reset_value: 0
  FS:
    location: 14-13
    description: |
      *Floating point context status*

      When 0, floating point instructions (from F and D extensions) in VS-mode or VU-mode are disabled,
      and cause ILLEGAL INSTRUCTION exceptions.
      Floating point instructions in all modes, including VS-mode and VU-mode,
      are similarly disabled when `mstatus.FS` is clear.

      When a floating point register, or the `fcsr` register is written in VS-mode or VU-mode,
      `vsstatus.FS` is written with the value 3.

      Values 1 and 2 are valid write values for software, but are not interpreted by hardware
      other than to possibly enable a previously-disabled floating point unit.
    type: RW-H
    definedBy: F
    reset_value: UNDEFINED_LEGAL
  VS:
    location: 10-9
    description: |
      *Vector context status*

      When 0, vector instructions (from the V extension) are disabled, and cause ILLEGAL INSTRUCTION exceptions.
      When a vector register or vector CSR is written, VS obtains the value 3.
      Values 1 and 2 are valid write values for software, but are not interpreted by hardware
      other than to possibly enable a previously-disabled vector unit.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
    definedBy: V
  SPP:
    location: 8
    description: |
      *VS-mode Previous Privilege*

      Written with the prior nominal privilege level (_i.e._, 0 for VU-mode and 1 for VS-mode)
      when entering VS-mode from an exception/interrupt.
      Can also be written by software without immediate side-effect.

      On a return from an exception from VS-mode, the machine will enter the nominal privilege level
      stored in `vsstatus.SPP`.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  UBE:
    location: 6
    description: |
      *VU-mode Big Endian*

      Controls the endianness of VU-mode (0 = little, 1 = big).

      [when,"VU_MODE_ENDIANNESS == 'little'"]
      Since the CPU does not support big endian, this is hardwired to 0.

      [when,"VU_MODE_ENDIANNESS == 'big'"]
      Since the CPU does not support big endian, this is hardwired to 1.
    type(): |
      return (VU_MODE_ENDIANNESS == "dynamic") ? CsrFieldType::RW : CsrFieldType::RO;
    definedBy: S
    reset_value(): |
      if (VU_MODE_ENDIANNESS == "little") {
        # little endian
        return 0;
      } else if (VU_MODE_ENDIANNESS == "big") {
        # big endian
        return 1;
      } else {
        # mutable
        return UNDEFINED_LEGAL;
      }
  SPIE:
    location: 5
    description: |
      *VS-mode Previous Interrupt Enable*

      Written by hardware in two cases:

      * Written with prior value of `vsstatus.SIE` when entering VS-mode from an exception/interrupt.
      * Written with the value 1 when returning from an exception in VS-mode (via the `sret` instruction).

      Can also be written by software without immediate side effect.

      Other than serving as a record of nested traps as described above, `vsstatus.SPIE` does not affect execution.
    type: RW-H
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  SIE:
    location: 1
    description: |
      *VS-mode Interrupt Enable*

      Written by hardware in two cases:

      * Written with the value 0 when entering VS-mode from an exception/interrupt.
      * Written with the prior value of `vsstatus.SPIE` when returning from an exception in VS-mode (via `sret`).

      Affects execution by:

      * When 0, all VS-mode interrupts are disabled when the current privilege level is VS ((H)S-mode and M-mode interrupts are still enabled).
      * When 1, VS-mode interrupts that are not otherwise disabled with a field in `vsie` are enabled.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
