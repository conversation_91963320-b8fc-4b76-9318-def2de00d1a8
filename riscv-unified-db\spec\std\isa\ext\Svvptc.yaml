# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Svvptc
long_name: Guarantees visibility of PTE transitions from invalid to valid
description: |
  When the Svvptc extension is implemented, explicit stores by a hart that update
  the Valid bit of leaf and/or non-leaf PTEs from 0 to 1 and are visible to a hart
  will eventually become visible within a bounded timeframe to subsequent implicit
  accesses by that hart to such PTEs.

  [NOTE]
  Svvptc relieves an operating system from executing certain memory-management
  instructions, such as `SFENCE.VMA` or `SINVAL.VMA`, which would normally be used
  to synchronize the hart's address-translation caches when a memory-resident PTE
  is changed from Invalid to Valid. Synchronizing the hart's address-translation
  caches with other forms of updates to a memory-resident PTE, including when a
  PTE is changed from Valid to Invalid, requires the use of suitable
  memory-management instructions. Svvptc guarantees that a change to a PTE from
  Invalid to Valid is made visible within a bounded time, thereby making the
  execution of these memory-management instructions redundant. The performance
  benefit of eliding these instructions outweighs the cost of an occasional
  gratuitous additional page fault that may occur.+
  Depending on the microarchitecture, some possible ways to facilitate
  implementation of Svvptc include: not having any address-translation caches, not
  storing Invalid PTEs in the address-translation caches, automatically evicting
  Invalid PTEs using a bounded timer, or making address-translation caches
  coherent with store instructions that modify PTEs.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
