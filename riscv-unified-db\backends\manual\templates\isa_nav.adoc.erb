<%- manual_version.volumes.each do |volume| -%>
* <%= volume.title %>
<%- volume.chapters.each do |chapter| -%>
** xref:chapters:<%= File.basename(chapter.path) %>[<%= chapter.title %>]
<%- end -%>

<%- end -%>

* Alphabetical list of instructions
<%- manual_version.instructions.sort { |a, b| a.name <=> b.name }.each do |inst| -%>
** <%= link_to_udb_doc_inst(inst.name) %>
<%- end -%>

* Alphabetical list of CSRs
<%- manual_version.csrs.sort { |a, b| a.name <=> b.name }.each do |csr| -%>
** <%= link_to_udb_doc_csr(csr.name) %>
<%- end -%>

* Alphabetical list of extensions
<%- manual_version.extensions.sort { |a, b| a.name <=> b.name }.each do |ext| -%>
** <%= link_to_udb_doc_ext(ext.name) %>
<%- end -%>

* xref:params:param_list.adoc[Alphabetical list of parameters]

* xref:funcs:funcs.adoc[Execution functions (IDL)]
