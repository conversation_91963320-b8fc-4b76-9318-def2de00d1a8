// THIS FILE IS AUTOGENERATED

<%- enums = cfg_arch.global_ast.enums -%>
<%- symtab = cfg_arch.symtab -%>

#pragma once

#include <cstdint>
#include <concepts>
#include <string>

namespace udb {
  <%- enums.each do |enum| -%>
  <%- raise "TODO: handle enum with width > 64" if enum.type(symtab).width > 64 -%>
  <%- underlying_type = enum.type(symtab).width <= 32 ? "uint32_t" : "uint64_t" -%>
  struct <%= enum.name %> {
    using ValueType = <%= underlying_type %>;

    <%- element_names = enum.type(symtab).element_names -%>
    <%- element_values = enum.type(symtab).element_values -%>
    <%- element_names.each_index do |idx| -%>
    static constexpr <%= underlying_type %> <%= element_names[idx] %> = <%= element_values[idx] %>;
    <%- end -%>

    // constructors
    constexpr <%= enum.name %>() = default;
    constexpr <%= enum.name %>(const <%= underlying_type %>& value) : m_value(value) {}

    static const <%= enum.name %> from_s(const std::string& value);

    <%= underlying_type %> m_value;
    <%= underlying_type %> value() const { return m_value; }

    constexpr uint32_t size() const { return <%= element_names.size %>; }

    template <std::integral Type>
    constexpr operator Type() const { return m_value; }

    template <std::integral Type>
    constexpr <%= enum.name %>& operator<<(const Type& rhs) {
      m_value << rhs;
      return *this;
    }

    template <std::integral Type>
    constexpr bool operator==(const Type& rhs) const {
      return m_value == rhs;
    }

    constexpr bool operator==(const <%= enum.name %>& rhs) const {
      return m_value == rhs.m_value;
    }

    constexpr bool operator!=(const <%= enum.name %>& rhs) const {
      return m_value == rhs.m_value;
    }

    constexpr bool operator<(const <%= enum.name %>& rhs) const {
      return m_value < rhs.m_value;
    }

  };

  const std::string to_s(const <%= enum.name %>& value);

  template <std::integral Type>
  constexpr Type operator<<(const Type& lhs, const <%= enum.name %>& rhs) {
    return lhs << rhs.value();
  }
  template <std::integral Type>
  constexpr bool operator==(const Type& lhs, const <%= enum.name %>& rhs) {
    return lhs == rhs.value();
  }

  <%- end -%>
}
