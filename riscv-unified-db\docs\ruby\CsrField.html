<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: CsrField
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "CsrField";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (C)</a> &raquo;
    
    
    <span class="title">CsrField</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: CsrField
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></li>
          
            <li class="next">CsrField</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>A CSR field object</p>


  </div>
</div>
<div class="tags">
  

</div><h2>Defined Under Namespace</h2>
<p class="children">
  
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="CsrField/Alias.html" title="CsrField::Alias (class)">Alias</a></span>
    
  
</p>

  
    <h2>
      Constant Summary
      <small><a href="#" class="constants_summary_toggle">collapse</a></small>
    </h2>

    <dl class="constants">
      
        <dt id="TYPE_DESC_MAP-constant" class="">TYPE_DESC_MAP =
          
        </dt>
        <dd><pre class="code"><span class='lbrace'>{</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RO</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Only* Field has a hardwired value that does not change.
      Writes to an RO field are ignored.</span><span class='tstring_end'>)</span></span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RO-H</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Only with Hardware update*
      Writes are ignored.
      Reads reflect a value dynamically generated by hardware.</span><span class='tstring_end'>)</span></span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Write*
      Field is writable by software.
      Any value that fits in the field is acceptable and shall be retained for subsequent reads.</span><span class='tstring_end'>)</span></span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-R</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Write Restricted*
      Field is writable by software.
      Only certain values are legal.
      Writing an illegal value into the field is ignored, and the field retains its prior state.</span><span class='tstring_end'>)</span></span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-H</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Write with Hardware update*
      Field is writable by software.
      Any value that fits in the field is acceptable.
      Hardware also updates the field without an explicit software write.</span><span class='tstring_end'>)</span></span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-RH</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span>
    <span class='tstring'><span class='tstring_beg'>%(</span><span class='tstring_content'>*Read-Write Restricted with Hardware update*
      Field is writable by software.
      Only certain values are legal.
      Writing an illegal value into the field is ignored, such that the field retains its prior state.
      Hardware also updates the field without an explicit software write.</span><span class='tstring_end'>)</span></span>
<span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_freeze'>freeze</span></pre></dd>
      
    </dl>
  




  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#parent-instance_method" title="#parent (instance method)">#<strong>parent</strong>  &#x21d2; Csr </a>
    

    
      (also: #csr)
    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The Csr that defines this field.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#alias-instance_method" title="#alias (instance method)">#<strong>alias</strong>  &#x21d2; Alias<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The aliased field, or nil if there is no alias.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#arch_def-instance_method" title="#arch_def (instance method)">#<strong>arch_def</strong>  &#x21d2; ArchDef </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The owning ArchDef.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#base32_only%3F-instance_method" title="#base32_only? (instance method)">#<strong>base32_only?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this field only exists when XLEN == 32.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#base64_only%3F-instance_method" title="#base64_only? (instance method)">#<strong>base64_only?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this field only exists when XLEN == 64.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#defined_in_all_bases%3F-instance_method" title="#defined_in_all_bases? (instance method)">#<strong>defined_in_all_bases?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this field exists for any XLEN.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#dynamic_location%3F-instance_method" title="#dynamic_location? (instance method)">#<strong>dynamic_location?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not the location of the field changes dynamically (e.g., based on mstatus.SXL).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#has_custom_write%3F-instance_method" title="#has_custom_write? (instance method)">#<strong>has_custom_write?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>True if the field has a custom write function (i.e., ‘write(csr_value)` exists in the spec).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(parent_csr, field_data)  &#x21d2; CsrField </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of CsrField.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#location-instance_method" title="#location (instance method)">#<strong>location</strong>(effective_xlen = nil)  &#x21d2; Range </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The location within the CSR as a range (single bit fields will be a range of size 1).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#location_pretty-instance_method" title="#location_pretty (instance method)">#<strong>location_pretty</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Pretty-printed location string.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#reset_value-instance_method" title="#reset_value (instance method)">#<strong>reset_value</strong>  &#x21d2; Integer, String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#reset_value_func-instance_method" title="#reset_value_func (instance method)">#<strong>reset_value_func</strong>  &#x21d2; Idl::AstNode </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Abstract syntax tree of the reset_value function.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type-instance_method" title="#type (instance method)">#<strong>type</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The type of the field.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_desc-instance_method" title="#type_desc (instance method)">#<strong>type_desc</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Long description of the field type.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#width-instance_method" title="#width (instance method)">#<strong>width</strong>(effective_xlen)  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Number of bits in the field.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(parent_csr, field_data)  &#x21d2; <tt><span class='object_link'><a href="" title="CsrField (class)">CsrField</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of CsrField.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>parent_csr</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The Csr that defined this field</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>field_data</span>
      
      
        <span class='type'>(<tt>Hash&lt;String,Object&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Field data from the arch spec</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


86
87
88
89</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 86</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_parent_csr'>parent_csr</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
  <span class='ivar'>@parent</span> <span class='op'>=</span> <span class='id identifier rubyid_parent_csr'>parent_csr</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>
<div id="method_missing_details" class="method_details_list">
  <h2>Dynamic Method Handling</h2>
  <p class="notice super">
    This class handles dynamic methods through the <tt>method_missing</tt> method
    
      in the class <span class='object_link'><a href="ArchDefObject.html#method_missing-instance_method" title="ArchDefObject#method_missing (method)">ArchDefObject</a></span>
    
  </p>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="parent-instance_method">
  
    #<strong>parent</strong>  &#x21d2; <tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt>  <span class="extras">(readonly)</span>
  

  
    <span class="aliases">Also known as:
    <span class="names"><span id='csr-instance_method'>csr</span></span>
    </span>
  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The Csr that defines this field.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The Csr that defines this field</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


76
77
78</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 76</span>

<span class='kw'>def</span> <span class='id identifier rubyid_parent'>parent</span>
  <span class='ivar'>@parent</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="alias-instance_method">
  
    #<strong>alias</strong>  &#x21d2; <tt><span class='object_link'><a href="CsrField/Alias.html" title="CsrField::Alias (class)">Alias</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The aliased field, or nil if there is no alias.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="CsrField/Alias.html" title="CsrField::Alias (class)">Alias</a></span></tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The aliased field, or nil if there is no alias</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


157
158
159
160
161
162
163
164
165
166
167
168
169
170
171
172
173
174
175
176
177
178
179
180
181</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 157</span>

<span class='kw'>def</span> <span class='kw'>alias</span>
  <span class='kw'>return</span> <span class='ivar'>@alias</span> <span class='kw'>unless</span> <span class='ivar'>@alias</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>alias</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Can&#39;t parse alias</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_data'>data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>alias</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>=~</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>^[a-z][a-z0-9]+\.[A-Z0-9]+(\[([0-9]+)(:[0-9]+)?\])?$</span><span class='regexp_end'>/</span></span>

    <span class='id identifier rubyid_csr_name'>csr_name</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_csr_field'>csr_field</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_range'>range</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_range_start'>range_start</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>4</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_range_end'>range_end</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>5</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_csr_field'>csr_field</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_csr'>csr</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_field'>field</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_field'>csr_field</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_range'>range</span> <span class='op'>=</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
        <span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span>
      <span class='kw'>elsif</span> <span class='id identifier rubyid_range_end'>range_end</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
        <span class='lparen'>(</span><span class='id identifier rubyid_range_start'>range_start</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='op'>..</span><span class='id identifier rubyid_range_start'>range_start</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='rparen'>)</span>
      <span class='kw'>else</span>
        <span class='lparen'>(</span><span class='id identifier rubyid_range_start'>range_start</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='op'>..</span><span class='id identifier rubyid_range_end'>range_end</span><span class='lbracket'>[</span><span class='int'>1</span><span class='op'>..</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
    <span class='ivar'>@alias</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="CsrField/Alias.html" title="CsrField::Alias (class)">Alias</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="CsrField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_csr_field'>csr_field</span><span class='comma'>,</span> <span class='id identifier rubyid_range'>range</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@alias</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="arch_def-instance_method">
  
    #<strong>arch_def</strong>  &#x21d2; <tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The owning ArchDef.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The owning ArchDef</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


92
93
94</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 92</span>

<span class='kw'>def</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
  <span class='ivar'>@parent</span><span class='period'>.</span><span class='id identifier rubyid_arch_def'>arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="base32_only?-instance_method">
  
    #<strong>base32_only?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not this field only exists when XLEN == 32.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not this field only exists when XLEN == 32</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


274</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 274</span>

<span class='kw'>def</span> <span class='id identifier rubyid_base32_only?'>base32_only?</span> <span class='op'>=</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>32</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="base64_only?-instance_method">
  
    #<strong>base64_only?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not this field only exists when XLEN == 64.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not this field only exists when XLEN == 64</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


271</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 271</span>

<span class='kw'>def</span> <span class='id identifier rubyid_base64_only?'>base64_only?</span> <span class='op'>=</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>64</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="defined_in_all_bases?-instance_method">
  
    #<strong>defined_in_all_bases?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not this field exists for any XLEN.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not this field exists for any XLEN</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


277</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 277</span>

<span class='kw'>def</span> <span class='id identifier rubyid_defined_in_all_bases?'>defined_in_all_bases?</span> <span class='op'>=</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="dynamic_location?-instance_method">
  
    #<strong>dynamic_location?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the location of the field changes dynamically (e.g., based on mstatus.SXL).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the location of the field changes dynamically (e.g., based on mstatus.SXL)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


193
194
195
196
197</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 193</span>

<span class='kw'>def</span> <span class='id identifier rubyid_dynamic_location?'>dynamic_location?</span>
  <span class='kw'>return</span> <span class='kw'>false</span> <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>

  <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_dynamic_length?'>dynamic_length?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="has_custom_write?-instance_method">
  
    #<strong>has_custom_write?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns True if the field has a custom write function (i.e., ‘write(csr_value)` exists in the spec).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>True if the field has a custom write function (i.e., ‘write(csr_value)` exists in the spec)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


184
185
186</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 184</span>

<span class='kw'>def</span> <span class='id identifier rubyid_has_custom_write?'>has_custom_write?</span>
  <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>write(csr_value)</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='op'>!</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>write(csr_value)</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="location-instance_method">
  
    #<strong>location</strong>(effective_xlen = nil)  &#x21d2; <tt>Range</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the location within the CSR as a range (single bit fields will be a range of size 1).</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>effective_xlen</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The effective xlen, needed since some fields change location with XLEN. If the field location is not determined by XLEN, then this parameter can be nil</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Range</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the location within the CSR as a range (single bit fields will be a range of size 1)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


240
241
242
243
244
245
246
247
248
249
250
251
252
253
254
255
256
257
258
259
260
261
262
263
264
265
266
267
268</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 240</span>

<span class='kw'>def</span> <span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span> <span class='op'>=</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_key'>key</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Expecting 32 or 64</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='lbracket'>[</span><span class='int'>32</span><span class='comma'>,</span> <span class='int'>64</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>

      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location_rv</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Missing location for </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_key'>key</span><span class='embexpr_end'>}</span><span class='tstring_content'>)?</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='id identifier rubyid_key'>key</span><span class='rparen'>)</span>

  <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span> <span class='op'>&gt;</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span> <span class='op'>||</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Location (</span><span class='embexpr_beg'>#{</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>) is past the csr length (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>) in </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>

    <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='op'>..</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span>
  <span class='kw'>elsif</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_e'>e</span><span class='comma'>,</span> <span class='id identifier rubyid_s'>s</span> <span class='op'>=</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_split'>split</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:to_i</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Invalid location</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_s'>s</span> <span class='op'>&gt;</span> <span class='id identifier rubyid_e'>e</span>

    <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span> <span class='op'>&gt;</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Location (</span><span class='embexpr_beg'>#{</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>) is past the csr length (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>) in </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>

    <span class='id identifier rubyid_s'>s</span><span class='op'>..</span><span class='id identifier rubyid_e'>e</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="location_pretty-instance_method">
  
    #<strong>location_pretty</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Pretty-printed location string.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Pretty-printed location string</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


286
287
288
289
290
291
292
293
294
295
296
297
298
299
300
301
302
303
304
305
306
307
308
309
310</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 286</span>

<span class='kw'>def</span> <span class='id identifier rubyid_location_pretty'>location_pretty</span>
  <span class='id identifier rubyid_derangeify'>derangeify</span> <span class='op'>=</span> <span class='id identifier rubyid_proc'>proc</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_loc'>loc</span><span class='op'>|</span>
    <span class='kw'>return</span> <span class='id identifier rubyid_loc'>loc</span><span class='period'>.</span><span class='id identifier rubyid_min'>min</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span> <span class='kw'>if</span> <span class='id identifier rubyid_loc'>loc</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span>

    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_loc'>loc</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_loc'>loc</span><span class='period'>.</span><span class='id identifier rubyid_min'>min</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='rbrace'>}</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_dynamic_location?'>dynamic_location?</span>
    <span class='id identifier rubyid_condition'>condition</span> <span class='op'>=</span>
      <span class='kw'>case</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_priv_mode'>priv_mode</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>S</span><span class='tstring_end'>&quot;</span></span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[mstatus].SXL == %%</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VS</span><span class='tstring_end'>&quot;</span></span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[hstatus].VSXL == %%</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>else</span>
        <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected priv mode</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>end</span>

    <span class='heredoc_beg'>&lt;&lt;~LOC</span>
<span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_derangeify'>derangeify</span><span class='period'>.</span><span class='id identifier rubyid_call'>call</span><span class='lparen'>(</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='int'>32</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> when </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_condition'>condition</span><span class='period'>.</span><span class='id identifier rubyid_sub'>sub</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>%%</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>0</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_derangeify'>derangeify</span><span class='period'>.</span><span class='id identifier rubyid_call'>call</span><span class='lparen'>(</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='int'>64</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> when </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_condition'>condition</span><span class='period'>.</span><span class='id identifier rubyid_sub'>sub</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>%%</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>1</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    LOC
</span>  <span class='kw'>else</span>
    <span class='id identifier rubyid_derangeify'>derangeify</span><span class='period'>.</span><span class='id identifier rubyid_call'>call</span><span class='lparen'>(</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="reset_value-instance_method">
  
    #<strong>reset_value</strong>  &#x21d2; <tt>Integer</tt>, <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The reset value of this field</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The string ‘UNDEFINED_LEGAL’ if, for this config, there is no defined reset value</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


219
220
221
222
223
224
225
226
227
228
229
230
231
232
233
234
235
236</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 219</span>

<span class='kw'>def</span> <span class='id identifier rubyid_reset_value'>reset_value</span>
  <span class='kw'>return</span> <span class='ivar'>@reset_value</span> <span class='kw'>unless</span> <span class='ivar'>@reset_value</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_sym_table'>sym_table</span>

  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_push'>push</span> <span class='comment'># for consistency with template functions
</span>
  <span class='kw'>begin</span>
    <span class='ivar'>@reset_value</span> <span class='op'>=</span>
      <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reset_value</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
        <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reset_value</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='kw'>else</span>
        <span class='id identifier rubyid_reset_value_func'>reset_value_func</span><span class='period'>.</span><span class='id identifier rubyid_return_value'>return_value</span><span class='lparen'>(</span><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_sym_table'>sym_table</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
  <span class='kw'>ensure</span>
    <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="reset_value_func-instance_method">
  
    #<strong>reset_value_func</strong>  &#x21d2; <tt><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">Idl::AstNode</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Abstract syntax tree of the reset_value function.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">Idl::AstNode</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Abstract syntax tree of the reset_value function</p>
</div>
      
    </li>
  
</ul>
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>StandardError if there is no reset_value function (i.e., the reset value is static)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


***********
***********
***********
***********
***********</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 201</span>

<span class='kw'>def</span> <span class='id identifier rubyid_reset_value_func'>reset_value_func</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Not an IDL value</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reset_value()</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>

  <span class='kw'>return</span> <span class='ivar'>@reset_value_func</span> <span class='kw'>unless</span> <span class='ivar'>@reset_value_func</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@reset_value_func</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_func_body'>compile_func_body</span><span class='lparen'>(</span>
    <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reset_value()</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>return_type:</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='int'>64</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>symtab:</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_sym_table'>sym_table</span><span class='comma'>,</span>
    <span class='label'>name:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reset_value</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>parent:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>].</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>input_file:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>].</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>no_rescue:</span> <span class='kw'>true</span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type-instance_method">
  
    #<strong>type</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The type of the field. One of:</p>

<pre class="code ruby"><code class="ruby">&#39;RO&#39;    =&gt; Read-only
&#39;RO-H&#39;  =&gt; Read-only, with hardware update
&#39;RW&#39;    =&gt; Read-write
&#39;RW-R&#39;  =&gt; Read-write, with a restricted set of legal values
&#39;RW-H&#39;  =&gt; Read-write, with a hardware update
&#39;RW-RH&#39; =&gt; Read-write, with a hardware update and a restricted set of legal values.
</code></pre>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The type of the field. One of:</p>

<pre class="code ruby"><code class="ruby">&#39;RO&#39;    =&gt; Read-only
&#39;RO-H&#39;  =&gt; Read-only, with hardware update
&#39;RW&#39;    =&gt; Read-write
&#39;RW-R&#39;  =&gt; Read-write, with a restricted set of legal values
&#39;RW-H&#39;  =&gt; Read-write, with a hardware update
&#39;RW-RH&#39; =&gt; Read-write, with a hardware update and a restricted set of legal values
</code></pre>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141
142
143
144
145
146
147
148
149
150
151
152
153
154</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 104</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type'>type</span>
  <span class='kw'>return</span> <span class='ivar'>@type</span> <span class='kw'>unless</span> <span class='ivar'>@type</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@type</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
      <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
    <span class='kw'>else</span>
      <span class='comment'># the type is config-specific...
</span>      <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type()</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type() is nil for </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='ivar'>@data</span><span class='embexpr_end'>}</span><span class='tstring_content'>?</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_idl'>idl</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

      <span class='id identifier rubyid_expected_return_type'>expected_return_type</span> <span class='op'>=</span>
        <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:enum_ref</span><span class='comma'>,</span> <span class='label'>enum_class:</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_sym_table'>sym_table</span><span class='period'>.</span><span class='id identifier rubyid_get'>get</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CsrFieldType</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_sym_table'>sym_table</span> <span class='op'>=</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_sym_table'>sym_table</span>

      <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_func_body'>compile_func_body</span><span class='lparen'>(</span>
        <span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span>
        <span class='label'>symtab:</span> <span class='id identifier rubyid_sym_table'>sym_table</span><span class='comma'>,</span>
        <span class='label'>return_type:</span> <span class='id identifier rubyid_expected_return_type'>expected_return_type</span><span class='comma'>,</span>
        <span class='label'>name:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
        <span class='label'>parent:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
      <span class='rparen'>)</span>

      <span class='id identifier rubyid_sym_table'>sym_table</span><span class='period'>.</span><span class='id identifier rubyid_push'>push</span> <span class='comment'># for consistency with template functions
</span>
      <span class='kw'>begin</span>
        <span class='kw'>case</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_return_value'>return_value</span><span class='lparen'>(</span><span class='id identifier rubyid_sym_table'>sym_table</span><span class='rparen'>)</span>
        <span class='kw'>when</span> <span class='int'>0</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RO</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>when</span> <span class='int'>1</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RO-H</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>when</span> <span class='int'>2</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>when</span> <span class='int'>3</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-R</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>when</span> <span class='int'>4</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-H</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>when</span> <span class='int'>5</span>
          <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RW-RH</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>else</span>
          <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unhandled CsrFieldType value</span><span class='tstring_end'>&quot;</span></span>
        <span class='kw'>end</span>
      <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
        <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>In parsing </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>::type()</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>  Return of type() function cannot be evaluated at compile time</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span>
      <span class='kw'>ensure</span>
        <span class='id identifier rubyid_sym_table'>sym_table</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_desc-instance_method">
  
    #<strong>type_desc</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Long description of the field type.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Long description of the field type</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


343
344
345</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 343</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_desc'>type_desc</span>
  <span class='const'><span class='object_link'><a href="#TYPE_DESC_MAP-constant" title="CsrField::TYPE_DESC_MAP (constant)">TYPE_DESC_MAP</a></span></span><span class='lbracket'>[</span><span class='id identifier rubyid_type'>type</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="width-instance_method">
  
    #<strong>width</strong>(effective_xlen)  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Number of bits in the field.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>effective_xlen</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The effective xlen, needed since some fields change location with XLEN. If the field location is not determined by XLEN, then this parameter can be nil</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Number of bits in the field</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


281
282
283</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 281</span>

<span class='kw'>def</span> <span class='id identifier rubyid_width'>width</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>