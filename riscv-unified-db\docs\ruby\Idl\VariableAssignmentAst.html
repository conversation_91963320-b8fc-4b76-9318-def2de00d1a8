<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::VariableAssignmentAst
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::VariableAssignmentAst";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (V)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">VariableAssignmentAst</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::VariableAssignmentAst
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></li>
          
            <li class="next">Idl::VariableAssignmentAst</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb<span class="defines">,<br />
  lib/idl/passes/gen_adoc.rb</span>
</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>represents a variable assignment statement</p>

<p>for example, these will result in a VariableAssignmentAst</p>

<pre class="code ruby"><code class="ruby">X[rs1] = XLEN&#39;b0
CSR[mepc] = PC + 4
</code></pre>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#lhs-instance_method" title="#lhs (instance method)">#<strong>lhs</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute lhs.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#rhs-instance_method" title="#rhs (instance method)">#<strong>rhs</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute rhs.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#execute-instance_method" title="#execute (instance method)">#<strong>execute</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#gen_adoc-instance_method" title="#gen_adoc (instance method)">#<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(input, interval, lhs_ast, rhs_ast)  &#x21d2; VariableAssignmentAst </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of VariableAssignmentAst.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_idl-instance_method" title="#to_idl (instance method)">#<strong>to_idl</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return valid IDL representation of the node (and its subtree).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check this node and all children.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  
  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(input, interval, lhs_ast, rhs_ast)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::VariableAssignmentAst (class)">VariableAssignmentAst</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of VariableAssignmentAst.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


986
987
988
989
990</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 986</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='id identifier rubyid_lhs_ast'>lhs_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_rhs_ast'>rhs_ast</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='lbracket'>[</span><span class='id identifier rubyid_lhs_ast'>lhs_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_rhs_ast'>rhs_ast</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='ivar'>@lhs</span> <span class='op'>=</span> <span class='id identifier rubyid_lhs_ast'>lhs_ast</span>
  <span class='ivar'>@rhs</span> <span class='op'>=</span> <span class='id identifier rubyid_rhs_ast'>rhs_ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="lhs-instance_method">
  
    #<strong>lhs</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute lhs.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


984
985
986</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 984</span>

<span class='kw'>def</span> <span class='id identifier rubyid_lhs'>lhs</span>
  <span class='ivar'>@lhs</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="rhs-instance_method">
  
    #<strong>rhs</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute rhs.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


984
985
986</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 984</span>

<span class='kw'>def</span> <span class='id identifier rubyid_rhs'>rhs</span>
  <span class='ivar'>@rhs</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="execute-instance_method">
  
    #<strong>execute</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1001
1002
1003
1004
1005
1006
1007
1008
1009
1010
1011</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1001</span>

<span class='kw'>def</span> <span class='id identifier rubyid_execute'>execute</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="CsrWriteAst.html" title="Idl::CsrWriteAst (class)">CsrWriteAst</a></span></span><span class='rparen'>)</span>
    <span class='id identifier rubyid_value_error'>value_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR writes are never compile-time-known</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_variable'>variable</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_get'>get</span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_internal_error'>internal_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No variable </span><span class='embexpr_beg'>#{</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_variable'>variable</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

    <span class='id identifier rubyid_variable'>variable</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span> <span class='op'>=</span> <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="gen_adoc-instance_method">
  
    #<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


134
135
136</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/gen_adoc.rb', line 134</span>

<span class='kw'>def</span> <span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='id identifier rubyid_indent'>indent</span> <span class='op'>=</span> <span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span> <span class='int'>2</span><span class='rparen'>)</span>
  <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_idl-instance_method">
  
    #<strong>to_idl</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return valid IDL representation of the node (and its subtree)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL code for the node</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1014</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1014</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_idl'>to_idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'> = </span><span class='embexpr_beg'>#{</span><span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>type check this node and all children</p>

<p>Calls to #type and/or #value may depend on type_check being called first with the same symtab. If not, those functions may raise an AstNode::InternalError</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is a type error</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is an internal compiler error during type check</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


993
994
995
996
997
998
999</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 993</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>unless</span> <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Incompatible type in assignment (</span><span class='embexpr_beg'>#{</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>, </span><span class='embexpr_beg'>#{</span><span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:45 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>