# Copyright (c) <PERSON><PERSON>
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zclsd
conflicts: Zcf
long_name: Compressed Load/Store Pair for RV32
description: |
  This extension adds load and store instructions using register pairs. It does so by reusing existing instruction encodings which are RV64-only. The specification defines 16-bit encodings.
  Load and store instructions will use the same definition of even-odd pairs as defined by the Zdinx extension.
  The extension improves static code density, by replacing two separate load or store instructions with a single one. In addition, it can provide a performance improvement for implementations that can make use of a wider than XLEN memory interface.
type: unprivileged
versions:
  - version: "1.0"
    state: ratified
    ratification_date: "2025-02"
    requires:
      allOf: [ Zilsd, Zca ]
