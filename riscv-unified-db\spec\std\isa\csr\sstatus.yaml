# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: sstatus
long_name: Supervisor Status
address: 0x100
writable: true
priv_mode: S
length: SXLEN
description: |
  The sstatus register tracks and controls the hart's current operating state.

  All fields in sstatus are aliases of the same field in mstatus.
definedBy: S
fields:
  SD:
    # The *position* of SD changes when SXLEN changes (yuck^[TM])
    location_rv32: 31
    location_rv64: 63
    alias: mstatus.SD
    description: |
      *State Dirty*

      Alias of `mstatus.SD`.

    type: RO-H
    reset_value: UNDEFINED_LEGAL
    affectedBy: [F, D, V]
  UXL:
    location: 33-32
    base: 64
    alias: mstatus.MXR
    description: |
      *U-mode XLEN*

      Alias of `mstatus.UXL`.

    type: RO
    reset_value: UNDEFINED_LEGAL
  MXR:
    alias: mstatus.MXR
    location: 19
    description: |
      *Make eXecutable Readable*

      Alias of `mstatus.MXR`.

    type: RW
    reset_value: UNDEFINED_LEGAL
  SUM:
    alias: mstatus.SUM
    location: 18
    description: |
      *permit Supervisor Memory Access*

      Alias of `mstatus.SUM`.

    type: RW
    reset_value: UNDEFINED_LEGAL
  XS:
    alias: mstatus.XS
    location: 16-15
    description: |
      Custom (X) extension context Status.

      Alias of `mstatus.XS`.

    type: RO
    reset_value: UNDEFINED_LEGAL
  FS:
    alias: mstatus.FS
    location: 14-13
    description: |
      Floating point context status.

      Alias of `mstatus.FS`.

    type: RW-H
    definedBy: F
    reset_value: UNDEFINED_LEGAL
  VS:
    alias: mstatus.VS
    location: 10-9
    description: |
      Vector context status.

      Alias of `mstatus.VS`.

    type: RW-H
    reset_value: UNDEFINED_LEGAL
    definedBy: V
  SPP:
    alias: mstatus.SPP
    location: 8
    description: |
      *S-mode Previous Privilege*

      Alias of `mstatus.SPP`.
    type: RW-H
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  UBE:
    alias: mstatus.UBE
    location: 6
    description: |
      *U-mode Big Endian*

      Alias of `mstatus.UBE`.
    type: RO
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  SPIE:
    alias: mstatus.SPIE
    location: 5
    description: |
      *S-mode Previous Interrupt Enable*

      Alias of `mstatus.SPIE`.

    type: RW-H
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  SIE:
    alias: mstatus.SIE
    location: 1
    description: |
      *S-mode Interrupt Enable*

      Alias of `mstatus.SIE`.

    type: RW-H
    reset_value: UNDEFINED_LEGAL
