# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fadd.q
long_name: Floating-point Add Quad-precision
description:
  - id: inst-fadd.q-behaviour
    normative: false
    text: |
      `fadd.q` is analogous to `fadd.d` and performs double-precision floating-point addition between
      `qs1` and `qs2` and writes the final result to `qd`.
definedBy: Q
assembly: fd, fs1, fs2, rm
encoding:
  match: 0000011------------------1010011
  variables:
    - name: fs2
      location: 24-20
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
