# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Shcounterenw
long_name: Hypervisor counter enable
description: |
  For any hpmcounter that is not read-only zero, the corresponding bit in `hcounteren` must be writable.

  [NOTE]
  This extension was ratified with the RVA22 profiles.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-08
    url: https://drive.google.com/file/d/1KcjgbLM5L1ZKY8934aJl8aQwGlMz6Cbo/view?usp=drive_link
    param_constraints:
      HCOUNTENABLE_EN:
        extra_validation: |
          HPM_COUNTER_EN.each_with_index { |hpm_exists, idx| assert(!hpm_exists || HCOUNTENABLE_EN[idx]) }
