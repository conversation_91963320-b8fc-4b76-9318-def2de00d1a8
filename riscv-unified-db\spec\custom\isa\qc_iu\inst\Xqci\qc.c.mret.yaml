# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.c.mret
long_name: Machine Exception Return
description: |
  Returns from an exception in M-mode.
  Instruction encoded in CI instruction format.
assembly: ""
definedBy:
  anyOf:
    - Xqci
    - Xqciint
access:
  s: never
  u: never
  vs: never
  vu: never
base: 32
encoding:
  match: "0001100100010010"
operation(): |
  XReg qc_mcause_val = CSR[qc.mcause].sw_read();
  XReg qc_mcause_val_masked = qc_mcause_val & ~(32'b1<<26) & ~(32'b1<<27) & ~(32'b1<<29) & ~(32'hFF<<12);
  Bits<1> mpie_val = (qc_mcause_val >> 27) & 1;
  Bits<1> mpdt_val = (qc_mcause_val >> 29) & 1;
  Bits<4> mpil_val = (qc_mcause_val >> 16) & 0xF;
  CSR[mstatus].MIE = mpie_val;
  CSR[mstatus].MPIE = 1'b1;
  if (implemented?(ExtensionName::Smdbltrp)) {
    CSR[mstatush].MDT = mpdt_val;
  }
  CSR[qc.mcause].sw_write(qc_mcause_val_masked |
                          (32'b1<<27) | (mpie_val`<<26) | (32'b0<<29) |
                          (mpil_val `<< 12) | (32'b1111 << 16));
  if (mpdt_val == 1'b0) {
    if (CSR[mstatus].MPP != 2'b11) {
      CSR[mstatus].MPRV = 0;
    }
    if (CSR[mstatus].MPP == 2'b00) {
      set_mode(PrivilegeMode::U);
    } else if (CSR[mstatus].MPP == 2'b01) {
      set_mode(PrivilegeMode::S);
    } else if (CSR[mstatus].MPP == 2'b11) {
      set_mode(PrivilegeMode::M);
    }
    CSR[mstatus].MPP = implemented?(ExtensionName::U) ? 2'b00 : 2'b11;
  }
  $pc = CSR[mepc].sw_read();
