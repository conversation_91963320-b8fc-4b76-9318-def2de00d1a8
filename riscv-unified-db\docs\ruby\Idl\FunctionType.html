<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::FunctionType
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::FunctionType";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (F)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">FunctionType</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::FunctionType
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></li>
          
            <li class="next">Idl::FunctionType</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/type.rb</dd>
  </dl>
  
</div>








  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#apply_arguments-instance_method" title="#apply_arguments (instance method)">#<strong>apply_arguments</strong>(symtab, argument_nodes, call_site_symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>apply the arguments as Vars.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#apply_template_values-instance_method" title="#apply_template_values (instance method)">#<strong>apply_template_values</strong>(template_values = [])  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#argument_name-instance_method" title="#argument_name (instance method)">#<strong>argument_name</strong>(index, template_values = [])  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#argument_type-instance_method" title="#argument_type (instance method)">#<strong>argument_type</strong>(index, template_values, argument_nodes, call_site_symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#body-instance_method" title="#body (instance method)">#<strong>body</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#builtin%3F-instance_method" title="#builtin? (instance method)">#<strong>builtin?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#clone-instance_method" title="#clone (instance method)">#<strong>clone</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(func_name, func_def_ast, symtab)  &#x21d2; FunctionType </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of FunctionType.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#num_args-instance_method" title="#num_args (instance method)">#<strong>num_args</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#return_type-instance_method" title="#return_type (instance method)">#<strong>return_type</strong>(template_values)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>return [Type] type of the call return.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#return_types-instance_method" title="#return_types (instance method)">#<strong>return_types</strong>(template_values, argument_nodes, call_site_symtab)  &#x21d2; Array&lt;Type&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return types.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#return_value-instance_method" title="#return_value (instance method)">#<strong>return_value</strong>(template_values, argument_nodes, call_site_symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#template_names-instance_method" title="#template_names (instance method)">#<strong>template_names</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#template_types-instance_method" title="#template_types (instance method)">#<strong>template_types</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#templated%3F-instance_method" title="#templated? (instance method)">#<strong>templated?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check_call-instance_method" title="#type_check_call (instance method)">#<strong>type_check_call</strong>(template_values = [])  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(func_name, func_def_ast, symtab)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::FunctionType (class)">FunctionType</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of FunctionType.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


432
433
434
435
436
437
438</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 432</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_func_name'>func_name</span><span class='comma'>,</span> <span class='id identifier rubyid_func_def_ast'>func_def_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='symbol'>:function</span><span class='comma'>,</span> <span class='label'>name:</span> <span class='id identifier rubyid_func_name'>func_name</span><span class='rparen'>)</span>
  <span class='ivar'>@func_def_ast</span> <span class='op'>=</span> <span class='id identifier rubyid_func_def_ast'>func_def_ast</span>
  <span class='ivar'>@symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>symtab should be at level 1</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>==</span> <span class='int'>1</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="apply_arguments-instance_method">
  
    #<strong>apply_arguments</strong>(symtab, argument_nodes, call_site_symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>apply the arguments as Vars. then add the value to the Var</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


493
494
495
496
497
498
499
500
501
502
503</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 493</span>

<span class='kw'>def</span> <span class='id identifier rubyid_apply_arguments'>apply_arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_idx'>idx</span> <span class='op'>=</span> <span class='int'>0</span>
  <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_arguments'>arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_atype'>atype</span><span class='comma'>,</span> <span class='id identifier rubyid_aname'>aname</span><span class='op'>|</span>
    <span class='kw'>begin</span>
      <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add!'>add!</span><span class='lparen'>(</span><span class='id identifier rubyid_aname'>aname</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_aname'>aname</span><span class='comma'>,</span> <span class='id identifier rubyid_atype'>atype</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='lbracket'>[</span><span class='id identifier rubyid_idx'>idx</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span>
      <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add!'>add!</span><span class='lparen'>(</span><span class='id identifier rubyid_aname'>aname</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_aname'>aname</span><span class='comma'>,</span> <span class='id identifier rubyid_atype'>atype</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_idx'>idx</span> <span class='op'>+=</span> <span class='int'>1</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="apply_template_values-instance_method">
  
    #<strong>apply_template_values</strong>(template_values = [])  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


471
472
473
474
475
476
477
478
479
480
481
482
483
484
485
486
487
488
489</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 471</span>

<span class='kw'>def</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Missing template values</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_templated?'>templated?</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_template_values'>template_values</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>wrong number of template values</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_template_names'>template_names</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='id identifier rubyid_template_values'>template_values</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>

  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='ivar'>@symtab</span><span class='period'>.</span><span class='id identifier rubyid_deep_clone'>deep_clone</span>
  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span> <span class='kw'>while</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>!=</span> <span class='int'>1</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Symbol table should be at global scope</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>==</span> <span class='int'>1</span>

  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_push'>push</span>

  <span class='id identifier rubyid_template_values'>template_values</span><span class='period'>.</span><span class='id identifier rubyid_each_with_index'>each_with_index</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_value'>value</span><span class='comma'>,</span> <span class='id identifier rubyid_idx'>idx</span><span class='op'>|</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>template value should be an Integer</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add!'>add!</span><span class='lparen'>(</span><span class='id identifier rubyid_template_names'>template_names</span><span class='lbracket'>[</span><span class='id identifier rubyid_idx'>idx</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_template_names'>template_names</span><span class='lbracket'>[</span><span class='id identifier rubyid_idx'>idx</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_template_types'>template_types</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='lbracket'>[</span><span class='id identifier rubyid_idx'>idx</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_value'>value</span><span class='comma'>,</span> <span class='label'>template_index:</span> <span class='id identifier rubyid_idx'>idx</span><span class='comma'>,</span> <span class='label'>function_name:</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_symtab'>symtab</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="argument_name-instance_method">
  
    #<strong>argument_name</strong>(index, template_values = [])  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


541
542
543
544
545
546
547
548
549</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 541</span>

<span class='kw'>def</span> <span class='id identifier rubyid_argument_name'>argument_name</span><span class='lparen'>(</span><span class='id identifier rubyid_index'>index</span><span class='comma'>,</span> <span class='id identifier rubyid_template_values'>template_values</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='kw'>nil</span> <span class='kw'>if</span> <span class='id identifier rubyid_index'>index</span> <span class='op'>&gt;=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_num_args'>num_args</span>

  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='comment'># apply_arguments(symtab, argument_nodes, call_site_symtab)
</span>
  <span class='id identifier rubyid_arguments'>arguments</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_arguments'>arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_arguments'>arguments</span><span class='lbracket'>[</span><span class='id identifier rubyid_index'>index</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="argument_type-instance_method">
  
    #<strong>argument_type</strong>(index, template_values, argument_nodes, call_site_symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


531
532
533
534
535
536
537
538
539</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 531</span>

<span class='kw'>def</span> <span class='id identifier rubyid_argument_type'>argument_type</span><span class='lparen'>(</span><span class='id identifier rubyid_index'>index</span><span class='comma'>,</span> <span class='id identifier rubyid_template_values'>template_values</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='kw'>nil</span> <span class='kw'>if</span> <span class='id identifier rubyid_index'>index</span> <span class='op'>&gt;=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_num_args'>num_args</span>

  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_apply_arguments'>apply_arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_arguments'>arguments</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_arguments'>arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_arguments'>arguments</span><span class='lbracket'>[</span><span class='id identifier rubyid_index'>index</span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="body-instance_method">
  
    #<strong>body</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


551</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 551</span>

<span class='kw'>def</span> <span class='id identifier rubyid_body'>body</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_body'>body</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="builtin?-instance_method">
  
    #<strong>builtin?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


444</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 444</span>

<span class='kw'>def</span> <span class='id identifier rubyid_builtin?'>builtin?</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_builtin?'>builtin?</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="clone-instance_method">
  
    #<strong>clone</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


440
441
442</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 440</span>

<span class='kw'>def</span> <span class='id identifier rubyid_clone'>clone</span>
  <span class='const'><span class='object_link'><a href="" title="Idl::FunctionType (class)">FunctionType</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::FunctionType#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='ivar'>@func_def_ast</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="num_args-instance_method">
  
    #<strong>num_args</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


446</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 446</span>

<span class='kw'>def</span> <span class='id identifier rubyid_num_args'>num_args</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_num_args'>num_args</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="return_type-instance_method">
  
    #<strong>return_type</strong>(template_values)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>return [Type] type of the call return</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>template_values</span>
      
      
        <span class='type'>(<tt>Array&lt;Integer&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Template values for the call, in declaration order</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>call_site_symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table at the call site</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


508
509
510
511
512
513</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 508</span>

<span class='kw'>def</span> <span class='id identifier rubyid_return_type'>return_type</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='comment'># apply_arguments(symtab, argument_nodes, call_site_symtab)
</span>
  <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_return_type'>return_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="return_types-instance_method">
  
    #<strong>return_types</strong>(template_values, argument_nodes, call_site_symtab)  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return types</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>template_values</span>
      
      
        <span class='type'>(<tt>Array&lt;Integer&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Template values to apply, required if <span class='object_link'><a href="#templated%3F-instance_method" title="Idl::FunctionType#templated? (method)">#templated?</a></span></p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>return types</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


524
525
526
527
528
529</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 524</span>

<span class='kw'>def</span> <span class='id identifier rubyid_return_types'>return_types</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_apply_arguments'>apply_arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>

  <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_return_types'>return_types</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:clone</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="return_value-instance_method">
  
    #<strong>return_value</strong>(template_values, argument_nodes, call_site_symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


515
516
517
518
519
520</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 515</span>

<span class='kw'>def</span> <span class='id identifier rubyid_return_value'>return_value</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_apply_arguments'>apply_arguments</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='id identifier rubyid_argument_nodes'>argument_nodes</span><span class='comma'>,</span> <span class='id identifier rubyid_call_site_symtab'>call_site_symtab</span><span class='rparen'>)</span>

  <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_body'>body</span><span class='period'>.</span><span class='id identifier rubyid_return_value'>return_value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="template_names-instance_method">
  
    #<strong>template_names</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


465</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 465</span>

<span class='kw'>def</span> <span class='id identifier rubyid_template_names'>template_names</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_template_names'>template_names</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="template_types-instance_method">
  
    #<strong>template_types</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


467</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 467</span>

<span class='kw'>def</span> <span class='id identifier rubyid_template_types'>template_types</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_template_types'>template_types</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="templated?-instance_method">
  
    #<strong>templated?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


469</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 469</span>

<span class='kw'>def</span> <span class='id identifier rubyid_templated?'>templated?</span> <span class='op'>=</span> <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_templated?'>templated?</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check_call-instance_method">
  
    #<strong>type_check_call</strong>(template_values = [])  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


448
449
450
451
452
453
454
455
456
457
458
459
460
461
462
463</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 448</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check_call'>type_check_call</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Missing template values</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_templated?'>templated?</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_template_values'>template_values</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

  <span class='kw'>if</span> <span class='id identifier rubyid_templated?'>templated?</span>
    <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_apply_template_values'>apply_template_values</span><span class='lparen'>(</span><span class='id identifier rubyid_template_values'>template_values</span><span class='rparen'>)</span>

    <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_type_check_template_instance'>type_check_template_instance</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_symtab'>symtab</span> <span class='op'>=</span> <span class='ivar'>@symtab</span><span class='period'>.</span><span class='id identifier rubyid_deep_clone'>deep_clone</span>
    <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span> <span class='kw'>while</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>!=</span> <span class='int'>1</span>

    <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_push'>push</span> <span class='comment'># to keep things consistent with template functions, push a scope
</span>
    <span class='ivar'>@func_def_ast</span><span class='period'>.</span><span class='id identifier rubyid_type_check_from_call'>type_check_from_call</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>