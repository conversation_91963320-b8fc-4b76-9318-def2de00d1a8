# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.psyscalli
long_name: System call with immediate argument pseudo-instruction (hint) working only in simulation environment
description: |
  The System call instruction calls simulation environment with unsigned `imm` explicit argument.
  Additional implicit arguments defined by simulation environment implementation.
  Instruction is used to call simulator to execute function according to the argument.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: " imm"
base: 32
encoding:
  match: 00----------00000010000000010011
  variables:
    - name: imm
      location: 29-20
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 11;
  XReg arg = imm;
  iss_syscall(func,arg);
