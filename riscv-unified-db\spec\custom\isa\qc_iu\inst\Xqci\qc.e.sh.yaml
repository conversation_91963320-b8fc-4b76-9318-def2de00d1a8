# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.sh
long_name: Store halfword
description: |
  Store 16 bits of data from register `rs2` to an
  address formed by adding `rs1` to a signed offset `imm`.
  Instruction encoded in QC.ES instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilo
assembly: " xs2, imm(xs1)"
base: 32
encoding:
  match: ----------------10---------------110-----0011111
  variables:
    - name: imm
      location: 47-32|29-25|11-7
    - name: rs2
      location: 24-20
    - name: rs1
      location: 19-15
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg virtual_address = X[rs1] + $signed(imm);
  write_memory<16>(virtual_address, X[rs2][15:0], $encoding);
