# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/mhpmeventNh.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mhpmevent6h
long_name: Machine Hardware Performance Counter 6 Control, High half
address: 0x726
priv_mode: M
length: 32
base: 32
description: |
  Alias of `mhpmevent6`[63:32].

  Introduced with the `Sscofpmf` extension. Prior to that, there was no way to access the upper
  32-bits of `mhpmevent#{hpm_num}`.
definedBy: Sscofpmf
fields:
  OF:
    location: 31
    alias: mhpmevent6.OF
    description: |
      Alias of mhpmevent6.OF.
    type(): |
      if (HPM_COUNTER_EN[6]) {
        return CsrFieldType::RWH;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[6]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  MINH:
    location: 30
    alias: mhpmevent6.MINH
    description: |
      Alias of mhpmevent6.MINH.
    type(): |
      if (HPM_COUNTER_EN[6]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[6]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  SINH:
    location: 29
    alias: mhpmevent6.SINH
    description: |
      Alias of mhpmevent6.SINH.
    type(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::S) && (CSR[misa].S == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::S)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  UINH:
    location: 28
    alias: mhpmevent6.UINH
    description: |
      Alias of mhpmevent6.UINH.
    type(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::U) && (CSR[misa].U == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::U)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  VSINH:
    location: 27
    alias: mhpmevent6.VSINH
    description: |
      Alias of mhpmevent6.VSINH.
    type(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::H) && (CSR[misa].H == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::H)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  VUINH:
    location: 26
    alias: mhpmevent6.VUINH
    description: |
      Alias of mhpmevent6.VUINH.
    type(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::H) && (CSR[misa].H == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[6]) && implemented?(ExtensionName::H)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  EVENT:
    location: 25-0
    description: High part of event selector for performance counter `mhpmcounter6`.
    alias: mhpmevent6.EVENT[57:32]
    type(): |
      if (HPM_COUNTER_EN[6]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[6]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
