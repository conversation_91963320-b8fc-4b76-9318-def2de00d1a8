# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl03
long_name: IRQ Level 3
address: 0xbc3
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 24-31
fields:
  IRQ24:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ24 level
  IRQ25:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ25 level
  IRQ26:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ26 level
  IRQ27:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ27 level
  IRQ28:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ28 level
  IRQ29:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ29 level
  IRQ30:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ30 level
  IRQ31:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ31 level
