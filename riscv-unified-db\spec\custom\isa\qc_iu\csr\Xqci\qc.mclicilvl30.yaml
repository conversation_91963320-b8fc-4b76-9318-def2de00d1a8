# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl30
long_name: IRQ Level 30
address: 0xbde
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 240-247
fields:
  IRQ240:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ240 level
  IRQ241:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ241 level
  IRQ242:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ242 level
  IRQ243:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ243 level
  IRQ244:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ244 level
  IRQ245:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ245 level
  IRQ246:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ246 level
  IRQ247:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ247 level
