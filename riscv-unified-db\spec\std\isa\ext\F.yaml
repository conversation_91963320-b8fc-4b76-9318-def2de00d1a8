# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: F
type: unprivileged
long_name: Single-precision floating-point
versions:
  - version: "2.2.0"
    state: ratified
    ratification_date: 2019-12
    changes:
      - Define NaN-boxing scheme, changed definition of FMAX and FMIN
    requires: Zicsr
description: |
  This chapter describes the standard instruction-set extension for
  single-precision floating-point, which is named "F" and adds
  single-precision floating-point computational instructions compliant
  with the IEEE 754-2008 arithmetic standard cite:[ieee754-2008]. The F extension depends on the "Zicsr" extension for control and status register access.

  = F Register State

  The F extension adds 32 floating-point registers, `f0-f31`, each 32
  bits wide, and a floating-point control and status register `fcsr`,
  which contains the operating mode and exception status of the
  floating-point unit. This additional state is shown in
  <<fprs>>. We use the term FLEN to describe the width of
  the floating-point registers in the RISC-V ISA, and FLEN=32 for the F
  single-precision floating-point extension. Most floating-point
  instructions operate on values in the floating-point register file.
  Floating-point load and store instructions transfer floating-point
  values between registers and memory. Instructions to transfer values to and from the integer register file are also provided.

  [TIP]
  ====
  We considered a unified register file for both integer and
  floating-point values as this simplifies software register allocation
  and calling conventions, and reduces total user state. However, a split
  organization increases the total number of registers accessible with a
  given instruction width, simplifies provision of enough regfile ports
  for wide superscalar issue, supports decoupled floating-point-unit
  architectures, and simplifies use of internal floating-point encoding
  techniques. Compiler support and calling conventions for split register
  file architectures are well understood, and using dirty bits on
  floating-point register file state can reduce context-switch overhead.
  ====

  [[fprs]]
  .RISC-V standard F extension single-precision floating-point state
  [cols="<,^,>",options="header",width="50%",align="center",grid="rows"]
  |===
  | [.small]#FLEN-1#| >| [.small]#0#
  3+^| [.small]#f0#
  3+^| [.small]#f1#
  3+^| [.small]#f2#
  3+^| [.small]#f3#
  3+^| [.small]#f4#
  3+^| [.small]#f5#
  3+^| [.small]#f6#
  3+^| [.small]#f7#
  3+^| [.small]#f8#
  3+^| [.small]#f9#
  3+^| [.small]#f10#
  3+^| [.small]#f11#
  3+^| [.small]#f12#
  3+^| [.small]#f13#
  3+^| [.small]#f14#
  3+^| [.small]#f15#
  3+^| [.small]#f16#
  3+^| [.small]#f17#
  3+^| [.small]#f18#
  3+^| [.small]#f19#
  3+^| [.small]#f20#
  3+^| [.small]#f21#
  3+^| [.small]#f22#
  3+^| [.small]#f23#
  3+^| [.small]#f24#
  3+^| [.small]#f25#
  3+^| [.small]#f26#
  3+^| [.small]#f27#
  3+^| [.small]#f28#
  3+^| [.small]#f29#
  3+^| [.small]#f30#
  3+^| [.small]#f31#
  3+^| [.small]#FLEN#
  | [.small]#31#| >| [.small]#0#
  3+^|  [.small]#fcsr#
  3+^| [.small]#32#
  |===

  == Floating-Point Control and Status Register

  The floating-point control and status register, `fcsr`, is a RISC-V
  control and status register (CSR). It is a 32-bit read/write register
  that selects the dynamic rounding mode for floating-point arithmetic
  operations and holds the accrued exception flags, as shown in <<fcsr>>.

  [[fcsr, Floating-Point Control and Status Register]]
  .Floating-point control and status register
  include::images/wavedrom/float-csr.adoc[]

  The `fcsr` register can be read and written with the FRCSR and FSCSR
  instructions, which are assembler pseudoinstructions built on the
  underlying CSR access instructions. FRCSR reads `fcsr` by copying it
  into integer register _rd_. FSCSR swaps the value in `fcsr` by copying
  the original value into integer register _rd_, and then writing a new
  value obtained from integer register _rs1_ into `fcsr`.

  The fields within the `fcsr` can also be accessed individually through
  different CSR addresses, and separate assembler pseudoinstructions are defined
  for these accesses. The FRRM instruction reads the Rounding Mode field `frm`
  (`fcsr` bits 7--5) and copies it into the least-significant three bits of
  integer register _rd_, with zero in all other bits. FSRM swaps the value in
  `frm` by copying the original value into integer register _rd_, and then
  writing a new value obtained from the three least-significant bits of integer
  register _rs1_ into `frm`. FRFLAGS and FSFLAGS are defined analogously for the
  Accrued Exception Flags field `fflags` (`fcsr` bits 4--0).

  Bits 31--8 of the `fcsr` are reserved for other standard extensions. If
  these extensions are not present, implementations shall ignore writes to
  these bits and supply a zero value when read. Standard software should
  preserve the contents of these bits.

  Floating-point operations use either a static rounding mode encoded in
  the instruction, or a dynamic rounding mode held in `frm`. Rounding
  modes are encoded as shown in <<rm>>. A value of 111 in the
  instruction's _rm_ field selects the dynamic rounding mode held in
  `frm`. The behavior of floating-point instructions that depend on
  rounding mode when executed with a reserved rounding mode is _reserved_, including both static reserved rounding modes (101-110) and dynamic reserved rounding modes (101-111). Some instructions, including widening conversions, have the _rm_ field but are nevertheless mathematically unaffected by the rounding mode; software should set their _rm_ field to
  RNE (000) but implementations must treat the _rm_ field as usual (in
  particular, with regard to decoding legal vs. reserved encodings).

  [[rm]]
  .Rounding mode encoding.
  [%autowidth,float="center",align="center",cols="^,^,<",options="header"]
  |===
  |Rounding Mode |Mnemonic |Meaning
  |000 |RNE |Round to Nearest, ties to Even
  |001 |RTZ |Round towards Zero
  |010 |RDN |Round Down (towards latexmath:[$-\infty$])
  |011 |RUP |Round Up (towards latexmath:[$+\infty$])
  |100 |RMM |Round to Nearest, ties to Max Magnitude
  |101 | |_Reserved for future use._
  |110 | |_Reserved for future use._
  |111 |DYN |In instruction's _rm_ field, selects dynamic rounding mode; In Rounding Mode register, _reserved_.
  |===

  [NOTE]
  ====
  The C99 language standard effectively mandates the provision of a
  dynamic rounding mode register. In typical implementations, writes to
  the dynamic rounding mode CSR state will serialize the pipeline. Static
  rounding modes are used to implement specialized arithmetic operations
  that often have to switch frequently between different rounding modes.

  The ratified version of the F spec mandated that an illegal-instruction
  exception was raised when an instruction was executed with a reserved
  dynamic rounding mode. This has been weakened to reserved, which matches
  the behavior of static rounding-mode instructions. Raising an
  illegal-instruction exception is still valid behavior when encountering a
  reserved encoding, so implementations compatible with the ratified spec
  are compatible with the weakened spec.
  ====

  The accrued exception flags indicate the exception conditions that have
  arisen on any floating-point arithmetic instruction since the field was
  last reset by software, as shown in <<bitdef>>. The base
  RISC-V ISA does not support generating a trap on the setting of a
  floating-point exception flag.
  (((floating-point, exception flag)))

  [[bitdef]]
  .Accrued exception flag encoding.
  [%autowidth,float="center",align="center",cols="^,<",options="header",]
  |===
  |Flag Mnemonic |Flag Meaning
  |NV |Invalid Operation
  |DZ |Divide by Zero
  |OF |Overflow
  |UF |Underflow
  |NX |Inexact
  |===

  [NOTE]
  ====
  As allowed by the standard, we do not support traps on floating-point
  exceptions in the F extension, but instead require explicit checks of
  the flags in software. We considered adding branches controlled directly
  by the contents of the floating-point accrued exception flags, but
  ultimately chose to omit these instructions to keep the ISA simple.
  ====

  = NaN Generation and Propagation

  Except when otherwise stated, if the result of a floating-point
  operation is NaN, it is the canonical NaN. The canonical NaN has a
  positive sign and all significand bits clear except the MSB, a.k.a. the
  quiet bit. For single-precision floating-point, this corresponds to the pattern `0x7fc00000`.
  (((NaN, generation)))
  (((NaN, propagation)))

  [TIP]
  ====
  We considered propagating NaN payloads, as is recommended by the
  standard, but this decision would have increased hardware cost.
  Moreover, since this feature is optional in the standard, it cannot be
  used in portable code.

  Implementers are free to provide a NaN payload propagation scheme as a
  nonstandard extension enabled by a nonstandard operating mode. However, the canonical NaN scheme described above must always be supported and should be the default mode.
  ====
  '''
  [NOTE]
  ====
  We require implementations to return the standard-mandated default
  values in the case of exceptional conditions, without any further
  intervention on the part of user-level software (unlike the Alpha ISA
  floating-point trap barriers). We believe full hardware handling of
  exceptional cases will become more common, and so wish to avoid
  complicating the user-level ISA to optimize other approaches.
  Implementations can always trap to machine-mode software handlers to
  provide exceptional default values.
  ====

  = Subnormal Arithmetic

  Operations on subnormal numbers are handled in accordance with the IEEE 754-2008 standard.
  (((operations, subnormal)))

  In the parlance of the IEEE standard, tininess is detected after
  rounding.
  (((tininess, handling)))

  [NOTE]
  ====
  Detecting tininess after rounding results in fewer spurious underflow
  signals.
  ====
params:
  MUTABLE_MISA_F:
    description: |
      Indicates whether or not the `F` extension can be disabled with the `misa.F` bit.
    schema:
      type: boolean
  HW_MSTATUS_FS_DIRTY_UPDATE:
    description: |
      Indicates whether or not hardware will write to `mstatus.FS`

      Values are:
      [separator="!"]
      !===
      h! never  ! Hardware never writes `mstatus.FS`
      h! precise  ! Hardware writes `mstatus.FS` to the Dirty (3) state precisely when F registers are modified
      h! imprecise ! Hardware writes `mstatus.FS` imprecisely. This will result in a call to unpredictable() on any attempt to read `mstatus` or write FP state.
      !===
    schema:
      type: string
      enum: ["never", "precise", "imprecise"]
  MSTATUS_FS_LEGAL_VALUES:
    description: |
      The set of values that mstatus.FS will accept from a software write.
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1, 2, 3]
      maxItems: 4
      uniqueItems: true
    also_defined_in: S
    extra_validation: |
      assert MSTATUS_FS_LEGAL_VALUES.include?(0) && MSTATUS_FS_LEGAL_VALUES.include?(3) if ext?(:F)

      # if HW is writing FS, then Dirty (3) better be a supported value
      assert MSTATUS_FS_LEGAL_VALUES.include?(3) if ext?(:F) && (HW_MSTATUS_FS_DIRTY_UPDATE != "never")
