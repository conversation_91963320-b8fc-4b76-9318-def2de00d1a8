= Architectural Parameters

<%-
  params = manual_version.extensions.map{ |e| e.params }.flatten.uniq(&:name).sort_by!(&:name)
-%>

The following <%= params.size %> parameters are defined in this manual:

|===
| Name | Type | Extension(s) | Description

<%- params.each do |param| -%>
| <%= param.name %>
| <%= param.schema.to_pretty_s %>
| <%= param.exts.map { |ext| link_to_udb_doc_ext_param(ext.name, param.name, ext.name) }.join(", ") %>
a| <%= param.desc %>
<%- end -%>
|===
