# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicie6
long_name: IRQ Enable 6
address: 0x7fe
length: 32
base: 32
priv_mode: M
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Enable bits for IRQs 192-223
fields:
  IRQ192:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ192 enabled
  IRQ193:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ193 enabled
  IRQ194:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ194 enabled
  IRQ195:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ195 enabled
  IRQ196:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ196 enabled
  IRQ197:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ197 enabled
  IRQ198:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ198 enabled
  IRQ199:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ199 enabled
  IRQ200:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ200 enabled
  IRQ201:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ201 enabled
  IRQ202:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ202 enabled
  IRQ203:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ203 enabled
  IRQ204:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ204 enabled
  IRQ205:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ205 enabled
  IRQ206:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ206 enabled
  IRQ207:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ207 enabled
  IRQ208:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ208 enabled
  IRQ209:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ209 enabled
  IRQ210:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ210 enabled
  IRQ211:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ211 enabled
  IRQ212:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ212 enabled
  IRQ213:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ213 enabled
  IRQ214:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ214 enabled
  IRQ215:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ215 enabled
  IRQ216:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ216 enabled
  IRQ217:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ217 enabled
  IRQ218:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ218 enabled
  IRQ219:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ219 enabled
  IRQ220:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ220 enabled
  IRQ221:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ221 enabled
  IRQ222:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ222 enabled
  IRQ223:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ223 enabled
