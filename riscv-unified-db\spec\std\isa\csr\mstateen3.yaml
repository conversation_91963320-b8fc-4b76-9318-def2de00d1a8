# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mstateen3
long_name: Machine State Enable 3 Register
address: 0x30F
priv_mode: M
length: 64
description:
  - id: csr-mstateen3-purpose
    normative: true
    text: |
      Each bit of a `stateen` CSR controls less-privileged access to an extension’s state,
      or an extension that was not deemed "worthy" of a full XS field in `sstatus` like the
      FS and VS fields for the F and V extensions.
  - id: csr-mstateen3-num-justification
    normative: false
    text: |
      The number of registers provided at each level is four because it is believed that
      4 * 64 = 256 bits for machine and hypervisor levels, and 4 * 32 = 128 bits for
      supervisor level, will be adequate for many years to come, perhaps for as long as
      the RISC-V ISA is in use.
      The exact number four is an attempted compromise between providing too few bits on
      the one hand and going overboard with CSRs that will never be used on the other.
  - id: csr-mstateen3-scope
    normative: true
    text: |
      The `stateen` registers at each level control access to state at all less-privileged
      levels, but not at its own level.
  - id: csr-mstateen3-effect
    normative: true
    text: |
      When a `stateen` CSR prevents access to state for a privilege mode, attempting to execute
      in that privilege mode an instruction that implicitly updates the state without reading
      it may or may not raise an illegal instruction or virtual instruction exception.
      Such cases must be disambiguated by being explicitly specified one way or the other.
      In some cases, the bits of the `stateen` CSRs will have a dual purpose as enables for the
      ISA extensions that introduce the controlled state.
  - id: csr-mstateen3-bit-allocation
    normative: true
    text: |
      For every bit with a defined purpose in an `sstateen` CSR, the same bit is defined in the matching
      `mstateen` CSR to control access below machine level to the same state. The upper 32 bits of an
      `mstateen` CSR (or for RV32, the corresponding high-half CSR) control access to state that is inherently
      inaccessible to user level, so no corresponding enable bits in the supervisor-level `sstateen` CSR are
      applicable. The intention is to allocate bits for this purpose starting at the most-significant end, bit 63,
      through to bit 32, and then on to the next-higher `mstateen` CSR. If the rate that bits are being allocated
      from the least-significant end for `sstateen` CSRs is sufficiently low, allocation from the most-significant
      end of `mstateen` CSRs may be allowed to encroach on the lower 32 bits before jumping to the next-higher
      `mstateen` CSR. In that case, the bit positions of "encroaching" bits will remain forever read-only zeros in
      the matching `sstateen` CSRs.
  - id: csr-mstateen3-zero
    normative: true
    text: |
      For every bit in an `mstateen` CSR that is zero (whether read-only zero or set to zero),
      the same bit appears as read-only zero in the matching `hstateen` and `sstateen` CSRs.
  - id: csr-mstateen3-read-only
    normative: true
    text: |
      A bit in a supervisor-level `sstateen` CSR cannot be read-only one unless the same bit is read-only one in the matching
      `mstateen` CSR and, if it exists, in the matching `hstateen` CSR. A bit in an `hstateen` CSR cannot be read-only one unless
      the same bit is read-only one in the matching `mstateen` CSR. Bit 63 of each `mstateen` CSR may be read-only zero only if
      the hypervisor extension is not implemented and the matching supervisor-level `sstateen` CSR is all read-only zeros.

definedBy: Smstateen
fields:
  SE0:
    long_name: hstateen3, hstateen3h, and sstateen3 access control
    location: 63
    description: |
      The SE0 bit in `mstateen3` controls access to the `hstateen3`, `hstateen3h`, and the `sstateen3` CSRs.
    type: RW
    reset_value: 0
