<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Module: TestMixin
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "TestMixin";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (T)</a> &raquo;
    
    
    <span class="title">TestMixin</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Module: TestMixin
  
  
  
</h1>
<div class="box_info">
  

  
  
  
  
  

  
  <dl>
    <dt>Included in:</dt>
    <dd><span class='object_link'><a href="TestExpressions.html" title="TestExpressions (class)">TestExpressions</a></span>, <span class='object_link'><a href="TestVariables.html" title="TestVariables (class)">TestVariables</a></span></dd>
  </dl>
  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/tests/helpers.rb</dd>
  </dl>
  
</div>








  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#setup-instance_method" title="#setup (instance method)">#<strong>setup</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  



  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="setup-instance_method">
  
    #<strong>setup</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


18
19
20
21
22</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/helpers.rb', line 18</span>

<span class='kw'>def</span> <span class='id identifier rubyid_setup'>setup</span>
  <span class='ivar'>@archdef</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="MockArchDef.html" title="MockArchDef (class)">MockArchDef</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span>
  <span class='ivar'>@symtab</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/SymbolTable.html#initialize-instance_method" title="Idl::SymbolTable#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='ivar'>@archdef</span><span class='rparen'>)</span>
  <span class='ivar'>@compiler</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/Compiler.html" title="Idl::Compiler (class)">Compiler</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/Compiler.html#initialize-instance_method" title="Idl::Compiler#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='ivar'>@archdef</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>