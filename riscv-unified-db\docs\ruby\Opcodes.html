<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Module: Opcodes
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Opcodes";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (O)</a> &raquo;
    
    
    <span class="title">Opcodes</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Module: Opcodes
  
  
  
</h1>
<div class="box_info">
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/opcodes.rb</dd>
  </dl>
  
</div>


  
    <h2>
      Constant Summary
      <small><a href="#" class="constants_summary_toggle">collapse</a></small>
    </h2>

    <dl class="constants">
      
        <dt id="DECODER_RING-constant" class="">DECODER_RING =
          
        </dt>
        <dd><pre class="code"><span class='lbrace'>{</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_p</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>4</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rd&#39;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_n0</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rd != 0</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_n2</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd != {0,2}</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>15</span><span class='op'>..</span><span class='int'>19</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1_p</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>9</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rs1&#39;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_rs1_n0</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rs1 != 0</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>

  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs2</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>20</span><span class='op'>..</span><span class='int'>24</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_rs2</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs2</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs2_p</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>4</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rs2&#39;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs2</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_rs2_n0</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rs2 != 0</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs2</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_rs1</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1/rd != 0</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_rs1_p</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>9</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>rd&#39;/rs1&#39;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd_rs1_n0</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd/rs1 != 0</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rd</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1_n0</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1 != 0</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rs1</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamtd</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='int'>20</span><span class='op'>..</span><span class='int'>25</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamt</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamt</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamtw</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='int'>20</span><span class='op'>..</span><span class='int'>24</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamt</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>shamt</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>csr</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='int'>20</span><span class='op'>..</span><span class='int'>31</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>zimm</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='int'>15</span><span class='op'>..</span><span class='int'>19</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm12</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>20</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[11:0]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm20</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>12</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[31:20]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>jimm20</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>31</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>12</span><span class='op'>..</span><span class='int'>19</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>20</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>21</span><span class='op'>..</span><span class='int'>30</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='int'>12</span><span class='op'>..</span><span class='int'>31</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>1</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[20|10:1|11|19:12]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>bimm12hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>bimm12lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>31</span><span class='comma'>,</span> <span class='int'>7</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>25</span><span class='op'>..</span><span class='int'>30</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>8</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>25</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>1</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[12|10:5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[4:1|11]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>offset</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm12hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm12lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>25</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>25</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[11:5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[4:0]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>pred</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>24</span><span class='op'>..</span><span class='int'>27</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>succ</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>20</span><span class='op'>..</span><span class='int'>23</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>fm</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>28</span><span class='op'>..</span><span class='int'>31</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span><span class='comma'>,</span>

  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzuimm10</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>10</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>11</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>5</span><span class='comma'>,</span> <span class='int'>6</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='int'>5</span><span class='op'>..</span><span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzuimm[5:4|9:6|2|3]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm8lo</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm8hi</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>5</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>5</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>3</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[7:6]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5:3]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm7lo</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm7hi</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>5</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>6</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>5</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5:3]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[2|6]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm6hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm6lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[4:0]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzuimm6hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzuimm6lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzuimm[5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzuimm[4:0]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_imm6hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_imm6lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[4:0]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm10hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm10lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>3</span><span class='op'>..</span><span class='int'>4</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>5</span><span class='comma'>,</span> <span class='int'>2</span><span class='comma'>,</span> <span class='int'>6</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>4</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[9]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[4|6|8:7|5]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm18hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_nzimm18lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[17]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>nzimm[16:12]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_imm12</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='int'>8</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>9</span><span class='op'>..</span><span class='int'>10</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>6</span><span class='comma'>,</span> <span class='int'>7</span><span class='comma'>,</span> <span class='int'>2</span><span class='comma'>,</span> <span class='int'>11</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>3</span><span class='op'>..</span><span class='int'>5</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='int'>2</span><span class='op'>..</span><span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>1</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[11|4|9:8|10|6|7|3:1|5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_bimm9hi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_bimm9lo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>5</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>2</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>11</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>3</span><span class='op'>..</span><span class='int'>4</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>1</span><span class='comma'>,</span>
    <span class='label'>sext:</span> <span class='kw'>true</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[8|4:3]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm[7:6|2:1|5]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm8sphi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm8splo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>3</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>4</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm5</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[4:2|7:6]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>cuimm8sp_s</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>8</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>9</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5:2|7:6]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm9sphi</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm9splo</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>3</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>4</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='lbracket'>[</span><span class='int'>12</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>2</span><span class='op'>..</span><span class='int'>6</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>3</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[4:3|8:6]</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm8sp_s</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>8</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>9</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='int'>7</span><span class='op'>..</span><span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>2</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5:2|7:6]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>c_uimm9sp_s</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lbracket'>[</span><span class='lparen'>(</span><span class='int'>7</span><span class='op'>..</span><span class='int'>9</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='int'>10</span><span class='op'>..</span><span class='int'>12</span><span class='rparen'>)</span><span class='rbracket'>]</span><span class='comma'>,</span>
    <span class='label'>group_by:</span> <span class='int'>7</span><span class='op'>..</span><span class='int'>12</span><span class='comma'>,</span>
    <span class='label'>lshift:</span> <span class='int'>3</span><span class='comma'>,</span>
    <span class='label'>display:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>uimm[5:3|8:6]</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span>
    <span class='label'>decode_variable:</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>imm</span><span class='tstring_end'>&#39;</span></span>
  <span class='rbrace'>}</span><span class='comma'>,</span>
  <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>rm</span><span class='tstring_end'>&#39;</span></span> <span class='op'>=&gt;</span> <span class='lbrace'>{</span>
    <span class='label'>bits:</span> <span class='lparen'>(</span><span class='int'>12</span><span class='op'>..</span><span class='int'>14</span><span class='rparen'>)</span>
  <span class='rbrace'>}</span>

<span class='rbrace'>}</span></pre></dd>
      
    </dl>
  







  
    <h2>
      Class Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#insn_table-class_method" title="insn_table (class method)">.<strong>insn_table</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  



  <div id="class_method_details" class="method_details_list">
    <h2>Class Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="insn_table-class_method">
  
    .<strong>insn_table</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


614
615
616
617
618
619
620</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 614</span>

<span class='kw'>def</span> <span class='kw'>self</span><span class='period'>.</span><span class='id identifier rubyid_insn_table'>insn_table</span>
  <span class='kw'>return</span> <span class='ivar'>@insn_table</span> <span class='kw'>unless</span> <span class='ivar'>@insn_table</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@insn_table</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_safe_load'>safe_load</span><span class='lparen'>(</span>
    <span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='lparen'>(</span><span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_dirname'>dirname</span><span class='lparen'>(</span><span class='kw'>__FILE__</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>..</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>..</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>ext</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>riscv-opcodes</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>instr_dict.yaml</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>