# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zcmp
long_name: 16-bit Push/Pop instructions
description: |
  The Zcmp extension is a set of instructions which may be executed as a series of existing 32-bit RISC-V instructions.

  This extension reuses some encodings from _c.fsdsp_.  Therefore it is _incompatible_ with <<Zcd>>,
   which is included when C and D extensions are both present.

  NOTE: Zcmp is primarily targeted at embedded class CPUs due to implementation complexity. Additionally, it is not compatible with architecture class profiles.

  The Zcmp extension depends on the <<Zca>> extension.

  The PUSH/POP assembly syntax uses several variables, the meaning of which are:

  * _reg_list_ is a list containing 1 to 13 registers (ra and 0 to 12 s registers)
  ** valid values: {ra}, {ra, s0}, {ra, s0-s1}, {ra, s0-s2}, ..., {ra, s0-s8}, {ra, s0-s9}, {ra, s0-s11}
  ** note that {ra, s0-s10} is _not_ valid, giving 12 lists not 13 for better encoding
  * _stack_adj_ is the total size of the stack frame.
  ** valid values vary with register list length and the specific encoding, see the instruction pages for details.

  [%header,cols="^1,^1,4,8"]
  |===
  |RV32
  |RV64
  |Mnemonic
  |Instruction

  |yes
  |yes
  |cm.push _{reg_list}, -stack_adj_
  |<<#insns-cm_push>>

  |yes
  |yes
  |cm.pop _{reg_list}, stack_adj_
  |<<#insns-cm_pop>>

  |yes
  |yes
  |cm.popret _{reg_list}, stack_adj_
  |<<#insns-cm_popret>>

  |yes
  |yes
  |cm.popretz _{reg_list}, stack_adj_
  |<<#insns-cm_popretz>>

  |yes
  |yes
  |cm.mva01s _rs1', rs2'_
  |<<#insns-cm_mva01s>>

  |yes
  |yes
  |cm.mvsa01 _r1s', r2s'_
  |<<#insns-cm_mvsa01>>

  |===

type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-04
    repositories:
      - url: https://github.com/riscv/riscv-code-size-reduction
        branch: main
    contributors:
      - name: Tariq Kurd
      - name: Ibrahim Abu Kharmeh
      - name: Torbjørn Viem Ness
      - name: Matteo Perotti
      - name: Nidal Faour
      - name: Bill Traynor
      - name: Rafael Sene
      - name: Xinlong Wu
      - name: sinan
      - name: Jeremy Bennett
      - name: Heda Chen
      - name: Alasdair Armstrong
      - name: Graeme Smecher
      - name: Nicolas Brunie
      - name: Jiawei
    requires: { name: Zca, version: ">= 1.0.0" }
conflicts:
  anyOf:
    - allOf: [C, D]
    - Zcd
