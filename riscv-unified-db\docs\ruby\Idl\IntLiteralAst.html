<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::IntLiteralAst
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::IntLiteralAst";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (I)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">IntLiteralAst</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::IntLiteralAst
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></li>
          
            <li class="next">Idl::IntLiteralAst</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="AstNodeFuncs.html" title="Idl::AstNodeFuncs (module)">AstNodeFuncs</a></span>, <span class='object_link'><a href="Rvalue.html" title="Idl::Rvalue (module)">Rvalue</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb<span class="defines">,<br />
  lib/idl/passes/gen_adoc.rb</span>
</dd>
  </dl>
  
</div>








  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#children-instance_method" title="#children (instance method)">#<strong>children</strong>  &#x21d2; Array&lt;AstNode&gt; </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#children-instance_method" title="Idl::AstNodeFuncs#children (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of children, or an empty array for a terminal.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#gen_adoc-instance_method" title="#gen_adoc (instance method)">#<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(input, interval)  &#x21d2; IntLiteralAst </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of IntLiteralAst.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#internal_error-instance_method" title="#internal_error (instance method)">#<strong>internal_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#internal_error-instance_method" title="Idl::AstNodeFuncs#internal_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise an internal error.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#nodes-instance_method" title="#nodes (instance method)">#<strong>nodes</strong>  &#x21d2; Array&lt;AstNode&gt; </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#nodes-instance_method" title="Idl::AstNodeFuncs#nodes (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>An array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#print_ast-instance_method" title="#print_ast (instance method)">#<strong>print_ast</strong>(indent = 0, indent_size: 2, io: $stdout)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#print_ast-instance_method" title="Idl::AstNodeFuncs#print_ast (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>pretty print the AST rooted at this node.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_idl-instance_method" title="#to_idl (instance method)">#<strong>to_idl</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return valid IDL representation of the node (and its subtree).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type-instance_method" title="#type (instance method)">#<strong>type</strong>(symtab)  &#x21d2; Type </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Given a specific symbol table, return the type of this node.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check this node and all children.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_error-instance_method" title="#type_error (instance method)">#<strong>type_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#type_error-instance_method" title="Idl::AstNodeFuncs#type_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise a type error.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#unindent-instance_method" title="#unindent (instance method)">#<strong>unindent</strong>(s)  &#x21d2; String </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#unindent-instance_method" title="Idl::AstNodeFuncs#unindent (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>unindent a multiline string, getting rid of all common leading whitespace (like &lt;&lt;~ heredocs).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#unsigned_value-instance_method" title="#unsigned_value (instance method)">#<strong>unsigned_value</strong>  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The unsigned value of this literal (i.e., treating it as unsigned even if the signed specifier is present).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#value-instance_method" title="#value (instance method)">#<strong>value</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return the compile-time-known value of the node.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#value_error-instance_method" title="#value_error (instance method)">#<strong>value_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#value_error-instance_method" title="Idl::AstNodeFuncs#value_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise a value error, indicating that the value is not known at compile time.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#values-instance_method" title="#values (instance method)">#<strong>values</strong>(symtab)  &#x21d2; Array&lt;Integer&gt;, ... </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="Rvalue.html#values-instance_method" title="Idl::Rvalue#values (method)">Rvalue</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return a complete list of possible compile-time-known values of the node, or raise a ValueError if the full list cannot be determined.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#width-instance_method" title="#width (instance method)">#<strong>width</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  
  
  
  
  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(input, interval)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::IntLiteralAst (class)">IntLiteralAst</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of IntLiteralAst.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


2880
2881
2882</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 2880</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='lbracket'>[</span><span class='rbracket'>]</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="children-instance_method">
  
    #<strong>children</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#children-instance_method" title="Idl::AstNodeFuncs#children (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns list of children, or an empty array for a terminal.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>list of children, or an empty array for a terminal</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="gen_adoc-instance_method">
  
    #<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


51
52
53
54</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/gen_adoc.rb', line 51</span>

<span class='kw'>def</span> <span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='id identifier rubyid_indent'>indent</span> <span class='op'>=</span> <span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span> <span class='int'>2</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>?</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
  <span class='id identifier rubyid_text_value'>text_value</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="internal_error-instance_method">
  
    #<strong>internal_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#internal_error-instance_method" title="Idl::AstNodeFuncs#internal_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise an internal error</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="nodes-instance_method">
  
    #<strong>nodes</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#nodes-instance_method" title="Idl::AstNodeFuncs#nodes (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns an array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>an array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST)</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="print_ast-instance_method">
  
    #<strong>print_ast</strong>(indent = 0, indent_size: 2, io: $stdout)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#print_ast-instance_method" title="Idl::AstNodeFuncs#print_ast (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>pretty print the AST rooted at this node</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>indent</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>0</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The starting indentation, in # of spaces</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>indent_size</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>2</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The extra indentation applied to each level of the tree</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>io</span>
      
      
        <span class='type'>(<tt>IO</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>$stdout</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Where to write the output</p>
</div>
      
    </li>
  
</ul>


</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_idl-instance_method">
  
    #<strong>to_idl</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return valid IDL representation of the node (and its subtree)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL code for the node</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


3058</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 3058</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_idl'>to_idl</span> <span class='op'>=</span> <span class='id identifier rubyid_text_value'>text_value</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type-instance_method">
  
    #<strong>type</strong>(symtab)  &#x21d2; <tt><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Given a specific symbol table, return the type of this node.</p>

<p>Should not be called until <span class='object_link'><a href="#type_check-instance_method" title="Idl::IntLiteralAst#type_check (method)">#type_check</a></span> is called with the same arguments</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The type of the node</p>
</div>
      
    </li>
  
</ul>
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if the type is dependent on symtab, and type_check was not called first</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


2902
2903
2904
2905
2906
2907
2908
2909
2910
2911
2912
2913
2914
2915
2916
2917
2918
2919
2920
2921
2922
2923
2924
2925
2926
2927
2928
2929
2930
2931
2932
2933
2934
2935
2936</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 2902</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='ivar'>@type</span> <span class='kw'>unless</span> <span class='ivar'>@type</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='kw'>case</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_delete'>delete</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>_</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]+)?&#39;(s?)([bodh]?)(.*)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># verilog-style literal
</span>    <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>true</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>false</span>
    <span class='kw'>end</span>

    <span class='id identifier rubyid_qualifiers'>qualifiers</span> <span class='op'>=</span> <span class='id identifier rubyid_signed'>signed</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>s</span><span class='tstring_end'>&quot;</span></span> <span class='op'>?</span> <span class='lbracket'>[</span><span class='symbol'>:signed</span><span class='comma'>,</span> <span class='symbol'>:const</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='lbracket'>[</span><span class='symbol'>:const</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_t'>t</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='comma'>,</span> <span class='label'>qualifiers:</span><span class='rparen'>)</span>
    <span class='ivar'>@type</span> <span class='op'>=</span> <span class='id identifier rubyid_t'>t</span> <span class='kw'>if</span> <span class='id identifier rubyid_memoize'>memoize</span>
    <span class='id identifier rubyid_t'>t</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>0([bdx]?)([0-9a-fA-F]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># C++-style literal
</span>    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_qualifiers'>qualifiers</span> <span class='op'>=</span> <span class='id identifier rubyid_signed'>signed</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>s</span><span class='tstring_end'>&quot;</span></span> <span class='op'>?</span> <span class='lbracket'>[</span><span class='symbol'>:signed</span><span class='comma'>,</span> <span class='symbol'>:const</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='lbracket'>[</span><span class='symbol'>:const</span><span class='rbracket'>]</span>
    <span class='ivar'>@type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_width'>width</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>qualifiers:</span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># basic decimal
</span>    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_qualifiers'>qualifiers</span> <span class='op'>=</span> <span class='id identifier rubyid_signed'>signed</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>s</span><span class='tstring_end'>&quot;</span></span> <span class='op'>?</span> <span class='lbracket'>[</span><span class='symbol'>:signed</span><span class='comma'>,</span> <span class='symbol'>:const</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='lbracket'>[</span><span class='symbol'>:const</span><span class='rbracket'>]</span>
    <span class='ivar'>@type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:bits</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_width'>width</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>qualifiers:</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_internal_error'>internal_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unhandled int value</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>type check this node and all children</p>

<p>Calls to <span class='object_link'><a href="#type-instance_method" title="Idl::IntLiteralAst#type (method)">#type</a></span> and/or <span class='object_link'><a href="#value-instance_method" title="Idl::IntLiteralAst#value (method)">#value</a></span> may depend on type_check being called first with the same symtab. If not, those functions may raise an AstNode::InternalError</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is a type error</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is an internal compiler error during type check</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


2885
2886
2887
2888
2889
2890
2891
2892
2893
2894
2895
2896
2897
2898
2899</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 2885</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_delete'>delete</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>_</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>=~</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]+)?&#39;(s?)([bodh]?)(.*)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># verilog-style literal
</span>    <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_value_text'>value_text</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>4</span><span class='rparen'>)</span>

    <span class='kw'>if</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>false</span>
    <span class='kw'>end</span>

    <span class='comment'># ensure we actually have enough bits to represent the value
</span>    <span class='id identifier rubyid_type_error'>type_error</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_value_text'>value_text</span><span class='embexpr_end'>}</span><span class='tstring_content'> cannot be represented in </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_width'>width</span><span class='embexpr_end'>}</span><span class='tstring_content'> bits</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='kw'>if</span> <span class='id identifier rubyid_unsigned_value'>unsigned_value</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span> <span class='op'>&gt;</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_error-instance_method">
  
    #<strong>type_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#type_error-instance_method" title="Idl::AstNodeFuncs#type_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise a type error</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="unindent-instance_method">
  
    #<strong>unindent</strong>(s)  &#x21d2; <tt>String</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#unindent-instance_method" title="Idl::AstNodeFuncs#unindent (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>unindent a multiline string, getting rid of all common leading whitespace (like &lt;&lt;~ heredocs)</p>

<p>borrowed from <a href="https://stackoverflow.com/questions/33527064/multiline-strings-with-no-indent">stackoverflow.com/questions/33527064/multiline-strings-with-no-indent</a></p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>s</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A string (presumably with newlines)</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Unindented string</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="unsigned_value-instance_method">
  
    #<strong>unsigned_value</strong>  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the unsigned value of this literal (i.e., treating it as unsigned even if the signed specifier is present).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the unsigned value of this literal (i.e., treating it as unsigned even if the signed specifier is present)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


3004
3005
3006
3007
3008
3009
3010
3011
3012
3013
3014
3015
3016
3017
3018
3019
3020
3021
3022
3023
3024
3025
3026
3027
3028
3029
3030
3031
3032
3033
3034
3035
3036
3037
3038
3039
3040
3041
3042
3043
3044
3045
3046
3047
3048
3049
3050
3051
3052
3053
3054
3055</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 3004</span>

<span class='kw'>def</span> <span class='id identifier rubyid_unsigned_value'>unsigned_value</span>
  <span class='kw'>return</span> <span class='ivar'>@unsigned_value</span> <span class='kw'>unless</span> <span class='ivar'>@unsigned_value</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='kw'>case</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_delete'>delete</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>_</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]+)?&#39;(s?)([bodh]?)(.*)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># verilog-style literal
</span>    <span class='id identifier rubyid_radix_id'>radix_id</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_value'>value</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>4</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_radix_id'>radix_id</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>d</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_radix_id'>radix_id</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

    <span class='comment'># ensure we actually have enough bits to represent the value
</span>    <span class='ivar'>@unsigned_value</span> <span class='op'>=</span>
      <span class='kw'>case</span> <span class='id identifier rubyid_radix_id'>radix_id</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>b</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>o</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>8</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>d</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>10</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>h</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>16</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>0([bdx]?)([0-9a-fA-F]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># C++-style literal
</span>    <span class='id identifier rubyid_radix_id'>radix_id</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_value'>value</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_radix_id'>radix_id</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>o</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_radix_id'>radix_id</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

    <span class='ivar'>@unsigned_value</span> <span class='op'>=</span>
      <span class='kw'>case</span> <span class='id identifier rubyid_radix_id'>radix_id</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>b</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>o</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>8</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>d</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>10</span><span class='rparen'>)</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>x</span><span class='tstring_end'>&quot;</span></span>
        <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>16</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># basic decimal
</span>    <span class='id identifier rubyid_value'>value</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>

    <span class='ivar'>@unsigned_value</span> <span class='op'>=</span> <span class='id identifier rubyid_value'>value</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='lparen'>(</span><span class='int'>10</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_internal_error'>internal_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unhandled int value</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="value-instance_method">
  
    #<strong>value</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return the compile-time-known value of the node</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


2974
2975
2976
2977
2978
2979
2980
2981
2982
2983
2984
2985
2986
2987
2988
2989
2990
2991
2992
2993
2994
2995
2996
2997
2998
2999
3000</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 2974</span>

<span class='kw'>def</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='ivar'>@value</span> <span class='kw'>unless</span> <span class='ivar'>@value</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='kw'>if</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_delete'>delete</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>_</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>=~</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]+)?&#39;(s?)([bodh]?)(.*)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># verilog-style literal
</span>    <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>true</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>false</span>
    <span class='kw'>end</span>

    <span class='id identifier rubyid_v'>v</span> <span class='op'>=</span>
      <span class='kw'>if</span> <span class='op'>!</span><span class='id identifier rubyid_signed'>signed</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='lparen'>(</span><span class='id identifier rubyid_unsigned_value'>unsigned_value</span> <span class='op'>&gt;&gt;</span> <span class='lparen'>(</span><span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span> <span class='op'>-</span> <span class='int'>1</span><span class='rparen'>)</span><span class='rparen'>)</span> <span class='op'>==</span> <span class='int'>1</span><span class='rparen'>)</span>
        <span class='op'>-</span><span class='lparen'>(</span><span class='int'>2</span><span class='op'>**</span><span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span> <span class='op'>-</span> <span class='id identifier rubyid_unsigned_value'>unsigned_value</span><span class='rparen'>)</span> 
      <span class='kw'>else</span>
        <span class='id identifier rubyid_unsigned_value'>unsigned_value</span>
      <span class='kw'>end</span>

    <span class='ivar'>@value</span> <span class='op'>=</span> <span class='id identifier rubyid_v'>v</span> <span class='kw'>if</span> <span class='id identifier rubyid_memoize'>memoize</span>
    <span class='id identifier rubyid_v'>v</span>
  <span class='kw'>else</span>
    <span class='ivar'>@value</span> <span class='op'>=</span> <span class='id identifier rubyid_unsigned_value'>unsigned_value</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="value_error-instance_method">
  
    #<strong>value_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#value_error-instance_method" title="Idl::AstNodeFuncs#value_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise a value error, indicating that the value is not known at compile time</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">AstNode::ValueError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="values-instance_method">
  
    #<strong>values</strong>(symtab)  &#x21d2; <tt>Array&lt;Integer&gt;</tt>, ... 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="Rvalue.html#values-instance_method" title="Idl::Rvalue#values (method)">Rvalue</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return a complete list of possible compile-time-known values of the node, or raise a ValueError if the full list cannot be determined</p>

<p>For most AstNodes, this will just be a single-entry array</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The context for the evaulation</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;Integer&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The complete list of compile-time-known values, when they are integral</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;Boolean&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The complete list of compile-time-known values, when they are booleans</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">AstNode::ValueError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if the list of values is not knowable at compile time</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="width-instance_method">
  
    #<strong>width</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


2938
2939
2940
2941
2942
2943
2944
2945
2946
2947
2948
2949
2950
2951
2952
2953
2954
2955
2956
2957
2958
2959
2960
2961
2962
2963
2964
2965
2966
2967
2968
2969
2970
2971</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 2938</span>

<span class='kw'>def</span> <span class='id identifier rubyid_width'>width</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='ivar'>@width</span> <span class='kw'>unless</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_text_value_no_underscores'>text_value_no_underscores</span> <span class='op'>=</span> <span class='id identifier rubyid_text_value'>text_value</span><span class='period'>.</span><span class='id identifier rubyid_delete'>delete</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>_</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>

  <span class='kw'>case</span> <span class='id identifier rubyid_text_value_no_underscores'>text_value_no_underscores</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]+)?&#39;(s?)([bodh]?)(.*)</span><span class='regexp_end'>/</span></span>
    <span class='comment'># verilog-style literal
</span>    <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>true</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_memoize'>memoize</span> <span class='op'>=</span> <span class='kw'>false</span>
    <span class='kw'>end</span>
    <span class='ivar'>@width</span> <span class='op'>=</span> <span class='id identifier rubyid_width'>width</span> <span class='kw'>if</span> <span class='id identifier rubyid_memoize'>memoize</span>
    <span class='id identifier rubyid_width'>width</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>0([bdx]?)([0-9a-fA-F]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>

    <span class='ivar'>@width</span> <span class='op'>=</span> <span class='id identifier rubyid_signed'>signed</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>s</span><span class='tstring_end'>&quot;</span></span> <span class='op'>?</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span> <span class='op'>+</span> <span class='int'>1</span> <span class='op'>:</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span>
    <span class='ivar'>@width</span> <span class='op'>=</span> <span class='int'>1</span> <span class='kw'>if</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_zero?'>zero?</span> <span class='comment'># happens when the literal is &#39;0&#39;
</span>
    <span class='ivar'>@width</span>
  <span class='kw'>when</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>([0-9]*)(s?)</span><span class='regexp_end'>/</span></span>
    <span class='id identifier rubyid_signed'>signed</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>3</span><span class='rparen'>)</span>

    <span class='ivar'>@width</span> <span class='op'>=</span> <span class='id identifier rubyid_signed'>signed</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>s</span><span class='tstring_end'>&quot;</span></span> <span class='op'>?</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span> <span class='op'>+</span> <span class='int'>1</span> <span class='op'>:</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span>
    <span class='ivar'>@width</span> <span class='op'>=</span> <span class='int'>1</span> <span class='kw'>if</span> <span class='ivar'>@width</span><span class='period'>.</span><span class='id identifier rubyid_zero?'>zero?</span> <span class='comment'># happens when the literal is &#39;0&#39;
</span>
    <span class='ivar'>@width</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_internal_error'>internal_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No match on int literal</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:46 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>