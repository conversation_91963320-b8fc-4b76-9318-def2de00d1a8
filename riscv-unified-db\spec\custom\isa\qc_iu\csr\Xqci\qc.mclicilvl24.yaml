# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl24
long_name: IRQ Level 24
address: 0xbd8
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 192-199
fields:
  IRQ192:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ192 level
  IRQ193:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ193 level
  IRQ194:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ194 level
  IRQ195:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ195 level
  IRQ196:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ196 level
  IRQ197:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ197 level
  IRQ198:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ198 level
  IRQ199:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ199 level
