<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Csr
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Csr";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (C)</a> &raquo;
    
    
    <span class="title">Csr</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Csr
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></li>
          
            <li class="next">Csr</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>CSR definition</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#arch_def-instance_method" title="#arch_def (instance method)">#<strong>arch_def</strong>  &#x21d2; ArchDef </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The owning ArchDef.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#sym_table-instance_method" title="#sym_table (instance method)">#<strong>sym_table</strong>  &#x21d2; Idl::SymbolTable </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The symbol table holding global names.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#description_html-instance_method" title="#description_html (instance method)">#<strong>description_html</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>parse description field with asciidoctor, and return the HTML result.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#dynamic_length%3F-instance_method" title="#dynamic_length? (instance method)">#<strong>dynamic_length?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not the length of the CSR depends on a runtime value (e.g., mstatus.SXL).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#field-instance_method" title="#field (instance method)">#<strong>field</strong>(field_name)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns [CsrField,nil] field named ‘field_name’ if it exists, and nil otherwise.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#field%3F-instance_method" title="#field? (instance method)">#<strong>field?</strong>(field_name)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>True if a field named ‘field_name’ is defined in the csr, and false otherwise.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#field_hash-instance_method" title="#field_hash (instance method)">#<strong>field_hash</strong>  &#x21d2; Hash&lt;String,CsrField&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Hash of fields, indexed by field name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#fields-instance_method" title="#fields (instance method)">#<strong>fields</strong>  &#x21d2; Array&lt;CsrField&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>All fields for this CSR, regardless of whether or not they are implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#has_custom_sw_read%3F-instance_method" title="#has_custom_sw_read? (instance method)">#<strong>has_custom_sw_read?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>True if the CSR has a custom sw_read function.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_fields-instance_method" title="#implemented_fields (instance method)">#<strong>implemented_fields</strong>  &#x21d2; Array&lt;CsrField&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>All implemented fields for this CSR Excluded any fields that are defined by unimplemented extensions.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_fields_for-instance_method" title="#implemented_fields_for (instance method)">#<strong>implemented_fields_for</strong>(effective_xlen)  &#x21d2; Array&lt;CsrField&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>All implemented fields for this CSR at the given effective XLEN, sorted by location (smallest location first) Excluded any fields that are defined by unimplemented extensions or a base that is not effective_xlen.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(csr_data, sym_table, arch_def)  &#x21d2; Csr </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Csr.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#length-instance_method" title="#length (instance method)">#<strong>length</strong>(effective_xlen = nil)  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Length, in bits, of the CSR.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#length_cond32-instance_method" title="#length_cond32 (instance method)">#<strong>length_cond32</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>IDL condition of when the effective xlen is 32.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#length_cond64-instance_method" title="#length_cond64 (instance method)">#<strong>length_cond64</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>IDL condition of when the effective xlen is 64.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#length_pretty-instance_method" title="#length_pretty (instance method)">#<strong>length_pretty</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Pretty-printed length string.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#wavedrom_desc-instance_method" title="#wavedrom_desc (instance method)">#<strong>wavedrom_desc</strong>(effective_xlen)  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A JSON representation of the WaveDrom drawing for the CSR.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(csr_data, sym_table, arch_def)  &#x21d2; <tt><span class='object_link'><a href="" title="Csr (class)">Csr</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Csr.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>csr_data</span>
      
      
        <span class='type'>(<tt>Hash&lt;String,Object&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Hash of data from the specification</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>sym_table</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The symbol table holding global names</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>arch_def</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The architecture definition</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


359
360
361
362
363
364</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 359</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_data'>csr_data</span><span class='comma'>,</span> <span class='id identifier rubyid_sym_table'>sym_table</span><span class='comma'>,</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_data'>csr_data</span><span class='rparen'>)</span>

  <span class='ivar'>@arch_def</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
  <span class='ivar'>@sym_table</span> <span class='op'>=</span> <span class='id identifier rubyid_sym_table'>sym_table</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>
<div id="method_missing_details" class="method_details_list">
  <h2>Dynamic Method Handling</h2>
  <p class="notice super">
    This class handles dynamic methods through the <tt>method_missing</tt> method
    
      in the class <span class='object_link'><a href="ArchDefObject.html#method_missing-instance_method" title="ArchDefObject#method_missing (method)">ArchDefObject</a></span>
    
  </p>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="arch_def-instance_method">
  
    #<strong>arch_def</strong>  &#x21d2; <tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The owning ArchDef.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The owning ArchDef</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


351
352
353</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 351</span>

<span class='kw'>def</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
  <span class='ivar'>@arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="sym_table-instance_method">
  
    #<strong>sym_table</strong>  &#x21d2; <tt><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The symbol table holding global names.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The symbol table holding global names</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


354
355
356</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 354</span>

<span class='kw'>def</span> <span class='id identifier rubyid_sym_table'>sym_table</span>
  <span class='ivar'>@sym_table</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="description_html-instance_method">
  
    #<strong>description_html</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>parse description field with asciidoctor, and return the HTML result</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Parsed description in HTML</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


467
468
469</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 467</span>

<span class='kw'>def</span> <span class='id identifier rubyid_description_html'>description_html</span>
  <span class='const'>Asciidoctor</span><span class='period'>.</span><span class='id identifier rubyid_convert'>convert</span> <span class='id identifier rubyid_description'>description</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="dynamic_length?-instance_method">
  
    #<strong>dynamic_length?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the length of the CSR depends on a runtime value (e.g., mstatus.SXL).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the length of the CSR depends on a runtime value (e.g., mstatus.SXL)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


368
369
370
371
372
373
374
375
376
377
378
379
380
381
382</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 368</span>

<span class='kw'>def</span> <span class='id identifier rubyid_dynamic_length?'>dynamic_length?</span>
  <span class='kw'>return</span> <span class='kw'>false</span> <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>

  <span class='kw'>case</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>MXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>false</span> <span class='comment'># mxlen can never change
</span>  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected length</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
  <span class='op'>!</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>!=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>MXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="field-instance_method">
  
    #<strong>field</strong>(field_name)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns [CsrField,nil] field named ‘field_name’ if it exists, and nil otherwise</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


554
555
556</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 554</span>

<span class='kw'>def</span> <span class='id identifier rubyid_field'>field</span><span class='lparen'>(</span><span class='id identifier rubyid_field_name'>field_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_field_hash'>field_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_field_name'>field_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="field?-instance_method">
  
    #<strong>field?</strong>(field_name)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns true if a field named ‘field_name’ is defined in the csr, and false otherwise.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>true if a field named ‘field_name’ is defined in the csr, and false otherwise</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


549
550
551</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 549</span>

<span class='kw'>def</span> <span class='id identifier rubyid_field?'>field?</span><span class='lparen'>(</span><span class='id identifier rubyid_field_name'>field_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_field_hash'>field_hash</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='id identifier rubyid_field_name'>field_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="field_hash-instance_method">
  
    #<strong>field_hash</strong>  &#x21d2; <tt>Hash&lt;String,<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Hash of fields, indexed by field name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;String,<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Hash of fields, indexed by field name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


537
538
539
540
541
542
543
544
545
546</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 537</span>

<span class='kw'>def</span> <span class='id identifier rubyid_field_hash'>field_hash</span>
  <span class='ivar'>@field_hash</span> <span class='kw'>unless</span> <span class='ivar'>@field_hash</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@field_hash</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='id identifier rubyid_fields'>fields</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_field'>field</span><span class='op'>|</span>
    <span class='ivar'>@field_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_field'>field</span>
  <span class='kw'>end</span>

  <span class='ivar'>@field_hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="fields-instance_method">
  
    #<strong>fields</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns All fields for this CSR, regardless of whether or not they are implemented.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>All fields for this CSR, regardless of whether or not they are implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


472
473
474
475
476
477
478
479
480</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 472</span>

<span class='kw'>def</span> <span class='id identifier rubyid_fields'>fields</span>
  <span class='kw'>return</span> <span class='ivar'>@fields</span> <span class='kw'>unless</span> <span class='ivar'>@fields</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>fields</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each_value'>each_value</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_field_data'>field_data</span><span class='op'>|</span>
    <span class='ivar'>@fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="CsrField.html#initialize-instance_method" title="CsrField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@fields</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="has_custom_sw_read?-instance_method">
  
    #<strong>has_custom_sw_read?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns true if the CSR has a custom sw_read function.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>true if the CSR has a custom sw_read function</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


559
560
561</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 559</span>

<span class='kw'>def</span> <span class='id identifier rubyid_has_custom_sw_read?'>has_custom_sw_read?</span>
  <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>sw_read</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='op'>!</span><span class='id identifier rubyid_sw_read'>sw_read</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_fields-instance_method">
  
    #<strong>implemented_fields</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns All implemented fields for this CSR Excluded any fields that are defined by unimplemented extensions.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>All implemented fields for this CSR Excluded any fields that are defined by unimplemented extensions</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


508
509
510
511
512
513
514
515
516
517
518
519
520
521
522
523
524
525
526
527
528
529
530
531
532
533
534</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 508</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_fields'>implemented_fields</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_fields</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_fields</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_implemented_bases'>implemented_bases</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span> <span class='op'>||</span>
       <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>UXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span> <span class='op'>||</span>
       <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span> <span class='op'>||</span>
       <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VUXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span>
      <span class='lbracket'>[</span><span class='int'>32</span><span class='comma'>,</span><span class='int'>64</span><span class='rbracket'>]</span>
    <span class='kw'>else</span>
      <span class='lbracket'>[</span><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rbracket'>]</span>
    <span class='kw'>end</span>

  <span class='ivar'>@implemented_fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>fields</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each_value'>each_value</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_field_data'>field_data</span><span class='op'>|</span>
    <span class='kw'>next</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_implemented_bases'>implemented_bases</span><span class='period'>.</span><span class='id identifier rubyid_none?'>none?</span><span class='lparen'>(</span><span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>+=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='op'>!</span><span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='lparen'>(</span><span class='id identifier rubyid_defined_by'>defined_by</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='op'>|</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='rparen'>)</span> <span class='rbrace'>}</span><span class='rparen'>)</span>
      <span class='ivar'>@implemented_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="CsrField.html#initialize-instance_method" title="CsrField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>

  <span class='ivar'>@implemented_fields</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_fields_for-instance_method">
  
    #<strong>implemented_fields_for</strong>(effective_xlen)  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns All implemented fields for this CSR at the given effective XLEN, sorted by location (smallest location first) Excluded any fields that are defined by unimplemented extensions or a base that is not effective_xlen.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>All implemented fields for this CSR at the given effective XLEN, sorted by location (smallest location first) Excluded any fields that are defined by unimplemented extensions or a base that is not effective_xlen</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


484
485
486
487
488
489
490
491
492
493
494
495
496
497
498
499
500
501
502
503
504</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 484</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_fields_for'>implemented_fields_for</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>
  <span class='ivar'>@implemented_fields_for</span> <span class='op'>||=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>fields</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each_value'>each_value</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_field_data'>field_data</span><span class='op'>|</span>
    <span class='kw'>next</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>!=</span> <span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>String</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_defined_by'>defined_by</span> <span class='op'>+=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='kw'>if</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='op'>!</span><span class='id identifier rubyid_field_data'>field_data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='lparen'>(</span><span class='id identifier rubyid_defined_by'>defined_by</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='op'>|</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='rparen'>)</span> <span class='rbrace'>}</span><span class='rparen'>)</span>
      <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="CsrField.html" title="CsrField (class)">CsrField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="CsrField.html#initialize-instance_method" title="CsrField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_sort!'>sort!</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='comma'>,</span> <span class='id identifier rubyid_b'>b</span><span class='op'>|</span>
    <span class='id identifier rubyid_a'>a</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span> <span class='op'>&lt;=&gt;</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span>
  <span class='kw'>end</span>

  <span class='ivar'>@implemented_fields_for</span><span class='lbracket'>[</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="length-instance_method">
  
    #<strong>length</strong>(effective_xlen = nil)  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Length, in bits, of the CSR.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>effective_xlen</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The effective xlen, needed since some fields change location with XLEN. If the field location is not determined by XLEN, then this parameter can be nil</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Length, in bits, of the CSR</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 386</span>

<span class='kw'>def</span> <span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span> <span class='op'>=</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='kw'>case</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>MXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>XLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>effective_xlen is required when length is dynamic (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

      <span class='id identifier rubyid_effective_xlen'>effective_xlen</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not implemented</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_implemented_csrs'>implemented_csrs</span><span class='period'>.</span><span class='id identifier rubyid_none?'>none?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_c'>c</span><span class='op'>|</span> <span class='id identifier rubyid_c'>c</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='id identifier rubyid_name'>name</span> <span class='rbrace'>}</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not implemented</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

      <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>effective_xlen is required when length is dynamic (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

      <span class='id identifier rubyid_effective_xlen'>effective_xlen</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> is not implemented</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

      <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_config_params'>config_params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
  <span class='kw'>when</span> <span class='const'>Integer</span>
    <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected length field for </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="length_cond32-instance_method">
  
    #<strong>length_cond32</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns IDL condition of when the effective xlen is 32.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL condition of when the effective xlen is 32</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


419
420
421
422
423
424
425
426
427
428</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 419</span>

<span class='kw'>def</span> <span class='id identifier rubyid_length_cond32'>length_cond32</span>
  <span class='kw'>case</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[mstatus].SXL == 0</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[hstatus].VSXL == 0</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected length </span><span class='embexpr_beg'>#{</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>length</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'> for </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="length_cond64-instance_method">
  
    #<strong>length_cond64</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns IDL condition of when the effective xlen is 64.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL condition of when the effective xlen is 64</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


431
432
433
434
435
436
437
438
439
440</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 431</span>

<span class='kw'>def</span> <span class='id identifier rubyid_length_cond64'>length_cond64</span>
  <span class='kw'>case</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[mstatus].SXL == 1</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[hstatus].VSXL == 1</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected length</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="length_pretty-instance_method">
  
    #<strong>length_pretty</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Pretty-printed length string.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Pretty-printed length string</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


443
444
445
446
447
448
449
450
451
452
453
454
455
456
457
458
459
460
461
462</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 443</span>

<span class='kw'>def</span> <span class='id identifier rubyid_length_pretty'>length_pretty</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_dynamic_length?'>dynamic_length?</span>
    <span class='id identifier rubyid_cond'>cond</span> <span class='op'>=</span> 
      <span class='kw'>case</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>length</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[mstatus].SXL == %%</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>when</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span>
        <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>CSR[hstatus].VSXL == %%</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>else</span>
        <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Unexpected length</span><span class='tstring_end'>&quot;</span></span>
      <span class='kw'>end</span>

    <span class='heredoc_beg'>&lt;&lt;~LENGTH</span>
<span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='int'>32</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> when </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_cond'>cond</span><span class='period'>.</span><span class='id identifier rubyid_sub'>sub</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>%%</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>0</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='int'>64</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> when </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_cond'>cond</span><span class='period'>.</span><span class='id identifier rubyid_sub'>sub</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>%%</span><span class='tstring_end'>&#39;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>1</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    LENGTH
</span>  <span class='kw'>else</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_length'>length</span><span class='embexpr_end'>}</span><span class='tstring_content'>-bit</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="wavedrom_desc-instance_method">
  
    #<strong>wavedrom_desc</strong>(effective_xlen)  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns A JSON representation of the WaveDrom drawing for the CSR.</p>


  </div>
</div>
<div class="tags">
  
  <div class="examples">
    <p class="tag_title">Examples:</p>
    
      
        <p class="example_title"><div class='inline'>
<p>Result for an I-type instruction</p>
</div></p>
      
      <pre class="example code"><code>{reg: [
  {bits: 7,  name: &#39;OP-IMM&#39;,    attr: [&#39;{op_major_name}&#39;], type: 8},
  {bits: 5,  name: &#39;rd&#39;,        attr: [&#39;&#39;], type: 2},
  {bits: 3,  name: {funct3},    attr: [&#39;{mnemonic}&#39;], type: 8},
  {bits: 5,  name: &#39;rs1&#39;,       attr: [&#39;&#39;], type: 4},
  {bits: 12, name: &#39;imm12&#39;,     attr: [&#39;&#39;], type: 6}
]}</code></pre>
    
  </div>
<p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>effective_xlen</span>
      
      
        <span class='type'>(<tt>Integer</tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Effective XLEN to use when CSR length is dynamic</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A JSON representation of the WaveDrom drawing for the CSR</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


580
581
582
583
584
585
586
587
588
589
590
591
592
593
594
595
596
597
598
599
600
601
602</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 580</span>

<span class='kw'>def</span> <span class='id identifier rubyid_wavedrom_desc'>wavedrom_desc</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_desc'>desc</span> <span class='op'>=</span> <span class='lbrace'>{</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='rbrace'>}</span>
  <span class='id identifier rubyid_last_idx'>last_idx</span> <span class='op'>=</span> <span class='op'>-</span><span class='int'>1</span>
  <span class='id identifier rubyid_implemented_fields_for'>implemented_fields_for</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_field'>field</span><span class='op'>|</span>

    <span class='kw'>if</span> <span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_min'>min</span> <span class='op'>!=</span> <span class='id identifier rubyid_last_idx'>last_idx</span> <span class='op'>+</span> <span class='int'>1</span>
      <span class='comment'># have some reserved space
</span>      <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>&lt;&lt;</span> <span class='lbrace'>{</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bits</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='lparen'>(</span><span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_min'>min</span> <span class='op'>-</span> <span class='id identifier rubyid_last_idx'>last_idx</span> <span class='op'>-</span> <span class='int'>1</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='int'>1</span> <span class='rbrace'>}</span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>&lt;&lt;</span> <span class='lbrace'>{</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bits</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='int'>2</span> <span class='rbrace'>}</span>
    <span class='id identifier rubyid_last_idx'>last_idx</span> <span class='op'>=</span> <span class='id identifier rubyid_field'>field</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span>
  <span class='kw'>end</span>
  <span class='kw'>if</span> <span class='op'>!</span><span class='id identifier rubyid_implemented_fields_for'>implemented_fields_for</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='id identifier rubyid_fields'>fields</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span><span class='period'>.</span><span class='id identifier rubyid_location'>location</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span> <span class='op'>!=</span> <span class='lparen'>(</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span> <span class='op'>-</span> <span class='int'>1</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='comment'># reserved space at the end
</span>    <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>&lt;&lt;</span> <span class='lbrace'>{</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bits</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='lparen'>(</span><span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span> <span class='op'>-</span> <span class='int'>1</span> <span class='op'>-</span> <span class='id identifier rubyid_last_idx'>last_idx</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='int'>1</span> <span class='rbrace'>}</span>
    <span class='comment'># desc[&#39;reg&#39;] &lt;&lt; { &#39;bits&#39; =&gt; 1, type: 1 }
</span>  <span class='kw'>end</span>
  <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>config</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='lbrace'>{</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bits</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
  <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>config</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>lanes</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_length'>length</span><span class='lparen'>(</span><span class='id identifier rubyid_effective_xlen'>effective_xlen</span><span class='rparen'>)</span> <span class='op'>/</span> <span class='int'>16</span>
  <span class='id identifier rubyid_desc'>desc</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>