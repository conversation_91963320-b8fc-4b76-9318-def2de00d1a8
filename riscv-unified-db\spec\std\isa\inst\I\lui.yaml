# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: lui
long_name: Load upper immediate
description: Load the zero-extended imm into xd.
definedBy: I
assembly: xd, imm
encoding:
  match: -------------------------0110111
  variables:
    - name: imm
      location: 31-12
      left_shift: 12
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): X[xd] = imm;

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let off : xlenbits = sign_extend(imm @ 0x000);
    let ret : xlenbits = match op {
      RISCV_LUI   => off,
      RISCV_AUIPC => get_arch_pc() + off
    };
    X(xd) = ret;
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
