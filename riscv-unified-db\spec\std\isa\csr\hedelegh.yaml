# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hedelegh
long_name: Hypervisor Exception Delegation High
address: 0x612
writable: true
base: 32
priv_mode: S
length: 32
description: |
  Controls exception delegation from HS-mode to VS-mode.

  Alias of upper bits of `hedeleg`[63:32].
definedBy: H
fields: {}
