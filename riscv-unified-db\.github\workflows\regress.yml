name: Regression test
permissions:
  contents: read
  pull-requests: write
on:
  merge_group:
    types: [checks_requested]
  pull_request:
    branches:
      - main
  workflow_dispatch:
jobs:
  regress-pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - uses: pre-commit/action@v3.0.1
  regress-smoke:
    needs: build-llvm
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: Get current LLVM submodule commit SHA
        id: get-llvm-sha
        run: echo "LLVM_SHA=$(git ls-tree HEAD ext/llvm-project | awk '{print $3}')" >> $GITHUB_ENV
      - name: Restore cache RISC-V JSON
        id: cache-riscv
        uses: actions/cache@v4
        with:
          path: ext/llvm-project/riscv.json
          key: ${{ runner.os }}-riscv-json-${{ env.LLVM_SHA }}
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Run smoke
        run: ./do test:smoke
      - name: Upload idlc coverage reports to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          disable_search: true
          files: tools/ruby-gems/idlc/coverage/coverage.xml
          flags: idlc
  regress-gen-isa-manual:
    runs-on: ubuntu-latest
    env:
      MANUAL_NAME: isa
      VERSIONS: all
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate HTML ISA manual
        run: ./do gen:html_manual
  regress-gen-instruction-appendix:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate instruction appendix asciidoc
        run: ./do gen:instruction_appendix_adoc
      - name: Check instruction appendix result
        run: ./do test:instruction_appendix
  regress-cfg-manual:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate HTML ISA manual
        run: ./do gen:html[example_rv64_with_overlay]
  regress-gen-ext-pdf:
    runs-on: ubuntu-latest
    env:
      EXT: B
      VERSION: latest
      SINGULARITY: 1
      PSEUDO: both
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate extension PDF
        run: ./do gen:ext_pdf
  regress-gen-certificate:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate extension PDF
        run: ./do gen:proc_crd_pdf[MockProcessor]
  regress-gen-profile:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate extension PDF
        run: ./do gen:profile_release_pdf[Mock]
  build-llvm:
    runs-on: ubuntu-latest
    steps:
      - name: Check out repository (no submodules, shallow fetch)
        uses: actions/checkout@v4
        with:
          submodules: false
          fetch-depth: 1
      - name: Get current LLVM submodule commit SHA
        id: get-llvm-sha
        run: echo "LLVM_SHA=$(git ls-tree HEAD ext/llvm-project | awk '{print $3}')" >> $GITHUB_ENV
      - name: Cache RISC-V JSON
        id: cache-riscv
        uses: actions/cache@v4
        with:
          path: ext/llvm-project/riscv.json
          key: ${{ runner.os }}-riscv-json-${{ env.LLVM_SHA }}
      - name: Initialize LLVM submodule (shallow + sparse)
        if: ${{ steps.cache-riscv.outputs.cache-hit != 'true' }}
        run: |
          rm -rf ext/llvm-project
          git submodule sync --recursive
          git submodule update --init --recursive --depth=1 ext/llvm-project
      - name: Check for required directories and files
        if: ${{ steps.cache-riscv.outputs.cache-hit != 'true' }}
        run: |
          ls -l ext/llvm-project/llvm/include
          ls -l ext/llvm-project/llvm/lib/Target/RISCV
          ls -l ext/llvm-project/llvm/lib/Target/RISCV/RISCV.td
      - name: Configure and build llvm-tblgen
        if: ${{ steps.cache-riscv.outputs.cache-hit != 'true' }}
        run: |
          cmake -S ext/llvm-project/llvm -B ext/llvm-project/build -DCMAKE_BUILD_TYPE=Release
          cmake --build ext/llvm-project/build --target llvm-tblgen
      - name: Generate RISC-V JSON
        if: ${{ steps.cache-riscv.outputs.cache-hit != 'true' }}
        run: |
          chmod +x ./ext/llvm-project/build/bin/llvm-tblgen
          ./ext/llvm-project/build/bin/llvm-tblgen \
            -I ext/llvm-project/llvm/include \
            -I ext/llvm-project/llvm/lib/Target/RISCV \
            ext/llvm-project/llvm/lib/Target/RISCV/RISCV.td \
            --dump-json \
            -o ext/llvm-project/riscv.json
      - name: Show riscv.json output
        run: ls -l ext/llvm-project/riscv.json
      - name: Upload RISC-V JSON as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: riscv-json
          path: ext/llvm-project/riscv.json
  regress-gen-go:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate Go code
        run: ./do gen:go
  regress-gen-c-header:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Generate c_header code
        run: ./do gen:c_header
  regress-cpp-unit:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Run cpp unit tests
        run: ./do test:cpp_hart CONFIG=rv64 JOBS=4
  regress-xqci-doc:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4
      - name: singularity setup
        uses: ./.github/actions/singularity-setup
      - name: Run cpp unit tests
        run: ./do gen:ext_pdf EXT=Xqci CFG=qc_iu.yaml VERSION=latest
  call-deploy:
    uses: ./.github/workflows/merge.yml
    with:
      dry-run: true
