---
site:
  title: <%= manual.marketing_name %>
  start_page: landing:ROOT:index.adoc
content:
  sources:
  - url: <%= $root %>
    start_path: gen/manual/<%= manual.name %>/top/<%= output_hash %>/antora/landing
  <%- manual.versions.each do |version| -%>
  - url: <%= $root %>
    start_path: gen/manual/<%= manual.name %>/<%= version.name %>/antora
  <%- end -%>
antora:
  extensions:
  - '@antora/lunr-extension'
asciidoc:
  extensions:
  - 'asciidoctor-kroki'
  - '@asciidoctor/tabs'
ui:
  bundle:
    url: https://gitlab.com/antora/antora-ui-default/-/jobs/artifacts/HEAD/raw/build/ui-bundle.zip?job=bundle-stable
    snapshot: true
  supplemental_files:
  - path: css/vendor/tabs.css
    contents: <%= $root %>/node_modules/@asciidoctor/tabs/dist/css/tabs.css
  - path: js/vendor/tabs.js
    contents: <%= $root %>/node_modules/@asciidoctor/tabs/dist/js/tabs.js
  - path: partials/footer-scripts.hbs
    contents: |
      <script id="site-script" src="{{{uiRootPath}}}/js/site.js" data-ui-root-path="{{{uiRootPath}}}"></script>
      <script async src="{{{uiRootPath}}}/js/vendor/highlight.js"></script>
      <script async src="{{{uiRootPath}}}/js/vendor/tabs.js"></script>
      {{#if env.SITE_SEARCH_PROVIDER}}
      {{> search-scripts}}
      {{/if}}
  - path: partials/head-styles.hbs
    contents: |
      <link rel="stylesheet" href="{{{uiRootPath}}}/css/site.css">
      <link rel="stylesheet" href="{{{uiRootPath}}}/css/vendor/tabs.css">
      <link rel="stylesheet" href="{{{uiRootPath}}}/css/vendor/custom.css">
  - path: js/vendor/highlight.js
    contents: <%= $root %>/backends/cfg_html_doc/ui/highlight.js
  - path: css/vendor/custom.css
    contents: |
      .small {
        font-size: 50%;
        font-weight: normal;
      }

      code.language-idl > a {
        text-decoration: none;
      }

      /* rotate text vertically */
      span.rotate {
        writing-mode: vertical-lr;
      }

      span.access-always {
        background-color: green;
        display: block;
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        color: white;
        font-weight: bold;
      }

      span.access-sometimes {
        background-color: rgb(255, 193, 7);
        display: block;
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        color: black;
        font-weight: bold;
      }

      span.access-never {
        background-color: #e71324;
        display: block;
        padding: 16px;
        border-radius: 8px;
        text-align: center;
        color: white;
        font-weight: bold;
      }

      .csr-field-info {
        margin-left: 1em;
        padding: 1em;
        border-radius: 25px;
        border: 2px solid;
      }

      .doc .admonitionblock td.icon i.icon-when {
        background-color: #fffdd0;
        color: #000;
      }

      .doc .admonitionblock td.icon i.icon-when::after {
        text-transform: none;
      }

      .edit-this-page {
         display: none !important;
      }
  - path: partials/header-content.hbs
    contents: |
      <header class="header">
        <nav class="navbar">
          <div class="navbar-brand">
            <a class="navbar-item" href="{{{or site.url siteRootPath}}}">{{site.title}}</a>
            {{#if env.SITE_SEARCH_PROVIDER}}
            <div class="navbar-item search hide-for-print">
              <div id="search-field" class="field">
                <input id="search-input" type="text" placeholder="Search the docs"{{#if page.home}} autofocus{{/if}}>
              </div>
            </div>
            {{/if}}
          </div>
        </nav>
      </header>
