# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mstatus
long_name: Machine Status
address: 0x300
writable: true
priv_mode: M

# length is MXLEN-bit
#
# MXLEN cannot change dynamically, so this will be converted to an integer
# in the generated, configuration-dependent spec
length: MXLEN
description:
  The mstatus register tracks and controls the hart's current operating
  state.
definedBy: Sm
fields:
  SD:
    location_rv32: 31
    location_rv64: 63
    description: |
      State Dirty.

      Read-only bit that summarizes whether either the FS, XS, or VS
      fields signal the presence of some dirty state.
    definedBy:
      anyOf: [F, V] # NOTE: if you implement a custom extension overlay that writes to XS, then you need to add your extension here in the overlay as well
    type(): |
      # this is read-only if FS and VS are both read-only
      # otherwise, it is read-only with hardware update

      if (implemented?(ExtensionName::F) && (HW_MSTATUS_FS_DIRTY_UPDATE != "never")) {
        return CsrFieldType::ROH;
      } else if (implemented?(ExtensionName::V) && (HW_MSTATUS_VS_DIRTY_UPDATE != "never")) {
        return CsrFieldType::ROH;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      # the reset value is known if both FS and VS are legal
      # make this a function call so that it can be displayed succinctly in docs

      if (mstatus_sd_has_known_reset()) {
        return mstatus_sd_reset_value();
      } else {
        return UNDEFINED_LEGAL;
      }

  MDT:
    location: 42
    base: 64
    description: |
      *Machine Disable Trap*

      Written to 1 when entering M-mode from an exception/interrupt.
      When returning via an MRET instruction, the bit is written to 0.
      On reset in set to 1, and software should write it to 0 when boot sequence is done.
      When mstatus.MDT=1, direct write by CSR instruction cannot set mstatus.MIE to 1, if not written together.
    definedBy: Smdbltrp
    type: RW-H
    reset_value: UNDEFINED_LEGAL
  MPV:
    location: 39
    base: 64
    description: |
      *Machine Previous Virtualization mode*

      Written with the prior virtualization mode when entering M-mode from an exception/interrupt.
      When returning via an MRET instruction, the virtualization mode becomes the value of MPV unless MPP=3, in which case the virtualization mode is always 0.
      Can also be written by software.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
    definedBy: H
  GVA:
    location: 38
    base: 64
    description: |
      *Guest Virtual Address*

      When a trap is taken and a guest virtual address is written into mtval, GVA is set.
      When a trap is taken and a guest virtual address is written into mtval, GVA is cleared.
    type: RW-H
    reset_value: 0
    definedBy: H
  MBE:
    location: 37
    base: 64
    description: |
      *M-mode Big Endian*

      Controls the endianness of data M-mode (0 = little, 1 = big).
      Instructions are always little endian, regardless of the data setting.

      [when,"M_MODE_ENDIANNESS == little"]
      Since the CPU does not support big endian, this is hardwired to 0.

      [when,"M_MODE_ENDIANNESS == big"]
      Since the CPU does not support little endian, this is hardwired to 1.
    type(): |
      return (M_MODE_ENDIANNESS == "dynamic") ? CsrFieldType::RW : CsrFieldType::RO;

    # if endianness is mutable, MBE comes out of reset in little-endian mode
    reset_value(): |
      return (M_MODE_ENDIANNESS == "big") ? 1 : 0;

  SBE:
    location: 36
    base: 64
    definedBy: S
    description: |
      *S-mode Big Endian*

      Controls the endianness of S-mode (0 = little, 1 = big).
      Instructions are always little endian, regardless of the data setting.

      [when,"S_MODE_ENDIANNESS == little"]
      Since the CPU does not support big endian, this is hardwired to 0.

      [when,"S_MODE_ENDIANNESS == big"]
      Since the CPU does not support little endian, this is hardwired to 1.
    type(): |
      return (S_MODE_ENDIANNESS == "dynamic") ? CsrFieldType::RW : CsrFieldType::RO;

    # if endianness is mutable, MBE comes out of reset in little-endian mode
    reset_value(): |
      if (S_MODE_ENDIANNESS == "little") {
        return 0;
      } else if (S_MODE_ENDIANNESS == "big") {
        return 1;
      } else {
        return UNDEFINED_LEGAL;
      }
  SXL:
    location: 35-34
    base: 64
    definedBy: S
    description: |
      *S-mode XLEN*

      Sets the effective XLEN for S-mode (0 = 32-bit, 1 = 64-bit, 2 = 128-bit [reserved]).

      [when,"SXLEN==32"]
      Since the CPU only supports SXLEN==32, this is hardwired to 0.

      [when,"SXLEN==32"]
      Since the CPU only supports SXLEN==64, this is hardwired to 1.

      [when,"SXLEN=3264"]
      --
      It is not valid to have SXLEN less than UXLEN.

      It is UNDEFINED_LEGAL what will happen if a software sets `mstatus.SXL` to be greater than `mstatus.UXL`.

      It is UNDEFINED_LEGAL to set the MSB of SXL.
      --
    type(): |
      return (implemented?(ExtensionName::S) && SXLEN == 3264) ? CsrFieldType::RW : CsrFieldType::RO;

    legal?(csr_value): |
      if (SXLEN == 32) {
        # SXLEN == 32 is encoded as 0
        return csr_value.SXL == 0;
      } else if (SXLEN == 64) {
        # SXLEN == 64 is encoded as 1
        return csr_value.SXL == 1;
      } else {
        # SXLEN could be 32 or 64
        return csr_value.SXL <= 1;
      }

    sw_write(csr_value): |
      if (csr_value.SXL < csr_value.UXL) {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else if (csr_value.SXL > 1) {
        # SXL != [0, 1] is not defined (2 reserved for RV128, but that isn't ratified)
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else {
        return csr_value.SXL;
      }
    reset_value: UNDEFINED_LEGAL

  UXL:
    location: 33-32
    base: 64
    definedBy: U
    description: |
      U-mode XLEN.

      Sets the effective XLEN for U-mode (0 = 32-bit, 1 = 64-bit, 2 = 128-bit [reserved]).

      [when,"UXLEN == 32"]
      Since the CPU only supports UXLEN==32, this is hardwired to 0.

      [when,"UXLEN == 64"]
      Since the CPU only supports UXLEN==64, this is hardwired to 1.


      [when,"UXLEN == 3264"]
      --
      It is not valid to have SXLEN less than UXLEN.

      It is UNDEFINED_LEGAL what will happen if a software sets `mstatus.SXL` to be greater than `mstatus.UXL`.

      It is UNDEFINED_LEGAL to set the MSB of UXL.
      --
    type(): |
      return (UXLEN == 3264) ? CsrFieldType::RW : CsrFieldType::RO;

    sw_write(csr_value): |
      if (csr_value.SXL < csr_value.UXL) {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else if (csr_value.UXL > 1) {
        # UXL != [0, 1] is not defined (2 reserved for RV128, but that isn't ratified)
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else {
        return csr_value.UXL;
      }

    legal?(csr_value): |
      if (UXLEN == 32) {
        return csr_value.UXL == 0;
      } else if (UXLEN == 64) {
        return csr_value.UXL == 1;
      } else {
        return csr_value.UXL <= 1;
      }

    reset_value(): |
      if (UXLEN == 32) {
        return 0;
      } else if (UXLEN == 64) {
        return 1;
      } else {
        return UNDEFINED_LEGAL;
      }

  TSR:
    location: 22
    description: |
      *Trap SRET*

      When 1, attempts to execute the `sret` instruction while executing in HS/S-mode
      will raise an Illegal Instruction exception.

      [when,"ext?(:H)"]
      Does not affect the behavior of `sret` in VS_mode (see `hstatus.VTSR`).
    type: RW
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  TW:
    location: 21
    description: |
      *Timeout Wait*

      When 1, the WFI instruction will raise an Illegal Instruction trap after an
      implementaion-defined wait period when executed in a mode other than M-mode.

      When 0, the `wfi` instruction is permitted to wait forever in (H)S-mode but must
      trap after an implementation-defined wait period in U-mode.
    type: RW
    definedBy: S
    reset_value: UNDEFINED_LEGAL
  TVM:
    location: 20
    description: |
      Trap Virtual Memory.

      When 1, an `Illegal Instruction` trap occurs when

      * writing the `satp` CSR, executing an `sfence.vma`, or executing an `sinval.vma` while in (H)S-mode (but not VS-mode)
      * writing the `hgtap` CSR, executing an `hfence.gvma`, or executing an `hinval.gvma` while in HS-mode

      Notably, `mstatus.TVM` does *not* cause

      *`hfence.vvma`, `sfence.w.inval`, or `sfence.inval.ir` to trap.
      * Any additional traps in VS-mode (controlled via `hstatus.VTVM` instead).

    type(): |
      if (CSR[misa].S == 1'b0) {
        return CsrFieldType::RO;
      } else {
        return CsrFieldType::RW;
      }
    definedBy: S
    reset_value(): |
      if (CSR[misa].S == 1'b0) {
        return 0;
      } else if (MSTATUS_TVM_IMPLEMENTED) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    sw_write(csr_value): |
      if (CSR[misa].S == 1'b0) {
        return 0;
      } else if (MSTATUS_TVM_IMPLEMENTED) {
        return csr_value.TVM;
      } else {
        return 0;
      }
  MXR:
    location: 19
    description: |
      Make eXecutable Readable.

      When 1, loads from pages marked readable *or executable* are allowed.
      When 0, loads from pages marked executable raise a Page Fault exception.
    definedBy: S
    type: RW
    reset_value: UNDEFINED_LEGAL
  SUM:
    location: 18
    description: |
      permit Supervisor Memory Access.

      When 0, an S-mode read or an M-mode read with mstatus.MPRV=1 and mstatus.MPP=01
      to a 'U' (user) page will cause an ILLEGAL INSTRUCTION exception.
    definedBy: S
    type(): |
      # only writable if there is some translation supported
      if (has_virt_mem?()) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (has_virt_mem?()) {
        return UNDEFINED_LEGAL;
      } else {
        # read-only zero when there is no virtual memory
        return 0;
      }
  MPRV:
    location: 17
    description: |
      Modify PRiVilege.

      When 1, loads and stores behave as if the current virtualization mode:privilege level was
      `mstatus.MPV`:`mstatus.MPP`.

      `mstatus.MPRV` is cleared on any exception return (`mret` or `sret` instruction, regardless of the trap handler privilege mode).
    definedBy: U
    type(): |
      return (CSR[misa].U == 1'b1) ? CsrFieldType::RWH : CsrFieldType::RO;
    reset_value: 0
  XS:
    location: 16-15
    description: |
      *Custom (X) extension context Status*

      Summarizes the current state of any custom extension state.
      Either 0 - Off, 1 - Initial, 2 - Clean, 3 - Dirty.
      Since there are no custom extensions in the base spec, this field is read-only 0.
    type: RO
    reset_value: 0
  FS:
    location: 14-13
    description: |
      Floating point context status.

      When 0, floating point instructions (from F and D extensions) are disabled,
      and cause `ILLEGAL INSTRUCTION` exceptions.
      When a floating point register, or the fCSR register is written, FS obtains the value 3.
      Values 1 and 2 are valid write values for software, but are not interpreted by hardware
      other than to possibly enable a previously-disabled floating point unit.
    type(): |
      if (CSR[misa].F == 1'b1){
        return CsrFieldType::RWH;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].F == 1'b0)) {
        # must be read-only-0
        return CsrFieldType::RO;
      } else {
        # there will be no hardware update in this case because we know the F extension isn't implemented
        return MSTATUS_FS_WRITABLE ? CsrFieldType::RW : CsrFieldType::RO;
      }
    definedBy:
      anyOf: [F, S]
    reset_value(): |
      if (CSR[misa].F == 1'b1){
        return UNDEFINED_LEGAL;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].F == 1'b0)) {
        # must be read-only-0
        return 0;
      } else {
        # there will be no hardware update in this case because we know the F extension isn't implemented
        return MSTATUS_FS_WRITABLE ? UNDEFINED_LEGAL : 0;
      }
    sw_write(csr_value): |
      if (CSR[misa].F == 1'b1){
        return ary_includes?<$array_size(MSTATUS_FS_LEGAL_VALUES), 2>(MSTATUS_FS_LEGAL_VALUES, csr_value.FS) ? csr_value.FS : UNDEFINED_LEGAL_DETERMINISTIC;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].F == 1'b0)) {
        # must be read-only-0
        return 0;
      } else {
        # there will be no hardware update in this case because we know the F extension isn't implemented
        return ary_includes?<$array_size(MSTATUS_FS_LEGAL_VALUES), 2>(MSTATUS_FS_LEGAL_VALUES, csr_value.FS) ? csr_value.FS : UNDEFINED_LEGAL_DETERMINISTIC;
      }
  MPP:
    location: 12-11
    description: |
      M-mode Previous Privilege.

      Written by hardware in two cases:

      * Written with the prior nominal privilege level when entering M-mode from an exception/interrupt.
      * Written with 0 when executing an `mret` instruction to return from an exception in M-mode.

      Can also be written by software without immediate side-effect.

      Affects execution in two cases:

      * On a return from an exception from M-mode, the machine will
      enter the privilege level stored in MPP before clearing the field.
      * When `mstatus.MPRV` is set, loads and stores behave as if the current privilege level were MPP.
    type: RW-H
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (csr_value.MPP == 2'b01 && !implemented?(ExtensionName::S)) {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else if (csr_value.MPP == 2'b00 && !implemented?(ExtensionName::U)) {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else if (csr_value.MPP == 2'b10) {
        # never a valid value
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else {
        return csr_value.MPP;
      }
    legal?(csr_value): |
      if (csr_value.MPP == 2'b01 && !implemented?(ExtensionName::S)) {
        return false;
      } else if (csr_value.MPP == 2'b00 && !implemented?(ExtensionName::U)) {
        return false;
      } else if (csr_value.MPP == 2'b10) {
        # never a valid value
        return false;
      } else {
        return true;
      }
  VS:
    location: 10-9
    description: |
      Vector context status.

      When 0, vector instructions (from the V extension) are disabled, and cause ILLEGAL INSTRUCTION exceptions.
      When a vector register or vector CSR is written, VS obtains the value 3.
      Values 1 and 2 are valid write values for software, but are not interpreted by hardware
      other than to possibly enable a previously-disabled vector unit.
    definedBy:
      anyOf: [V, S]
    type(): |
      if (CSR[misa].V == 1'b1){
        return CsrFieldType::RWH;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].V == 1'b0)) {
        # must be read-only-0
        return CsrFieldType::RO;
      } else {
        # there will be no hardware update in this case because we know the V extension isn't implemented
        return MSTATUS_VS_WRITABLE ? CsrFieldType::RW : CsrFieldType::RO;
      }
    reset_value(): |
      if (CSR[misa].V == 1'b1){
        return UNDEFINED_LEGAL;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].V == 1'b0)) {
        # must be read-only-0
        return 0;
      } else {
        # there will be no hardware update in this case because we know the V extension isn't implemented
        return MSTATUS_VS_WRITABLE ? UNDEFINED_LEGAL : 0;
      }
    sw_write(csr_value): |
      if (CSR[misa].V == 1'b1){
        return ary_includes?<$array_size(MSTATUS_VS_LEGAL_VALUES), 2>(MSTATUS_VS_LEGAL_VALUES, csr_value.FS) ? csr_value.FS : UNDEFINED_LEGAL_DETERMINISTIC;
      } else if ((CSR[misa].S == 1'b0) && (CSR[misa].V == 1'b0)) {
        # must be read-only-0
        return 0;
      } else {
        # there will be no hardware update in this case because we know the V extension isn't implemented
        return ary_includes?<$array_size(MSTATUS_VS_LEGAL_VALUES), 2>(MSTATUS_VS_LEGAL_VALUES, csr_value.FS) ? csr_value.FS : UNDEFINED_LEGAL_DETERMINISTIC;
      }
  SPP:
    location: 8
    description: |
      *S-mode Previous Privilege*

      Written by hardware in two cases:

      * Written with the prior nominal privilege level when entering (H)S-mode from an exception/interrupt.
      * Written with 0 when executing an `sret` instruction to return from an exception in (H)S-mode or (unlikely) M-mode.

      Can also be written by software without immediate side-effect.

      Affects execution in one case:

      * On a return from an exception using the `sret` instruction in (H)S-mode or (unlikely) M-mode,
        the machine will enter the privilege level stored in SPP before clearing the field.

      Notably, `mstatus.SPP` does not affect exception return in VS-mode (see `vsstatus.SPP`).

    type: RW-H
    definedBy: S
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (csr_value.SPP == 2'b10) {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else {
        return csr_value.SPP;
      }
    legal?(csr_value): |
      return csr_value.SPP != 2'b10;
  MPIE:
    location: 7
    description: |
      *M-mode Previous Interrupt Enable*

      Written by hardware in two cases:

      * Written with prior value of `mstatus.MIE` when entering M-mode from an exception/interrupt.
      * Written with the value 1 when returning from an exception in M-mode (via the `mret` instruction).

      Can also be written by software without immediate side effect.

      Other than serving as a record of nested traps as described above, `mstatus.MPIE` does not affect execution.

    type: RW-H
    reset_value: UNDEFINED_LEGAL
  UBE:
    location: 6
    definedBy: U
    description: |
      *U-mode Big Endian*

      Controls the endianness of U-mode (0 = little, 1 = big).
      Instructions are always little endian, regardless of the data setting.

      [when,"U_MODE_ENDIANNESS == 'little'"]
      Since the CPU does not support big endian in U-mode, this is hardwired to 0.

      [when,"U_MODE_ENDIANNESS == 'big'"]
      Since the CPU does not support little endian in U-mode, this is hardwired to 1.
    type(): |
      return (U_MODE_ENDIANNESS == "dynamic") ? CsrFieldType::RW : CsrFieldType::RO;

    reset_value(): |
      if (U_MODE_ENDIANNESS == "little") {
        return 0;
      } else if (U_MODE_ENDIANNESS == "big") {
        return 1;
      } else {
        return UNDEFINED_LEGAL;
      }

  SPIE:
    location: 5
    description: |
      *S-mode Previous Interrupt Enable*

      Written by hardware in two cases:

      * Written with prior value of `mstatus.SIE` when entering (H)S-mode from an exception/interrupt.
      * Written with the value 1 when returning from an exception via the `sret` instruction in (H)S-mode or (unlikely) M-mode.

      Can also be written by software without immediate side effect.

      Other than serving as a record of nested traps as described above, `mstatus.SPIE` does not affect execution.
    type(): |
      return (CSR[misa].S == 1'b1) ? CsrFieldType::RWH : CsrFieldType::RO;
    definedBy: S
    reset_value(): |
      return (CSR[misa].S == 1'b1) ? UNDEFINED_LEGAL : 0;
  MIE:
    location: 3
    description: |
      *M-mode Interrupt Enable*

      Written by hardware in two cases:

      * Written with the value 0 when entering M-mode from an exception/interrupt.
      * Written with the prior value of `mstatus.MPIE` when returning from an exception in M-mode (via `mret`).

      Affects execution by:

      * When 0, all interrupts are disabled when the current privilege level is M.
      * When 1, interrupts that are not otherwise disabled with a field in `mie` are enabled.

    type: RW-H
    reset_value: 0
  SIE:
    location: 1
    description: |
      *S-mode Interrupt Enable*

      Written by hardware in two cases:

      * Written with the value 0 when entering (H)S-mode from an exception/interrupt.
      * Written with the prior value of `mstatus.SPIE` when returning from an exception via `sret` in (H)S-mode or (unlikely) M-mode.

      Affects execution by:

      * When 0, all (H)S-mode interrupts are disabled when the current privilege level is (H)S (M-mode interrupts are still enabled).
      * When 1, (H)S-mode interrupts that are not otherwise disabled with a field in `sie` are enabled.

    type(): |
      return (CSR[misa].S == 1'b1) ? CsrFieldType::RWH : CsrFieldType::RO;
    definedBy: S
    reset_value(): |
      return (CSR[misa].S == 1'b1) ? UNDEFINED_LEGAL : 0;
