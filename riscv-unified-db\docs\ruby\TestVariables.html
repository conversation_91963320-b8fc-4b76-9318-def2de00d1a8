<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: TestVariables
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "TestVariables";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (T)</a> &raquo;
    
    
    <span class="title">TestVariables</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: TestVariables
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Minitest::Test</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Minitest::Test</li>
          
            <li class="next">TestVariables</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="TestMixin.html" title="TestMixin (module)">TestMixin</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/tests/test_variables.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>test IDL variables</p>


  </div>
</div>
<div class="tags">
  

</div>






  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#setup-instance_method" title="#setup (instance method)">#<strong>setup</strong>  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="TestMixin.html#setup-instance_method" title="TestMixin#setup (method)">TestMixin</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#test_that_constants_are_read_only-instance_method" title="#test_that_constants_are_read_only (instance method)">#<strong>test_that_constants_are_read_only</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="setup-instance_method">
  
    #<strong>setup</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="TestMixin.html#setup-instance_method" title="TestMixin#setup (method)">TestMixin</a></span>
    </span>
  
</h3>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="test_that_constants_are_read_only-instance_method">
  
    #<strong>test_that_constants_are_read_only</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


12
13
14
15
16
17
18
19
20
21</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/test_variables.rb', line 12</span>

<span class='kw'>def</span> <span class='id identifier rubyid_test_that_constants_are_read_only'>test_that_constants_are_read_only</span>
  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='heredoc_beg'>&lt;&lt;~IDL</span><span class='period'>.</span><span class='id identifier rubyid_strip'>strip</span>
<span class='tstring_content'>    XReg MyConstant = 15;
</span><span class='tstring_content'>    MyContant = 0;
</span><span class='heredoc_end'>  IDL
</span>
  <span class='id identifier rubyid_assert_raises'>assert_raises</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='rparen'>)</span> <span class='kw'>do</span>
    <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_func_body'>compile_func_body</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='label'>symtab:</span> <span class='ivar'>@symtab</span><span class='comma'>,</span> <span class='label'>no_rescue:</span> <span class='kw'>true</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>