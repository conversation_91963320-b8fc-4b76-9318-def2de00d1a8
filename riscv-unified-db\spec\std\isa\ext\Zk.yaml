# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zk
long_name: Standard Cryptography Extensions
description: |
  This extension is shorthand for the following set of other extensions:

  * `Zkn`
  * `Zkr`
  * `Zkt`

type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    implies:
      - name: "Zkn"
        version: "1.0.0"
      - name: "Zkr"
        version: "1.0.0"
      - name: "Zkt"
        version: "1.0.0"
