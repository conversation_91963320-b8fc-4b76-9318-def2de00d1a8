# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.selectieq
long_name: Select load immediate or register if equal (Register)
description: |
  Move `rs2` to `rd` if the value in `rd` is equal to value `rs1`,
  move `simm2` to `rd` otherwise.
  Instruction encoded in R4 instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcics
base: 32
encoding:
  match: -----01----------010-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: simm2
      location: 31-27
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2, simm2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (X[rd] == X[rs1]) {
    X[rd] = X[rs2];
  } else {
    X[rd] = sext(simm2, 5);
  }
