:tabs-sync-option:

// definitions and helper functions for ISA definitions

= Functions

<%- cfg_arch.implemented_functions.each do |f| -%>
[#<%= f.name %>-func-def]
== <%= f.name %><%- if f.builtin? -%> (builtin)<%- end -%><%- if f.generated? -%> (generated)<%- end -%>

<%= f.description %>

|===
h| Return Type l| <%= f.return_type_list_str.join(', ') %>
h| Arguments   l| <%= f.arguments_list_str.join (', ') %>
|===

<%- unless f.builtin? || f.generated? -%>
<%- body_ast = f.body -%>
[tabs]
====
Original::
+
[subs="specialchars,macros"]
----
<%= body_ast.gen_adoc %>
----

Pruned::
+
[subs="specialchars,macros"]
----
<%= body_ast.prune(global_symtab.deep_clone).gen_adoc %>
----
====
<%- end -%>

<%- end -%>
