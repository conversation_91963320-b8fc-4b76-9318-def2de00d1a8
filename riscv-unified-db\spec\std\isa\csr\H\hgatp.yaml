# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hgatp
long_name: Hypervisor guest address translation and protection
description: |
  The hgatp register is an HSXLEN-bit read/write register
  which controls G-stage address translation and protection, the second stage of two-stage
  translation for guest virtual addresses.
  Similar to CSR `satp`, this register holds the physical page number (PPN) of the
  guest-physical root page table;
  a virtual machine identifier (VMID), which facilitates address-translation fences on a
  per-virtual-machine basis;
  and the MODE field, which selects the address-translation scheme for guest physical addresses.
  When `mstatus.TVM`=1, attempts to read or write `hgatp` while executing in HS-mode will
  raise an `IllegalInstruction` exception.

  <<hgatp-mode>> shows the encodings of the MODE field when
  HSXLEN=32 and HSXLEN=64. When MODE=Bare, guest physical addresses are
  equal to supervisor physical addresses, and there is no further memory
  protection for a guest virtual machine beyond the physical memory
  protection scheme described in <<pmp>>. In this
  case, the remaining fields in `hgatp` must be set to zeros.

  [[hgatp-mode]]
  .Encoding of `hgatp` MODE field.
  [%autowidth,float="center",align="center",cols="^,^,<",options="header"]
  |===
  3+|HSXLEN=32
  |Value |Name |Description
  |0 +
  1
  |Bare +
  Sv32x4
  |No translation or protection. +
  Page-based 34-bit virtual addressing (2-bit extension of
  Sv32).
  3+s|HSXLEN=64
  |Value |Name |Description
  |0 +
  1-7 +
  8 +
  9 +
  10 +
  11-15
  |Bare +
  -- +
  Sv39x4 +
  Sv48x4 +
  Sv57x4 +
  --
  |No translation or protection. +
  _Reserved_ +
  Page-based 41-bit virtual addressing (2-bit extension of
  Sv39). +
  Page-based 50-bit virtual addressing (2-bit extension of
  Sv48). +
  Page-based 59-bit virtual addressing (2-bit extension of
  Sv57). +
  _Reserved_
  |===

  Implementations are not required to support all defined MODE settings
  when HSXLEN=64.

  A write to `hgatp` with an unsupported MODE value is not ignored as it
  is for `satp`. Instead, the fields of `hgatp` are *WARL* in the normal way,
  when so indicated.

  As explained in <<guest-addr-translation>>, for the
  paged virtual-memory schemes (Sv32x4, Sv39x4, Sv48x4, and Sv57x4), the
  root page table is 16 KiB and must be aligned to a 16-KiB boundary. In
  these modes, the lowest two bits of the physical page number (PPN) in
  `hgatp` always read as zeros. An implementation that supports only the
  defined paged virtual-memory schemes and/or Bare may make PPN[1:0]
  read-only zero.

  The number of VMID bits is UNSPECIFIED and may be zero. The number of implemented
  VMID bits, termed _VMIDLEN_, may be determined by writing one to every
  bit position in the VMID field, then reading back the value in `hgatp`
  to see which bit positions in the VMID field hold a one. The
  least-significant bits of VMID are implemented first: that is, if
  VMIDLEN > 0, VMID[VMIDLEN-1:0] is writable. The maximal
  value of VMIDLEN, termed VMIDMAX, is 7 for Sv32x4 or 14 for Sv39x4,
  Sv48x4, and Sv57x4.

  The `hgatp` register is considered _active_ for the purposes of the
  address-translation algorithm _unless_ the effective privilege mode is U
  and `hstatus`.HU=0.

  [NOTE]
  ====
  This definition simplifies the implementation of speculative execution
  of `hlv`, `hlvx`, and 'hsv' instructions.
  ====

  Note that writing `hgatp` does not imply any ordering constraints
  between page-table updates and subsequent G-stage address translations.
  If the new virtual machine's guest physical page tables have been
  modified, or if a VMID is reused, it may be necessary to execute an
  HFENCE.GVMA instruction (see <<hfence.vma>>) before or
  after writing `hgatp`.
address: 0x680
writable: true
priv_mode: S
definedBy: H
length: SXLEN
fields:
  MODE:
    location_rv32: 31
    location_rv64: 63-60
    description: |
      When MODE=Bare, guest physical addresses are equal to supervisor physical addresses,
      and there is no further memory protection for a guest virtual machine beyond the
      physical memory protection scheme.
      In this case, the remaining fields in hgatp must be set to zeros.

      When HSXLEN=32, the only other valid setting for MODE is Sv32x4, which is a
      modification of the usual Sv32 paged virtual-memory scheme, extended to support
      34-bit guest physical addresses.
      When HSXLEN=64, modes Sv39x4, Sv48x4, and Sv57x4 are defined as modifications of the
      Sv39, Sv48, and Sv57 paged virtual-memory schemes.

      A write to hgatp with an unsupported MODE value is not ignored as it is for `satp`.
      Instead, the fields of hgatp are WARL in the normal way, when so indicated.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (csr_value.MODE == $bits(HgatpMode::Bare)) {
        if (!GSTAGE_MODE_BARE) {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }

      if (csr_value.MODE == $bits(HgatpMode::Sv32x4)) {
        if (!SV32X4_TRANSLATION) {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }

      if (csr_value.MODE == $bits(HgatpMode::Sv39x4)) {
        if (!SV39X4_TRANSLATION) {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }

      if (csr_value.MODE == $bits(HgatpMode::Sv48x4)) {
        if (!SV48X4_TRANSLATION) {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }

      if (csr_value.MODE == $bits(HgatpMode::Sv57x4)) {
        if (!SV57X4_TRANSLATION) {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }

      # mode is supported
      return csr_value.MODE;
  VMID:
    location_rv32: 28-22
    location_rv64: 57-44
    description: |
      Virtual machine ID.

      The number of VMID bits is determined by parameter VMID_WIDTH.
      VMID_WIDTH may be determined by software by writing one to every bit position in
      the VMID field, then reading back the value in hgatp to see which bit
      positions in the VMID field hold a one.
      The least-significant bits of VMID are implemented first:
      that is, if VMID_WIDTH > 0, VMID[VMID_WIDTH-1:0] is writable.
      The maximal value of VMID_WIDTH, termed VMIDMAX, is 7 for Sv32x4 or
      14 for Sv39x4, Sv48x4, and Sv57x4.
    type(): |
      if (VMID_WIDTH == 0) {
        return CsrFieldType::RO;
      } else {
        return CsrFieldType::RW;
      }
    reset_value(): |
      if (VMID_WIDTH == 0) {
        return 0;
      } else {
        return UNDEFINED_LEGAL;
      }
    sw_write(csr_value): |
      if (csr_value.MODE == $bits(HgatpMode::Bare)) {
        # when MODE == Bare, PPN and VMID must be 0
        if (csr_value.VMID == 0) {
          return 0;
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }
      return csr_value.VMID[VMID_WIDTH-1:0];
  PPN:
    location_rv32: 21-0
    location_rv64: 43-0
    description: |
      The physical page number (PPN) of the guest-physical root page table.
    type(): |
      if (!SV32X4_TRANSLATION && !SV39X4_TRANSLATION && !SV48X4_TRANSLATION && !SV57X4_TRANSLATION) {
        # Bare is the only supported mode, PPN is always 0
        return CsrFieldType::RO;
      } else {
        return CsrFieldType::RW;
      }
    reset_value(): |
      if (!SV32X4_TRANSLATION && !SV39X4_TRANSLATION && !SV48X4_TRANSLATION && !SV57X4_TRANSLATION) {
        # Bare is the only supported mode, PPN is always 0
        return 0;
      } else {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      }
    sw_write(csr_value): |
      if (csr_value.MODE == $bits(HgatpMode::Bare)) {
        # when MODE == Bare, PPN and VMID must be 0
        if (csr_value.PPN == 0) {
          return 0;
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }
      return csr_value.PPN;

sw_read(): |
  if ((CSR[hgatp].MODE == $bits(HgatpMode::Sv32x4))
      || (CSR[hgatp].MODE == $bits(HgatpMode::Sv39x4))
      || (CSR[hgatp].MODE == $bits(HgatpMode::Sv48x4))
      || (CSR[hgatp].MODE == $bits(HgatpMode::Sv57x4))) {
    # bits 1:0 of PPN read as zero
    return $bits(CSR[hgatp]) & ~64'h3;
  } else {
    return $bits(CSR[hgatp]);
  }
