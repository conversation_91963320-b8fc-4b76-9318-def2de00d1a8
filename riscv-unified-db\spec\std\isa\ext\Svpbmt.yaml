# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Svpbmt
long_name: Page-based memory types
description: |
  This extension mandates that the `satp` mode Bare must
  be supported.

  [NOTE]
  This extension was ratified as part of the RVA22 profile.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires:
      name: Sv39
    param_constraints:
      SATP_MODE_BARE:
        schema:
          const: true
