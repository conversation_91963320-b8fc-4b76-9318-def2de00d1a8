# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.swsp
long_name: Store word to stack
description: |
  Stores a 32-bit value in register xs2 to memory.
  It computes an effective address by adding the zero-extended offset, scaled by 4,
  to the stack pointer, x2.
  It expands to `sw` `xs2, offset(x2)`.
definedBy:
  anyOf:
    - C
    - Zca
assembly: xs2, imm(sp)
encoding:
  match: 110-----------10
  variables:
    - name: imm
      location: 8-7|12-9
      left_shift: 2
    - name: xs2
      location: 6-2
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  XReg virtual_address = X[2] + imm;

  write_memory<32>(virtual_address, X[xs2][31:0], $encoding);
