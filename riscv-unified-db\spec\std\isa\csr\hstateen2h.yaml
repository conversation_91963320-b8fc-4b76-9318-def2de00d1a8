# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hstateen2h
long_name: Upper 32 bits of Hypervisor State Enable 2 Register
address: 0x61E
priv_mode: S
length: 32
base: 32
description:
  - id: csr-hstateen0h-purpose
    normative: true
    text: |
      For RV64 harts, the Smstateen/Ssstateen extension adds four new 64-bit CSRs at machine level: `mstateen0` (Machine State Enable 0),
      `mstateen1`, `mstateen2`, and `mstateen3`. If supervisor mode is implemented, another four CSRs are defined at
      supervisor level: `sstateen0`, `sstateen1`, `sstateen2`, and `sstateen3`. And if the hypervisor extension is implemented,
      another set of CSRs is added: `hstateen0`, `hstateen1`, `hstateen2`, and `hstateen3`.

      For RV32, the registers listed above are 32-bit, and for the machine-level and hypervisor CSRs there is a corresponding
      set of high-half CSRs for the upper 32 bits of each register: `mstateen0h`, `mstateen1h`, `mstateen2h`, `mstateen3h`, `hstateen0h`,
      `hstateen1h`, `hstateen2h`, and `hstateen3h`.

definedBy:
  allOf:
    - H
    - Smstateen
    - Ssstateen
fields:
  SE0:
    long_name: sstateen2 access control
    location: 31
    alias: hstateen2.SE0
    sw_write(csr_value): |
      if (CSR[mstateen2].SE0 == 1'b0){
        return 0;
      }
      CSR[hstateen2].SE0 = csr_value.SE0;
      return csr_value.SE0;
    description: |
      The SE0 bit in `hstateen2h` controls access to the `sstateen2` CSR.
    type: RW
    reset_value: UNDEFINED_LEGAL
sw_read(): return $bits(CSR[hstateen2])[63:32];
