# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.outw
long_name: Output word to non-memory-mapped device
description: |
  Output 32 bits of data from register `rs2` to a non-memory-mapped device.
  Such devices have own address space, unrelated to memory map.
  Device space address formed by adding `rs1` to  to a unsigned offset `imm`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqciio
assembly: xs2, imm(xs1)
base: 32
encoding:
  match: -----------------100-----0001011
  variables:
    - name: imm
      location: 31-20
      left_shift: 2
    - name: rs1
      location: 19-15
    - name: rs2
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg device_address = X[rs1] + imm;
  write_device_32(device_address, X[rs2]);
