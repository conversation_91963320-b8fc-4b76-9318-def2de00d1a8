# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvbb
long_name: Vector Basic Bit-manipulation
description: |
  Vector basic bit-manipulation instructions.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    implies:
      name: Zvkb
      version: "1.0.0"
