{
  "name": "RISC-V UnifiedDB Dev",
  "build": { "dockerfile": "Dockerfile" },
  "updateContentCommand": "${containerWorkspaceFolder}/.devcontainer/updateContentCommand.sh",
  "onCreateCommand": "${containerWorkspaceFolder}/.devcontainer/onCreateCommand.sh",
  "remoteEnv": {
    "DISPLAY": ":0"
  },
  "containerEnv": {
    "DEVCONTAINER_ENV": "true"
  },
  // vscode extensions
  "customizations": {
    "vscode": {
      "settings": {
        "solargraph.bundlerPath": "bundle",
        "solargraph.useBundler": true,
        "files.exclude": {
          "**/.home": true,
          "**/.yardoc": true
        },
        "files.watcherExclude": {
          "**/.home": true
        },
        "asciidoc.antora.showEnableAntoraPrompt": true
      },
      "extensions": [
        "asciidoctor.asciidoctor-vscode",
        "castwide.solargraph",
        "CraigMaslowski.erb",
        "HowerLimited.udb-extension-pack-vscode",
        "mathematic.vscode-pdf",
        "redhat.vscode-yaml",
        "zhwu95.riscv"
      ]
    }
  },
  "forwardPorts": [8000, 8080]
}
