# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: rori
long_name: Rotate right (Immediate)
description: |
  Performs a rotate right of xs1 by the amount in the least-significant log2(XLEN) bits of shamt.
  For RV32, the encodings corresponding to shamt[5]=1 are reserved.
definedBy:
  anyOf: [Zbb, Zbkb]
assembly: xd, xs1, shamt
encoding:
  RV32:
    match: 0110000----------101-----0010011
    variables:
      - name: shamt
        location: 24-20
      - name: xs1
        location: 19-15
      - name: xd
        location: 11-7
  RV64:
    match: 011000-----------101-----0010011
    variables:
      - name: shamt
        location: 25-20
      - name: xs1
        location: 19-15
      - name: xd
        location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |
  if (implemented?(ExtensionName::B) && (CSR[misa].B == 1'b0)) {
    raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  XReg shamt = (xlen() == 32) ? shamt[4:0] : shamt[5:0];

  X[xd] = (X[xs1] >> shamt) | (X[xs1] << (xlen() - shamt));

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let rs1_val = X(rs1);
    let result : xlenbits = if sizeof(xlen) == 32
                            then rs1_val >>> shamt[4..0]
                            else rs1_val >>> shamt;
    X(rd) = result;
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
