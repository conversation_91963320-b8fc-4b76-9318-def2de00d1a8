# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl10
long_name: IRQ Level 10
address: 0xbca
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 80-87
fields:
  IRQ80:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ80 level
  IRQ81:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ81 level
  IRQ82:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ82 level
  IRQ83:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ83 level
  IRQ84:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ84 level
  IRQ85:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ85 level
  IRQ86:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ86 level
  IRQ87:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ87 level
