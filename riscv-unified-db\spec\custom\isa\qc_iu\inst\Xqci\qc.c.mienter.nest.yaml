# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

$schema: inst_schema.json#
kind: instruction
name: qc.c.mienter.nest
long_name: Machine mode interrupt enter
description: |
  Machine mode interrupt enter, interrupt nesting is enabled.
  Interrupt frame is saved in the stack.
  Interrupts are enabled.
  Instruction encoded in CI instruction format.
assembly: ""
definedBy:
  anyOf:
    - Xqci
    - Xqciint
access:
  s: never
  u: never
  vs: never
  vu: never
base: 32
encoding:
  match: "0001100010010010"
operation(): |
  XReg virtual_address = get_and_validate_stack_pointer(X[2], $encoding);
  XReg mepc_val = CSR[mepc].sw_read();
  XReg mnepc_val = CSR[mnepc].sw_read();
  XReg qc_mcause_val = CSR[qc.mcause].sw_read();
  XReg reserved_val = 0;
  if (CSR[mnstatus].NMIE == 1'b1) {
    write_memory<32>(virtual_address -  4, mepc_val, $encoding);
  } else {
    write_memory<32>(virtual_address -  4, mnepc_val, $encoding);
  }
  write_memory<32>(virtual_address -  8, X[ 8][31:0], $encoding);
  write_memory<32>(virtual_address - 12, qc_mcause_val, $encoding);
  write_memory<32>(virtual_address - 16, X[ 1][31:0], $encoding);
  write_memory<32>(virtual_address - 20, reserved_val, $encoding);
  write_memory<32>(virtual_address - 24, X[ 5][31:0], $encoding);
  write_memory<32>(virtual_address - 28, X[ 6][31:0], $encoding);
  write_memory<32>(virtual_address - 32, X[ 7][31:0], $encoding);
  write_memory<32>(virtual_address - 36, X[10][31:0], $encoding);
  write_memory<32>(virtual_address - 40, X[11][31:0], $encoding);
  write_memory<32>(virtual_address - 44, X[12][31:0], $encoding);
  write_memory<32>(virtual_address - 48, X[13][31:0], $encoding);
  write_memory<32>(virtual_address - 52, X[14][31:0], $encoding);
  write_memory<32>(virtual_address - 56, X[15][31:0], $encoding);
  write_memory<32>(virtual_address - 60, X[16][31:0], $encoding);
  write_memory<32>(virtual_address - 64, X[17][31:0], $encoding);
  write_memory<32>(virtual_address - 68, X[28][31:0], $encoding);
  write_memory<32>(virtual_address - 72, X[29][31:0], $encoding);
  write_memory<32>(virtual_address - 76, X[30][31:0], $encoding);
  write_memory<32>(virtual_address - 80, X[31][31:0], $encoding);
  X[8] = X[2];
  X[2] = X[2] - 96;
  CSR[mstatus].MIE = 1'b1;
  CSR[qc.mcause].sw_write(qc_mcause_val | (32'b1 << 26));
