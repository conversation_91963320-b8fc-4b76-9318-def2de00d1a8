# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: flw
long_name: Single-precision floating-point load
description: |
  The `flw` instruction loads a single-precision floating-point value from memory at address _xs1_ + _imm_ into floating-point register _fd_.

  `flw` does not modify the bits being transferred; in particular, the payloads of non-canonical NaNs are preserved.

definedBy: F
assembly: fd, imm(xs1)
encoding:
  match: -----------------010-----0000111
  variables:
    - name: imm
      location: 31-20
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |
  check_f_ok($encoding);

  XReg virtual_address = X[xs1] + $signed(imm);

  Bits<32> sp_value = read_memory<32>(virtual_address, $encoding);

  if (implemented?(ExtensionName::D)) {
    f[fd] = nan_box<32, 64>(sp_value);
  } else {
    f[fd] = sp_value;
  }

  mark_f_state_dirty();

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let offset : xlenbits = sign_extend(imm);
    /* Get the address, X(rs1) + offset.
       Some extensions perform additional checks on address validity. */
    match ext_data_get_addr(rs1, offset, Read(Data), width) {
      Ext_DataAddr_Error(e)  => { ext_handle_data_check_error(e); RETIRE_FAIL },
      Ext_DataAddr_OK(vaddr) =>
        if   check_misaligned(vaddr, width)
        then { handle_mem_exception(vaddr, E_Load_Addr_Align()); RETIRE_FAIL }
        else match translateAddr(vaddr, Read(Data)) {
          TR_Failure(e, _) => { handle_mem_exception(vaddr, e); RETIRE_FAIL },
          TR_Address(addr, _) => {
            let (aq, rl, res) = (false, false, false);
            match (width) {
              BYTE => { handle_illegal(); RETIRE_FAIL },
              HALF =>
                 process_fload16(rd, vaddr, mem_read(Read(Data), addr, 2, aq, rl, res)),
              WORD =>
                 process_fload32(rd, vaddr, mem_read(Read(Data), addr, 4, aq, rl, res)),
              DOUBLE if sizeof(flen) >= 64 =>
                 process_fload64(rd, vaddr, mem_read(Read(Data), addr, 8, aq, rl, res)),
              _ => report_invalid_width(__FILE__, __LINE__, width, "floating point load"),
            }
          }
        }
    }
  }

# SPDX-SnippetEnd
