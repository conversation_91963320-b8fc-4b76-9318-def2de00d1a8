# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/I/mcounteren.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mcounteren
long_name: Machine Counter Enable
address: 0x306
priv_mode: M
length: 32
description: |
  The counter-enable `mcounteren` register is a 32-bit register that controls the availability
  of the hardware performance-monitoring counters to
  <%- if ext?(:S) -%>
  S-mode
  <%- elsif ext?(:U) -%>
  U-mode
  <%- else -%>
  the next-lower privileged mode
  <%- end -%>
  .

  The settings in this register only control accessibility. The act of reading or writing this
  register does not affect the underlying counters, which continue to increment even when not
  accessible.

  When the CY, TM, IR, or HPMn bit in the mcounteren register is clear, attempts to read the
  `cycle`, `time`, `instret`, or `hpmcountern` register while executing in
  <%- if ext?(:S) -%>
  S-mode
  <%- elsif ext?(:U) -%>
  U-mode
  <%- else -%>
  S-mode or U-mode
  <%- end -%>
  will cause an `IllegalInstruction` exception. When one of these bits is set, access to the
  corresponding register is permitted in
  <%- if ext?(:S) -%>
  S-mode
  <%- elsif ext?(:U) -%>
  U-mode
  <%- else -%>
  the next implemented privilege mode (S-mode if implemented, otherwise U-mode).
  <%- end -%>

  [NOTE]
  The counter-enable bits support two common use cases with minimal hardware.
  For harts that do not need high-performance timers and counters, machine-mode software can
  trap accesses and implement all features in software. For harts that need high-performance
  timers and counters but are not concerned with obfuscating the underlying hardware counters,
  the counters can be directly exposed to lower privilege modes.

  The `cycle`, `instret`, and `hpmcountern` CSRs are read-only shadows of `mcycle`, `minstret`,
  and `mhpmcounter n`, respectively. The `time` CSR is a read-only shadow of the memory-mapped
  `mtime` register.
  <%- if possible_xlens.include?(32) -%>
  Analogously, on RV32I the `cycleh`, `instreth` and `hpmcounternh` CSRs are
  read-only shadows of `mcycleh`, `minstreth` and `mhpmcounternh`, respectively.
  On RV32I the `timeh` CSR is a read-only shadow of the upper 32 bits of the memory-mapped `mtime`
  register, while time shadows only the lower 32 bits of `mtime`.
  <%- end -%>

  [NOTE]
  Implementations can convert reads of the `time` and `timeh` CSRs into loads to the
  memory-mapped `mtime` register, or emulate this functionality on behalf of less-privileged
  modes in M-mode software.

  <%- if !ext?(:U) -%>
  In harts with U-mode, the `mcounteren` CSR must be implemented, but all fields are WARL and may
  be read-only zero, indicating reads to the corresponding counter will cause an
  `IllegalInstruction` exception when executing in a less-privileged mode.
  In harts without U-mode, the `mcounteren` register should not exist.
  <%- end -%>

  <%- if ext?(:S) -%>
  [INFO]
  The `cycle`, `instret`, and `hpmcountern` CSRs can also be made available to U-mode
  through the `scounteren` CSR
  <%- if ext?(:H) -%>
  and to VS-mode and/or VU-mode through `hcounteren`
  <%- end -%>
  .
  <%- end -%>
definedBy: U # actually, defined by RV64, but must implement U-mode for this CSR to exist
fields:
  CY:
    location: 0
    description: |
      When set, the `cycle` CSR (an alias of `mcycle`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.CY` is also set, `cycle` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.CY` is also set, `cycle` is further accessible to VS-mode.

      When `hcounteren.CY` && `scounteren.CY` are both set, `cycle` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[0]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[0]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  TM:
    location: 1
    description: |
      Placeholder for delegating `time` to less-privileged modes; however, since `time`
      is memory-mapped rather than a CSR, this field is always read-only zero.
    type: RO
    reset_value: 0
  IR:
    location: 2
    description: |
      When set, the `instret` CSR (an alias of `minstret`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.IR` is also set, `instret` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.IR` is also set, `instret` is further accessible to VS-mode.

      When `hcounteren.IR` && `scounteren.IR` are both set, `instret` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[2]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[2]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM3:
    location: 3
    description: |
      When set, the `hpmcounter3` CSR (an alias of `mhpmcounter3`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM3` is also set, `hpmcounter3` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM3` is also set, `hpmcounter3` is further accessible to VS-mode.

      When `hcounteren.HPM3` && `scounteren.HPM3` are both set, `hpmcounter3` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[3]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[3]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM4:
    location: 4
    description: |
      When set, the `hpmcounter4` CSR (an alias of `mhpmcounter4`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM4` is also set, `hpmcounter4` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM4` is also set, `hpmcounter4` is further accessible to VS-mode.

      When `hcounteren.HPM4` && `scounteren.HPM4` are both set, `hpmcounter4` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[4]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[4]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM5:
    location: 5
    description: |
      When set, the `hpmcounter5` CSR (an alias of `mhpmcounter5`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM5` is also set, `hpmcounter5` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM5` is also set, `hpmcounter5` is further accessible to VS-mode.

      When `hcounteren.HPM5` && `scounteren.HPM5` are both set, `hpmcounter5` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[5]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[5]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM6:
    location: 6
    description: |
      When set, the `hpmcounter6` CSR (an alias of `mhpmcounter6`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM6` is also set, `hpmcounter6` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM6` is also set, `hpmcounter6` is further accessible to VS-mode.

      When `hcounteren.HPM6` && `scounteren.HPM6` are both set, `hpmcounter6` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[6]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[6]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM7:
    location: 7
    description: |
      When set, the `hpmcounter7` CSR (an alias of `mhpmcounter7`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM7` is also set, `hpmcounter7` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM7` is also set, `hpmcounter7` is further accessible to VS-mode.

      When `hcounteren.HPM7` && `scounteren.HPM7` are both set, `hpmcounter7` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[7]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[7]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM8:
    location: 8
    description: |
      When set, the `hpmcounter8` CSR (an alias of `mhpmcounter8`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM8` is also set, `hpmcounter8` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM8` is also set, `hpmcounter8` is further accessible to VS-mode.

      When `hcounteren.HPM8` && `scounteren.HPM8` are both set, `hpmcounter8` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[8]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[8]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM9:
    location: 9
    description: |
      When set, the `hpmcounter9` CSR (an alias of `mhpmcounter9`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM9` is also set, `hpmcounter9` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM9` is also set, `hpmcounter9` is further accessible to VS-mode.

      When `hcounteren.HPM9` && `scounteren.HPM9` are both set, `hpmcounter9` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[9]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[9]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM10:
    location: 10
    description: |
      When set, the `hpmcounter10` CSR (an alias of `mhpmcounter10`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM10` is also set, `hpmcounter10` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM10` is also set, `hpmcounter10` is further accessible to VS-mode.

      When `hcounteren.HPM10` && `scounteren.HPM10` are both set, `hpmcounter10` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[10]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[10]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM11:
    location: 11
    description: |
      When set, the `hpmcounter11` CSR (an alias of `mhpmcounter11`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM11` is also set, `hpmcounter11` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM11` is also set, `hpmcounter11` is further accessible to VS-mode.

      When `hcounteren.HPM11` && `scounteren.HPM11` are both set, `hpmcounter11` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[11]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[11]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM12:
    location: 12
    description: |
      When set, the `hpmcounter12` CSR (an alias of `mhpmcounter12`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM12` is also set, `hpmcounter12` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM12` is also set, `hpmcounter12` is further accessible to VS-mode.

      When `hcounteren.HPM12` && `scounteren.HPM12` are both set, `hpmcounter12` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[12]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[12]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM13:
    location: 13
    description: |
      When set, the `hpmcounter13` CSR (an alias of `mhpmcounter13`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM13` is also set, `hpmcounter13` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM13` is also set, `hpmcounter13` is further accessible to VS-mode.

      When `hcounteren.HPM13` && `scounteren.HPM13` are both set, `hpmcounter13` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[13]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[13]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM14:
    location: 14
    description: |
      When set, the `hpmcounter14` CSR (an alias of `mhpmcounter14`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM14` is also set, `hpmcounter14` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM14` is also set, `hpmcounter14` is further accessible to VS-mode.

      When `hcounteren.HPM14` && `scounteren.HPM14` are both set, `hpmcounter14` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[14]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[14]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM15:
    location: 15
    description: |
      When set, the `hpmcounter15` CSR (an alias of `mhpmcounter15`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM15` is also set, `hpmcounter15` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM15` is also set, `hpmcounter15` is further accessible to VS-mode.

      When `hcounteren.HPM15` && `scounteren.HPM15` are both set, `hpmcounter15` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[15]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[15]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM16:
    location: 16
    description: |
      When set, the `hpmcounter16` CSR (an alias of `mhpmcounter16`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM16` is also set, `hpmcounter16` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM16` is also set, `hpmcounter16` is further accessible to VS-mode.

      When `hcounteren.HPM16` && `scounteren.HPM16` are both set, `hpmcounter16` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[16]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[16]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM17:
    location: 17
    description: |
      When set, the `hpmcounter17` CSR (an alias of `mhpmcounter17`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM17` is also set, `hpmcounter17` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM17` is also set, `hpmcounter17` is further accessible to VS-mode.

      When `hcounteren.HPM17` && `scounteren.HPM17` are both set, `hpmcounter17` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[17]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[17]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM18:
    location: 18
    description: |
      When set, the `hpmcounter18` CSR (an alias of `mhpmcounter18`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM18` is also set, `hpmcounter18` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM18` is also set, `hpmcounter18` is further accessible to VS-mode.

      When `hcounteren.HPM18` && `scounteren.HPM18` are both set, `hpmcounter18` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[18]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[18]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM19:
    location: 19
    description: |
      When set, the `hpmcounter19` CSR (an alias of `mhpmcounter19`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM19` is also set, `hpmcounter19` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM19` is also set, `hpmcounter19` is further accessible to VS-mode.

      When `hcounteren.HPM19` && `scounteren.HPM19` are both set, `hpmcounter19` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[19]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[19]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM20:
    location: 20
    description: |
      When set, the `hpmcounter20` CSR (an alias of `mhpmcounter20`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM20` is also set, `hpmcounter20` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM20` is also set, `hpmcounter20` is further accessible to VS-mode.

      When `hcounteren.HPM20` && `scounteren.HPM20` are both set, `hpmcounter20` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[20]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[20]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM21:
    location: 21
    description: |
      When set, the `hpmcounter21` CSR (an alias of `mhpmcounter21`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM21` is also set, `hpmcounter21` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM21` is also set, `hpmcounter21` is further accessible to VS-mode.

      When `hcounteren.HPM21` && `scounteren.HPM21` are both set, `hpmcounter21` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[21]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[21]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM22:
    location: 22
    description: |
      When set, the `hpmcounter22` CSR (an alias of `mhpmcounter22`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM22` is also set, `hpmcounter22` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM22` is also set, `hpmcounter22` is further accessible to VS-mode.

      When `hcounteren.HPM22` && `scounteren.HPM22` are both set, `hpmcounter22` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[22]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[22]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM23:
    location: 23
    description: |
      When set, the `hpmcounter23` CSR (an alias of `mhpmcounter23`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM23` is also set, `hpmcounter23` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM23` is also set, `hpmcounter23` is further accessible to VS-mode.

      When `hcounteren.HPM23` && `scounteren.HPM23` are both set, `hpmcounter23` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[23]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[23]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM24:
    location: 24
    description: |
      When set, the `hpmcounter24` CSR (an alias of `mhpmcounter24`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM24` is also set, `hpmcounter24` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM24` is also set, `hpmcounter24` is further accessible to VS-mode.

      When `hcounteren.HPM24` && `scounteren.HPM24` are both set, `hpmcounter24` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[24]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[24]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM25:
    location: 25
    description: |
      When set, the `hpmcounter25` CSR (an alias of `mhpmcounter25`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM25` is also set, `hpmcounter25` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM25` is also set, `hpmcounter25` is further accessible to VS-mode.

      When `hcounteren.HPM25` && `scounteren.HPM25` are both set, `hpmcounter25` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[25]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[25]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM26:
    location: 26
    description: |
      When set, the `hpmcounter26` CSR (an alias of `mhpmcounter26`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM26` is also set, `hpmcounter26` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM26` is also set, `hpmcounter26` is further accessible to VS-mode.

      When `hcounteren.HPM26` && `scounteren.HPM26` are both set, `hpmcounter26` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[26]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[26]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM27:
    location: 27
    description: |
      When set, the `hpmcounter27` CSR (an alias of `mhpmcounter27`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM27` is also set, `hpmcounter27` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM27` is also set, `hpmcounter27` is further accessible to VS-mode.

      When `hcounteren.HPM27` && `scounteren.HPM27` are both set, `hpmcounter27` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[27]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[27]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM28:
    location: 28
    description: |
      When set, the `hpmcounter28` CSR (an alias of `mhpmcounter28`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM28` is also set, `hpmcounter28` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM28` is also set, `hpmcounter28` is further accessible to VS-mode.

      When `hcounteren.HPM28` && `scounteren.HPM28` are both set, `hpmcounter28` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[28]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[28]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM29:
    location: 29
    description: |
      When set, the `hpmcounter29` CSR (an alias of `mhpmcounter29`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM29` is also set, `hpmcounter29` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM29` is also set, `hpmcounter29` is further accessible to VS-mode.

      When `hcounteren.HPM29` && `scounteren.HPM29` are both set, `hpmcounter29` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[29]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[29]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM30:
    location: 30
    description: |
      When set, the `hpmcounter30` CSR (an alias of `mhpmcounter30`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM30` is also set, `hpmcounter30` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM30` is also set, `hpmcounter30` is further accessible to VS-mode.

      When `hcounteren.HPM30` && `scounteren.HPM30` are both set, `hpmcounter30` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[30]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[30]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM31:
    location: 31
    description: |
      When set, the `hpmcounter31` CSR (an alias of `mhpmcounter31`) is accessible to
      <%- if ext?(:S) -%>
      S-mode.
      <%- else -%>
      U-mode.
      <%- end -%>

      <%- if ext?(:S) -%>
      When `scounteren.HPM31` is also set, `hpmcounter31` is further accessible to U-mode.
      <%- end -%>

      <%- if ext?(:H) -%>
      When `hcounteren.HPM31` is also set, `hpmcounter31` is further accessible to VS-mode.

      When `hcounteren.HPM31` && `scounteren.HPM31` are both set, `hpmcounter31` is further accessible to VU-mode.
      <%- end -%>
    type(): |
      if (MCOUNTENABLE_EN[31]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (MCOUNTENABLE_EN[31]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
