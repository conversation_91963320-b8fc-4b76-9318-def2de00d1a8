# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: fld
long_name: Load Double-precision Floating-Point
description: |
  No description available.
definedBy: D
assembly: fd, imm(xs1)
encoding:
  match: -----------------011-----0000111
  variables:
    - name: imm
      location: 31-20
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
