<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::Var
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::Var";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (V)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">Var</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::Var
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Idl::Var</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/symbol_table.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Objects to represent variables in the ISA def</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute name.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#type-instance_method" title="#type (instance method)">#<strong>type</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute type.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#value-instance_method" title="#value (instance method)">#<strong>value</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute value.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#clone-instance_method" title="#clone (instance method)">#<strong>clone</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#const%3F-instance_method" title="#const? (instance method)">#<strong>const?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#decode_var%3F-instance_method" title="#decode_var? (instance method)">#<strong>decode_var?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(name, type, value = nil, decode_var: false, template_index: nil, function_name: nil)  &#x21d2; Var </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Var.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#template_index-instance_method" title="#template_index (instance method)">#<strong>template_index</strong>  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The template value position.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#template_val%3F-instance_method" title="#template_val? (instance method)">#<strong>template_val?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#template_value_for%3F-instance_method" title="#template_value_for? (instance method)">#<strong>template_value_for?</strong>(function_name)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this variable is a function template argument from a call site for the function ‘function_name’.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_cxx-instance_method" title="#to_cxx (instance method)">#<strong>to_cxx</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(name, type, value = nil, decode_var: false, template_index: nil, function_name: nil)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::Var (class)">Var</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Var.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt>ArgumentError</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


10
11
12
13
14
15
16
17
18
19
20
21</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 10</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='id identifier rubyid_type'>type</span><span class='comma'>,</span> <span class='id identifier rubyid_value'>value</span> <span class='op'>=</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>decode_var:</span> <span class='kw'>false</span><span class='comma'>,</span> <span class='label'>template_index:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>function_name:</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_name'>name</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Expecting a Type, got </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='rparen'>)</span>

  <span class='ivar'>@type</span> <span class='op'>=</span> <span class='id identifier rubyid_type'>type</span>
  <span class='ivar'>@value</span> <span class='op'>=</span> <span class='id identifier rubyid_value'>value</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>unexpected</span><span class='tstring_end'>&#39;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_decode_var'>decode_var</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>TrueClass</span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_decode_var'>decode_var</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>FalseClass</span><span class='rparen'>)</span>

  <span class='ivar'>@decode_var</span> <span class='op'>=</span> <span class='id identifier rubyid_decode_var'>decode_var</span>
  <span class='ivar'>@template_index</span> <span class='op'>=</span> <span class='id identifier rubyid_template_index'>template_index</span>
  <span class='ivar'>@function_name</span> <span class='op'>=</span> <span class='id identifier rubyid_function_name'>function_name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute name.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


8
9
10</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 8</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="type-instance_method">
  
    #<strong>type</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute type.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


8
9
10</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 8</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type'>type</span>
  <span class='ivar'>@type</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id="value=-instance_method"></span>
      <div class="method_details ">
  <h3 class="signature " id="value-instance_method">
  
    #<strong>value</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute value.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


8
9
10</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 8</span>

<span class='kw'>def</span> <span class='id identifier rubyid_value'>value</span>
  <span class='ivar'>@value</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="clone-instance_method">
  
    #<strong>clone</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


23
24
25
26
27
28
29
30</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 23</span>

<span class='kw'>def</span> <span class='id identifier rubyid_clone'>clone</span>
  <span class='const'><span class='object_link'><a href="" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span>
    <span class='id identifier rubyid_name'>name</span><span class='comma'>,</span>
    <span class='id identifier rubyid_type'>type</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span>
    <span class='id identifier rubyid_value'>value</span><span class='op'>&amp;.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span>
    <span class='label'>decode_var:</span> <span class='ivar'>@decode_var</span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="const?-instance_method">
  
    #<strong>const?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


32
33
34</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 32</span>

<span class='kw'>def</span> <span class='id identifier rubyid_const?'>const?</span>
  <span class='ivar'>@type</span><span class='period'>.</span><span class='id identifier rubyid_const?'>const?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="decode_var?-instance_method">
  
    #<strong>decode_var?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


36
37
38</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 36</span>

<span class='kw'>def</span> <span class='id identifier rubyid_decode_var?'>decode_var?</span>
  <span class='ivar'>@decode_var</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="template_index-instance_method">
  
    #<strong>template_index</strong>  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the template value position.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the template value position</p>
</div>
      
    </li>
  
</ul>
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>if Var is not a template value</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


48
49
50
51
52</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 48</span>

<span class='kw'>def</span> <span class='id identifier rubyid_template_index'>template_index</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Not a template value</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='ivar'>@template_index</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@template_index</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="template_val?-instance_method">
  
    #<strong>template_val?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


54
55
56</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 54</span>

<span class='kw'>def</span> <span class='id identifier rubyid_template_val?'>template_val?</span>
  <span class='op'>!</span><span class='ivar'>@template_index</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="template_value_for?-instance_method">
  
    #<strong>template_value_for?</strong>(function_name)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this variable is a function template argument from a call site for the function ‘function_name’.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>function_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A function name</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this variable is a function template argument from a call site for the function ‘function_name’</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


42
43
44</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 42</span>

<span class='kw'>def</span> <span class='id identifier rubyid_template_value_for?'>template_value_for?</span><span class='lparen'>(</span><span class='id identifier rubyid_function_name'>function_name</span><span class='rparen'>)</span>
  <span class='op'>!</span><span class='ivar'>@template_index</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>&amp;&amp;</span> <span class='lparen'>(</span><span class='id identifier rubyid_function_name'>function_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span> <span class='op'>==</span> <span class='ivar'>@function_name</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_cxx-instance_method">
  
    #<strong>to_cxx</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


58
59
60</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/symbol_table.rb', line 58</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_cxx'>to_cxx</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>