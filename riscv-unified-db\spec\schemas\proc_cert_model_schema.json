{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "required": ["$schema", "kind", "name", "long_name", "base"], "additionalProperties": false, "properties": {"$inherits": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "$child_of": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "$parent_of": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "$schema": {"type": "string", "const": "proc_cert_model_schema.json#"}, "kind": {"type": "string", "const": "processor certificate model"}, "name": {"type": "string", "pattern": "^[A-Z][a-zA-Z0-9_-]*$", "description": "The short name of the model, used as a database key"}, "long_name": {"type": "string", "description": "One line description of certificate model"}, "class": {"type": "object", "properties": {"$ref": {"type": "string", "pattern": "^proc_cert_class/[A-Z][a-zA-Z0-9_]*\\.yaml#"}}, "description": "Reference to the class this model belongs to"}, "versions": {"type": "array", "items": {"type": "object", "required": ["version"], "properties": {"version": {"$ref": "schema_defs.json#/$defs/semantic_version"}}, "additionalProperties": false}, "minItems": 1, "description": "List of semantic versions within the model"}, "base": {"type": "integer", "enum": [32, 64], "description": "Base of the model"}, "revision_history": {"type": "array", "items": {"$ref": "schema_defs.json#/$defs/revision_history_entry"}, "minItems": 1, "description": "Revisions of the model document"}, "introduction": {"type": "string", "description": "Asciidoc text containing the introduction prose for the model"}, "tsc_profile_release": {"oneOf": [{"type": "null"}, {"type": "object", "required": ["$ref"], "properties": {"$ref": {"type": "string", "pattern": "^profile_release/[A-Z][a-zA-Z0-9_]*\\.yaml#"}}, "additionalProperties": false}], "description": "Profile release associated with this certificate"}, "unpriv_isa_manual_revision": {"type": "string"}, "priv_isa_manual_revision": {"type": "string"}, "debug_manual_revision": {"type": "string"}, "in_scope_priv_modes": {"type": "array", "items": {"type": "string", "enum": ["M", "S", "U", "HS", "VS", "VU"]}, "uniqueItems": true, "minItems": 1, "description": "List of in-scope privilege modes for the certificate"}, "extensions": {"type": "object", "additionalProperties": false, "patternProperties": {"\\$inherits": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "\\$child_of": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "\\$parent_of": {"oneOf": [{"type": "string", "pattern": "^proc_cert_model/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^proc_cert_model/.*\\.yaml#.*"}, "uniqueItems": true}]}, "^([A-WY])|([SXZ][a-z0-9]+)$": {"type": "object", "properties": {"version": {"$ref": "schema_defs.json#/$defs/requirement_string"}, "presence": {"$ref": "schema_defs.json#/$defs/extension_presence"}, "param_constraints": {"type": "object", "patternProperties": {"^[A-Z][A-Z0-9_]+$": {"$ref": "schema_defs.json#/$defs/parameter_constraint"}}}, "note": {"type": "string"}}, "additionalProperties": false}}}, "requirement_groups": {"type": "object", "additionalProperties": false, "patternProperties": {"\\$inherits": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "\\$child_of": {"oneOf": [{"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^(profile|proc_cert_model)/.*\\.yaml#.*"}, "uniqueItems": true}]}, "\\$parent_of": {"oneOf": [{"type": "string", "pattern": "^proc_cert_model/.*\\.yaml#.*"}, {"type": "array", "items": {"type": "string", "pattern": "^proc_cert_model/.*\\.yaml#.*"}, "uniqueItems": true}]}, "^[A-Za-z0-9_-]+$": {"type": "object", "properties": {"name": {"type": "string"}, "requirements": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "when": {"$ref": "schema_defs.json#/$defs/when_condition"}}, "additionalProperties": false}}}}}}, "extra_notes": {"type": "array", "items": {"type": "object", "properties": {"presence": {"$ref": "schema_defs.json#/$defs/extension_presence"}, "text": {"type": "string"}}, "additionalProperties": false}}, "recommendations": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}}}}, "$source": {"$ref": "schema_defs.json#/$defs/$source"}}}