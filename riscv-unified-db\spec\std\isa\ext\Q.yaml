# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Q
long_name: Quad-Precision Floating-Point
description: |
  128-bit quad-precision binary floating-point instructions compliant with the IEEE 754-2008
  arithmetic standard.
  `Q` depends on the double-precision floating-point extension `D`.
  With `Q`, the floating-point registers are extended to hold either a single, double, or quad-precision
  floating-point value (FLEN=128).
  The NaN-boxing is extended recursively to allow a single-precision value to be NaN-boxed inside
  a double-precision value which is itself NaN-boxed inside a quad-precision value.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: D
params:
  MUTABLE_MISA_Q:
    description: |
      Indicates whether or not the `Q` extension can be disabled with the `misa.Q` bit.
    schema:
      type: boolean
