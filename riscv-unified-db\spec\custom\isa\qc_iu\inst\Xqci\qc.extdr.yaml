# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.extdr
long_name: Extract bits from pair signed (Register)
description: |
  Extract a subset of bits from the register pair [`rs1`, `rs1`+1] into `rd`, and sign extend the result.
  The width of the subset is determined by `rs2` bits [21:16] (0..32),
  and the offset of the subset is determined by `rs2` bits [5:0] (0..63).
  In case when `rs2` bit [21] == 1 width is enforced to 32.
  In case when width == 0, to the destination register written 0.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 0000101----------011-----0001011
  variables:
    - name: rs2
      location: 24-20
      not: 0
    - name: rs1
      location: 19-15
      not: 31
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  Bits<{1'b0, MXLEN}*2> pair = {X[rs1 + 1], X[rs1]};
  XReg width_bits = X[rs2][21:16];
  XReg width = (width_bits > 32) ? 32 : width_bits;
  XReg shamt = X[rs2][5:0];
  if (width > 0) {
    X[rd] = sext((pair >> shamt) & ((32'b1 << width) - 1), width);
  } else {
    X[rd] = 0;
  }
