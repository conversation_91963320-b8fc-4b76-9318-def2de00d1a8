# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.jal
long_name: Jump and link
description: |
  Jump to a PC-relative offset and store the return.
  Instruction encoded in QC.EJ instruction format.
  address in x1.
definedBy:
  anyOf:
    - Xqci
    - Xqcilb
assembly: " imm"
base: 32
encoding:
  match: -----------------------00000---01100-----0011111
  variables:
    - name: imm
      location: 47-32|19-17|31|7|30-25|11-8
      left_shift: 1
      sign_extend: true
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg return_addr = $pc + 6;
  jump_halfword($pc + imm);
  X[1] = return_addr;
