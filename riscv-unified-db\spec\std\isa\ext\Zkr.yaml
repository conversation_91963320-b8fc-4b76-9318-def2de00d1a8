# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zkr
long_name: Entropy Source
description: |
  Defines the `seed` CSR.
  This CSR provides up to 16 physical entropy bits that can be used to seed cryptographic random bit generators.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
