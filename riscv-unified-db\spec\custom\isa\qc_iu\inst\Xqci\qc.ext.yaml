# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.ext
long_name: Extract bits signed
description: |
  Extract a subset of bits from `rs1` into `rd`, and sign-extend the result.
  The width of the subset is determined by (`width_minus1` + 1) (1..32),
  and the offset of the subset is determined by `shamt`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 01---------------010-----0001011
  variables:
    - name: width_minus1
      location: 29-25
    - name: shamt
      location: 24-20
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, width, shamt"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg width = width_minus1 + 1;
  XReg unsigned_extraction = (X[rs1] >> shamt) & ((32'b1 << width) - 1);
  X[rd] = sext(unsigned_extraction, width);
