# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl29
long_name: IRQ Level 29
address: 0xbdd
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 232-239
fields:
  IRQ232:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ232 level
  IRQ233:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ233 level
  IRQ234:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ234 level
  IRQ235:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ235 level
  IRQ236:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ236 level
  IRQ237:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ237 level
  IRQ238:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ238 level
  IRQ239:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ239 level
