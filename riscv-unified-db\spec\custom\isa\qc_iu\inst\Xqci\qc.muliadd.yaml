# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.muliadd
long_name: Multiply and accumulate (Immediate)
description: |
  Increments `rd` by the multiplication of `rs1` and a signed immediate `imm`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqciac
base: 32
encoding:
  match: -----------------110-----0001011
  variables:
    - name: imm
      location: 31-20
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, imm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg orig_val = X[rd];
  X[rd] = orig_val + (X[rs1] * sext(imm,12));
