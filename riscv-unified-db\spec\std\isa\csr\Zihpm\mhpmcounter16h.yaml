# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/mhpmcounterNh.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mhpmcounter16h
long_name: Machine Hardware Performance Counter 16, Upper half
address: 0xB90
priv_mode: M
length: 32
base: 32
description: |
  Upper half of mhpmcounter16.
definedBy: Smhpm
fields:
  COUNT:
    location: 31-0
    alias: mhpmcounter.COUNT16[63:32]
    description: |
      Upper bits of counter.
    type(): "return (HPM_COUNTER_EN[16]) ? CsrFieldType::RWH : CsrFieldType::RO;"
    reset_value(): "return (HPM_COUNTER_EN[16]) ? UNDEFINED_LEGAL : 0;"
sw_read(): |
  if (HPM_COUNTER_EN[16]) {
    return read_hpm_counter(16)[63:32];
  } else {
    return 0;
  }
