[[header]]
:description: <%= ext.long_name %> (<%= ext.name %>)
:revdate: <%= max_version.ratification_date.nil? ? Date.today : max_version.ratification_date %>
:revnumber: <%= max_version.version_spec %>
:revmark: <%=
  case max_version.state
  when "ratified"
    <<~STATE
      This document is in the http://riscv.org/spec-state[Ratified state] + \\
      + \\
      No changes are allowed. + \\
      Any desired or needed changes can be the subject of a follow-on new extension. + \\
      Ratified extensions are never revised. + \\
    STATE
  when "frozen"
    <<~FROZEN_STATE
      This document is in the http://riscv.org/spec-state[Frozen state]. + \\
      + \\
      Change is extremely unlikely. + \\
      A high threshold will be used, and a change will only occur because of some truly + \\
      critical issue being identified during the public review cycle. + \\
      Any other desired or needed changes can be the subject of a follow-on new extension. + \\
    FROZEN_STATE
  when "development"
    <<~DEV_STATE
      This document is in the http://riscv.org/spec-state[Development state]. + \\
      + \\
      Change should be expected + \\
    DEV_STATE
  else
    raise "TODO: #{max_version.state} description"
  end
%>
:company: <%= ext.company.nil? ? "unknown" : ext.company["name"] %>
:url-riscv: https://riscv.org
:doctype: book
:preface-title: Licensing and Acknowledgements
:colophon:
:appendix-caption: Appendix
<%- if !ext.company.nil? && (ext.company["name"] =~ /RISCV/) -%>
:title-logo-image: image:risc-v_logo.png["RISC-V International Logo",pdfwidth=3.25in,align=center]
:back-cover-image: image:riscv-horizontal-color.svg[opacity=25%]
<%- end -%>
<%- if max_version.state == "development" -%>
:page-background-image: image:draft.png[opacity=20%]
<%- end -%>
// Settings
:experimental:
:reproducible:
:wavedrom: <%= $root %>/node_modules/.bin/wavedrom-cli
// needs to be changed
:imagesoutdir: images
:icons: font
:lang: en
:example-caption: Example
:listing-caption: Listing
:table-caption: Table
:figure-caption: Figure
:xrefstyle: short
:chapter-refsig: Chapter
:section-refsig: Section
:appendix-refsig: Appendix
:sectnums:
:toc: left
:toclevels: 5
:source-highlighter: pygments
ifdef::backend-pdf[]
:source-highlighter: rouge
endif::[]
:data-uri:
:hide-uri-scheme:
:stem:
:footnote:
:stem: latexmath
:footnote:
:le: &#8804;
:ge: &#8805;
:ne: &#8800;
:approx: &#8776;
:inf: &#8734;
:csrname: envcfg
:imagesdir: images

= <%= ext.long_name %>

// Preamble
<%=
  case max_version.state
  when "ratified"
    <<~RATIFIED_STATE
      [WARNING]
      .This document is in the link:http://riscv.org/spec-state[Ratified state]
      ====
      No changes are allowed. Any desired or needed changes can be the subject of a
      follow-on new extension. Ratified extensions are never revised
      ====
    RATIFIED_STATE
  when "frozen"
    <<~FROZEN_STATE
      [WARNING]
      This document is in the http://riscv.org/spec-state[Frozen state].
      ====
      Change is extremely unlikely.
      A high threshold will be used, and a change will only occur because of some truly
      critical issue being identified during the public review cycle.
      Any other desired or needed changes can be the subject of a follow-on new extension.
      ====
    FROZEN_STATE
  when "development"
    <<~DEV_STATE
      [WARNING]
      This document is in the http://riscv.org/spec-state[Development state].
      ====
      Change should be expected
      ====
    DEV_STATE
  else
    raise "TODO: #{max_version.state} description"
  end
%>

[preface]
== Copyright and license information
This document is released under the <%= ext.doc_license.nil? ? "unknown" : ext.doc_license["url"] %>[<%= ext.doc_license.nil? ? "unknown" : ext.doc_license["name"] %>].

Copyright <%= max_version.ratification_date.nil? ? Date.today.year : max_version.ratification_date.split("-")[0] %> by <%= ext.company.nil? ? "unknown" : ext.company["name"] %>.

[preface]
== Acknowledgements

<%- versions.each do |version| -%>
Contributors to version <%= version.version_spec %> of the specification (in alphabetical order) include: +

<%- unless version.contributors.empty? -%>
<%- version.contributors.sort { |a, b| a.name.split(" ").last <=> b.name.split(" ").last }.each do |c| -%>
  * <%= c.name %> <<%= c.email %>> (<%= c.company %>)

<%- end -%>
<%- end -%>
<%- end -%>

We express our gratitude to everyone that contributed to, reviewed or
improved this specification through their comments and questions.

[preface]
== Versions

<%- if versions.size > 1 -%>
This specification documents versions <%= versions.map { |v| v.version_spec.to_s }.join(', ') %> of <%= ext.name %>:
<%- else -%>
This specification documents version <%= max_version.version_spec %> of <%= ext.name %>.
<%- end -%>

=== Version History

<%- ext.versions.each do |version| -%>
==== <%= version.version_spec %>

--
State:: <%= version.state %>
<%- unless version.ratification_date.nil? -%>
Ratification Date:: <%= version.ratification_date %>
<%- end -%>
<%- unless version.url.nil? -%>
Design document:: <%= version.url %>
<%- end -%>
<%- unless version.changes.empty? -%>
Changes::

    <% version.changes.each do |c| -%>
    * <%= c %>
    <% end -%>

<%- end -%>
<%- unless version.implications.empty? -%>
Implies::

<%= version.implications.map { |i| "    * #{i.ext_ver.name} (#{i.ext_ver.version_spec}) #{i.cond.empty? ? '' : i.cond.to_asciidoc(join: ', ')}" }.join("\n") %>

<%- unless version.requirement_condition.empty? -%>
Requires::
<%= version.requirement_condition.to_asciidoc %>
<%- end -%>
<%- end -%>
--
<%- end -%>

<<<
== Conventions

Version requirements are specified as conditions using the following operators:

[cols="1,2"]
|===
| Operator | Meaning

| `~> VERSION` | Accepts any version that is _compatible_ with `VERSION`. By default, a version A is compatible with a version B if A's version number is greater than or equal to version B. If that default assumption is ever broken, it will be noted in the list of extension versions.
| `= VERSION` | Accepts only version `VERSION`.
| `>= VERSION` | Accepts any version greater than or equal to `VERSION`.
| `\<= VERSION` | Accepts any version less than or equal to `VERSION`.
| `> VERSION` | Accepts any version greater than `VERSION`.
| `< VERSION` | Accepts any version less than `VERSION`.
|===

<<<
== Extension description

:leveloffset: +2
<%= ext.description %>
:leveloffset: -2

<%- has_implications = versions.any? { |v| !v.implications.empty? } -%>
<%- if has_implications -%>
=== Sub-extensions
<%-   versions.each do |version| -%>
<%-     next if version.implications.empty? -%>
<%-     if version.implications.size > 1 -%>
<%= version.name %> defines the following <%= version.implications.size %> sub-extensions:
<%-     else -%>
<%= version.name %> defines a single sub-extension:
<%-     end -%>

<%-     version.implications.each do |implication_hsh| -%>
<%-       sub_ext = implication_hsh.ext_ver -%>
<%-       cond = implication_hsh.cond -%>
==== <%= sub_ext.name %> (<%= sub_ext.version_spec %>) <%= "if #{cond.to_asciidoc(join: ', ')}" unless cond.empty? %>

<%= cfg_arch.extension(sub_ext.name).description %>

<%-       unless sub_ext.requirement_condition.empty? -%>
<%= sub_ext.name %> requires:

<%= sub_ext.requirement_condition.to_asciidoc %>

<%-       end -%>
<%-     end -%>
<%-   end -%>
<%- end -%>

<%- unless ext.instructions.empty? -%>
<<<
== Instruction summary

The following <%= ext.instructions.size %> instructions are affected by this extension:

[%autowidth]
|===
| RV32 | RV64 | Mnemonic | Instruction <%- if versions.size > 1 -%>| <%= versions.map { |v| "v#{v.version}" }.join(" | ") %><%- end -%>

<%- inst_list = ext.instructions.select { |inst| versions.any? { |ext_ver| inst.defined_by_condition.possibly_satisfied_by?(ext_ver) } } -%>
<%- inst_list.each do |i| -%>
| <%= i.rv32? ? "&#x2713;" : "" %>
| <%= i.rv64? ? "&#x2713;" : "" %>
| `<%= i.name %> <%= i.assembly.gsub("x", "r").strip %>`
| xref:insns-<%= i.name.gsub('.', '_') %>[<%= i.long_name %>]
<%- if versions.size > 1 -%>
| <%= ext.versions.map { |v| i.defined_by_condition.possibly_satisfied_by?(v) ? "&#x2713;" : "" }.join(" | ") %>
<%- end -%>
<%- end -%>
|===

<%- if has_implications -%>
=== Instructions by sub-extension

<%- implications = versions.map { |v| v.implications.to_a }.flatten -%>

[%autowidth]
|===
| Mnemonic | <%= implications.map { |e| "`#{e.ext_ver.name}`" }.join(" | ") %>

<%- inst_list.each do |i| -%>
| `<%= i.name %>`
| <%= implications.map { |e| i.defined_by_condition.possibly_satisfied_by?(e.ext_ver) ? "&#x2713;" : "" }.join(" | ") %>
<%- end -%>
|===

<%- end -%>

<%- end -%>

<%- unless ext.csrs.empty? -%>
<<<
== CSR summary

<%- csr_list = ext.csrs.select { |csr| versions.any? { |ext_ver| csr.affected_by?(ext_ver) } } -%>

The following <%= csr_list.size %> CSRs are affected by this extension.

[%autowidth]
|===
| RV32 | RV64 | CSR | Name <%- if versions.size > 1 -%>| <%= versions.map { |v| "v#{v.version}" }.join(" | ") %><%- end -%>

<%- csr_list.each do |csr| -%>
| <%= csr.defined_in_base32? ? "&#x2713;" : "" %>
| <%= csr.defined_in_base64? ? "&#x2713;" : "" %>
| xref:csrs-<%= csr.name.gsub('.', '_') %>[<%= csr.name %>]
| <%= csr.long_name %>
<%- if versions.size > 1 -%>
| <%= ext.versions.map { |v| csr.affected_by?(v) ? "&#x2713;" : "" }.join(" | ") %>
<%- end -%>
<%- end -%>

|===

<<<
[#csrs,reftext="CSRs (in alphabetical order)"]
== Csrs (in alphabetical order)

<%- ext.csrs.each do |csr| -%>
<<<
:leveloffset: +2
<%= partial "common_templates/adoc/csr.adoc.erb", { csr: csr, cfg_arch: cfg_arch } %>
:leveloffset: -2
<%- end -%>

<%- end # unless csrs.empty? -%>

<%- unless ext.instructions.empty? -%>
<<<
[#insns,reftext="Instructions (in alphabetical order)"]
== Instructions (in alphabetical order)

<%- ext.instructions.each do |i| -%>
:leveloffset: +2
<%= partial "common_templates/adoc/inst.adoc.erb", { inst: i, cfg_arch: cfg_arch } %>
:leveloffset: -2

<<<
<%- end -%>
<%- end -%>

<<<
== IDL Functions

<%- ext.reachable_functions.sort { |a,b| a.name <=> b.name }.each do |f| -%>
[#<%= f.name %>-func-def]
=== <%= f.name %><%- if f.builtin? -%> (builtin)<%- end -%><%- if f.generated? -%> (generated)<%- end -%>

<%= f.description %>

|===
h| Return Type l| <%= f.return_type_list_str.join(', ') %>
h| Arguments   l| <%= f.arguments_list_str.join (', ') %>
|===

<%- unless f.builtin? || f.generated? -%>
<%- body_ast = f.body -%>
[source,idl,subs="specialchars,macros"]
----
<%= body_ast.gen_adoc %>
----
<%- end -%>

<%- end -%>
