<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::EnumerationType
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::EnumerationType";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (E)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">EnumerationType</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::EnumerationType
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></li>
          
            <li class="next">Idl::EnumerationType</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/type.rb</dd>
  </dl>
  
</div>





  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#element_names-instance_method" title="#element_names (instance method)">#<strong>element_names</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute element_names.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#element_values-instance_method" title="#element_values (instance method)">#<strong>element_values</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute element_values.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#width-instance_method" title="#width (instance method)">#<strong>width</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute width.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#clone-instance_method" title="#clone (instance method)">#<strong>clone</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(type_name, element_names, element_values)  &#x21d2; EnumerationType </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of EnumerationType.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#value-instance_method" title="#value (instance method)">#<strong>value</strong>(element_name)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(type_name, element_names, element_values)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::EnumerationType (class)">EnumerationType</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of EnumerationType.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


357
358
359
360
361
362
363
364
365
366
367
368
369
370
371
372</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 357</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_type_name'>type_name</span><span class='comma'>,</span> <span class='id identifier rubyid_element_names'>element_names</span><span class='comma'>,</span> <span class='id identifier rubyid_element_values'>element_values</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='id identifier rubyid_element_values'>element_values</span><span class='period'>.</span><span class='id identifier rubyid_max'>max</span><span class='period'>.</span><span class='id identifier rubyid_bit_length'>bit_length</span>
  <span class='id identifier rubyid_width'>width</span> <span class='op'>=</span> <span class='int'>1</span> <span class='kw'>if</span> <span class='id identifier rubyid_width'>width</span><span class='period'>.</span><span class='id identifier rubyid_zero?'>zero?</span> <span class='comment'># can happen if only enum member has value 0
</span>  <span class='kw'>super</span><span class='lparen'>(</span><span class='symbol'>:enum</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_width'>width</span><span class='rparen'>)</span>

  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_type_name'>type_name</span>
  <span class='ivar'>@element_names</span> <span class='op'>=</span> <span class='id identifier rubyid_element_names'>element_names</span>
  <span class='ivar'>@element_values</span> <span class='op'>=</span> <span class='id identifier rubyid_element_values'>element_values</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_element_names'>element_names</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>

  <span class='comment'># now add the constant values at the same scope
</span>  <span class='comment'># ...or, enum values are only usable in specific contexts?
</span>  <span class='comment'>#    element_names.each_index do |idx|
</span>  <span class='comment'>#      syms.add!(element_names[idx], Var.new(element_names[idx], self, element_values[idx]))
</span>  <span class='comment'>#    end
</span><span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="element_names-instance_method">
  
    #<strong>element_names</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute element_names.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


355
356
357</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 355</span>

<span class='kw'>def</span> <span class='id identifier rubyid_element_names'>element_names</span>
  <span class='ivar'>@element_names</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="element_values-instance_method">
  
    #<strong>element_values</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute element_values.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


355
356
357</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 355</span>

<span class='kw'>def</span> <span class='id identifier rubyid_element_values'>element_values</span>
  <span class='ivar'>@element_values</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="width-instance_method">
  
    #<strong>width</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute width.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


355
356
357</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 355</span>

<span class='kw'>def</span> <span class='id identifier rubyid_width'>width</span>
  <span class='ivar'>@width</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="clone-instance_method">
  
    #<strong>clone</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


374
375
376</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 374</span>

<span class='kw'>def</span> <span class='id identifier rubyid_clone'>clone</span>
  <span class='const'><span class='object_link'><a href="" title="Idl::EnumerationType (class)">EnumerationType</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="#initialize-instance_method" title="Idl::EnumerationType#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='ivar'>@name</span><span class='comma'>,</span> <span class='ivar'>@element_names</span><span class='comma'>,</span> <span class='ivar'>@element_values</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="value-instance_method">
  
    #<strong>value</strong>(element_name)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


378
379
380
381
382
383</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/type.rb', line 378</span>

<span class='kw'>def</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_element_name'>element_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_i'>i</span> <span class='op'>=</span> <span class='ivar'>@element_names</span><span class='period'>.</span><span class='id identifier rubyid_index'>index</span><span class='lparen'>(</span><span class='id identifier rubyid_element_name'>element_name</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='kw'>nil</span> <span class='kw'>if</span> <span class='id identifier rubyid_i'>i</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@element_values</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>