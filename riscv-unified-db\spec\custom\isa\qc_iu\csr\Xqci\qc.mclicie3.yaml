# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicie3
long_name: IRQ Enable 3
address: 0x7fb
length: 32
base: 32
priv_mode: M
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Enable bits for IRQs 96-127
fields:
  IRQ96:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ96 enabled
  IRQ97:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ97 enabled
  IRQ98:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ98 enabled
  IRQ99:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ99 enabled
  IRQ100:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ100 enabled
  IRQ101:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ101 enabled
  IRQ102:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ102 enabled
  IRQ103:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ103 enabled
  IRQ104:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ104 enabled
  IRQ105:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ105 enabled
  IRQ106:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ106 enabled
  IRQ107:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ107 enabled
  IRQ108:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ108 enabled
  IRQ109:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ109 enabled
  IRQ110:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ110 enabled
  IRQ111:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ111 enabled
  IRQ112:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ112 enabled
  IRQ113:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ113 enabled
  IRQ114:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ114 enabled
  IRQ115:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ115 enabled
  IRQ116:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ116 enabled
  IRQ117:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ117 enabled
  IRQ118:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ118 enabled
  IRQ119:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ119 enabled
  IRQ120:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ120 enabled
  IRQ121:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ121 enabled
  IRQ122:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ122 enabled
  IRQ123:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ123 enabled
  IRQ124:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ124 enabled
  IRQ125:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ125 enabled
  IRQ126:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ126 enabled
  IRQ127:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ127 enabled
