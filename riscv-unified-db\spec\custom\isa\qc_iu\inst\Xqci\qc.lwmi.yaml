# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.lwmi
long_name: Load word multiple (Immediate)
description: |
  Loads multiple words starting from address (`rs1` + `imm`) to registers, starting from `rd`.
  The number of words is in the `length` immediate.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilsm
base: 32
encoding:
  match: 01---------------111-----0001011
  variables:
    - name: imm
      location: 29-25
      left_shift: 2
    - name: rs1
      location: 19-15
    - name: length
      location: 24-20
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, length, imm(xs1)"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg vaddr = X[rs1] + imm;
  raise (ExceptionCode::IllegalInstruction, effective_ldst_mode(), $encoding) if ((rd + length) > 32);
  for (U32 i = 0; i < length; i++) {
    X[rd + i] = read_memory<32>(vaddr, $encoding);
    vaddr = vaddr + 4;
  }
