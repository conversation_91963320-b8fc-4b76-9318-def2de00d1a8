<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::Compiler
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::Compiler";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (C)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">Compiler</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::Compiler
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Idl::Compiler</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>the Idl compiler</p>


  </div>
</div>
<div class="tags">
  

</div>






  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#compile_expression-instance_method" title="#compile_expression (instance method)">#<strong>compile_expression</strong>(expression, symtab, pass_error: false)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#compile_file-instance_method" title="#compile_file (instance method)">#<strong>compile_file</strong>(path, symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#compile_func_body-instance_method" title="#compile_func_body (instance method)">#<strong>compile_func_body</strong>(body, return_type: nil, symtab: SymbolTable.new, name: nil, parent: nil, input_file: nil, input_line: 0, no_rescue: false)  &#x21d2; Ast </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>compile a function body, and return the abstract syntax tree.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#compile_inst_operation-instance_method" title="#compile_inst_operation (instance method)">#<strong>compile_inst_operation</strong>(inst, input_file: nil, input_line: 0)  &#x21d2; Ast </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>compile an instruction operation, and return the abstract syntax tree.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(arch_def)  &#x21d2; Compiler </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Compiler.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(ast, symtab, what)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Type check an abstract syntax tree.</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(arch_def)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::Compiler (class)">Compiler</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Compiler.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>arch_def</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="../ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Architecture defintion, the context of the compilation</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


39
40
41
42</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 39</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_arch_def'>arch_def</span><span class='rparen'>)</span>
  <span class='ivar'>@parser</span> <span class='op'>=</span> <span class='const'>IdlParser</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span>
  <span class='ivar'>@arch_def</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="compile_expression-instance_method">
  
    #<strong>compile_expression</strong>(expression, symtab, pass_error: false)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


189
190
191
192
193
194
195
196
197
198
199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 189</span>

<span class='kw'>def</span> <span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_expression'>expression</span><span class='comma'>,</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='label'>pass_error:</span> <span class='kw'>false</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_m'>m</span> <span class='op'>=</span> <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_parse'>parse</span><span class='lparen'>(</span><span class='id identifier rubyid_expression'>expression</span><span class='comma'>,</span> <span class='label'>root:</span> <span class='symbol'>:expression</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>SyntaxError</span><span class='comma'>,</span> <span class='heredoc_beg'>&lt;&lt;~MSG</span>
<span class='tstring_content'>      While parsing </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_expression'>expression</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_line'>failure_line</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_column'>failure_column</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_reason'>failure_reason</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    MSG
</span>  <span class='kw'>end</span>

  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
  <span class='kw'>begin</span>
    <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_pass_error'>pass_error</span>

    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Compiling </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_expression'>expression</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_backtrace'>backtrace</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">InternalError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_pass_error'>pass_error</span>

    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Compiling </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_expression'>expression</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_backtrace'>backtrace</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="compile_file-instance_method">
  
    #<strong>compile_file</strong>(path, symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 44</span>

<span class='kw'>def</span> <span class='id identifier rubyid_compile_file'>compile_file</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='comma'>,</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_m'>m</span> <span class='op'>=</span> <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_parse'>parse</span> <span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span>

  <span class='kw'>if</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>SyntaxError</span><span class='comma'>,</span> <span class='heredoc_beg'>&lt;&lt;~MSG</span>
<span class='tstring_content'>      While parsing </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_input_file'>input_file</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_line'>failure_line</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_column'>failure_column</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_reason'>failure_reason</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    MSG
</span>  <span class='kw'>end</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected type </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="IsaAst.html" title="Idl::IsaAst (class)">IsaAst</a></span></span><span class='rparen'>)</span>

  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>

  <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_path'>path</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>
  <span class='kw'>begin</span>
    <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">InternalError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_bt'>bt</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="compile_func_body-instance_method">
  
    #<strong>compile_func_body</strong>(body, return_type: nil, symtab: SymbolTable.new, name: nil, parent: nil, input_file: nil, input_line: 0, no_rescue: false)  &#x21d2; <tt>Ast</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>compile a function body, and return the abstract syntax tree</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>body</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Function body source code</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>return_type</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Expected return type, if known</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
        <em class="default">(defaults to: <tt>SymbolTable.new</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table to use for type checking</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>name</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Function name, used for error messages</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>parent</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Parent class of the function, used for error messages</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>input_file</span>
      
      
        <span class='type'>(<tt>Pathname</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Path to the input file this source comes from</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>input_line</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>0</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Starting line in the input file that this source comes from</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>no_rescue</span>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>false</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not to automatically catch any errors</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Ast</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The root of the abstract syntax tree</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
***********
136
137</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 83</span>

<span class='kw'>def</span> <span class='id identifier rubyid_compile_func_body'>compile_func_body</span><span class='lparen'>(</span><span class='id identifier rubyid_body'>body</span><span class='comma'>,</span> <span class='label'>return_type:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>symtab:</span> <span class='const'><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="SymbolTable.html#initialize-instance_method" title="Idl::SymbolTable#initialize (method)">new</a></span></span><span class='comma'>,</span> <span class='label'>name:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>parent:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>input_file:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>input_line:</span> <span class='int'>0</span><span class='comma'>,</span> <span class='label'>no_rescue:</span> <span class='kw'>false</span><span class='rparen'>)</span>
  <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_input_file'>input_file</span><span class='comma'>,</span> <span class='id identifier rubyid_input_line'>input_line</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span> <span class='op'>=</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_deep_clone'>deep_clone</span>
  <span class='kw'>while</span> <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>!=</span> <span class='int'>1</span>
    <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_m'>m</span> <span class='op'>=</span> <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_parse'>parse</span><span class='lparen'>(</span><span class='id identifier rubyid_body'>body</span><span class='comma'>,</span> <span class='label'>root:</span> <span class='symbol'>:function_body</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>SyntaxError</span><span class='comma'>,</span> <span class='heredoc_beg'>&lt;&lt;~MSG</span>
<span class='tstring_content'>      While parsing </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_parent'>parent</span><span class='embexpr_end'>}</span><span class='tstring_content'>::</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_line'>failure_line</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_column'>failure_column</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_reason'>failure_reason</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    MSG
</span>  <span class='kw'>end</span>

  <span class='comment'># fix up left recursion
</span>  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>

  <span class='comment'># type check
</span>  <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='period'>.</span><span class='id identifier rubyid_push'>push</span>
  <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>__expected_return_type</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_return_type'>return_type</span><span class='rparen'>)</span> <span class='kw'>unless</span> <span class='id identifier rubyid_return_type'>return_type</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='kw'>begin</span>
    <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_statements'>statements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_s'>s</span><span class='op'>|</span>
      <span class='id identifier rubyid_s'>s</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_no_rescue'>no_rescue</span>

    <span class='kw'>if</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_parent'>parent</span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>In function </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> of </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_parent'>parent</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>In function </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">InternalError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='kw'>if</span> <span class='id identifier rubyid_no_rescue'>no_rescue</span>

    <span class='kw'>if</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_parent'>parent</span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>In function </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> of </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_parent'>parent</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>In function </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_backtrace'>backtrace</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>ensure</span>
    <span class='id identifier rubyid_cloned_symtab'>cloned_symtab</span><span class='period'>.</span><span class='id identifier rubyid_pop'>pop</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="compile_inst_operation-instance_method">
  
    #<strong>compile_inst_operation</strong>(inst, input_file: nil, input_line: 0)  &#x21d2; <tt>Ast</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>compile an instruction operation, and return the abstract syntax tree</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>inst</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="../Instruction.html" title="Instruction (class)">Instruction</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Instruction object</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table to use for type checking</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>input_file</span>
      
      
        <span class='type'>(<tt>Pathname</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>nil</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Path to the input file this source comes from</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>input_line</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>0</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Starting line in the input file that this source comes from</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Ast</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The root of the abstract syntax tree</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


***********
***********
***********
***********
***********
***********</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 146</span>

<span class='kw'>def</span> <span class='id identifier rubyid_compile_inst_operation'>compile_inst_operation</span><span class='lparen'>(</span><span class='id identifier rubyid_inst'>inst</span><span class='comma'>,</span> <span class='label'>input_file:</span> <span class='kw'>nil</span><span class='comma'>,</span> <span class='label'>input_line:</span> <span class='int'>0</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_operation'>operation</span> <span class='op'>=</span> <span class='id identifier rubyid_inst'>inst</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>operation()</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_input_file'>input_file</span><span class='comma'>,</span> <span class='id identifier rubyid_input_line'>input_line</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_m'>m</span> <span class='op'>=</span> <span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_parse'>parse</span><span class='lparen'>(</span><span class='id identifier rubyid_operation'>operation</span><span class='comma'>,</span> <span class='label'>root:</span> <span class='symbol'>:instruction_operation</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>SyntaxError</span><span class='comma'>,</span> <span class='heredoc_beg'>&lt;&lt;~MSG</span>
<span class='tstring_content'>      While parsing </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_input_file'>input_file</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_line'>failure_line</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_column'>failure_column</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='tstring_content'>
</span><span class='tstring_content'>      </span><span class='embexpr_beg'>#{</span><span class='ivar'>@parser</span><span class='period'>.</span><span class='id identifier rubyid_failure_reason'>failure_reason</span><span class='embexpr_end'>}</span><span class='tstring_content'>
</span><span class='heredoc_end'>    MSG
</span>  <span class='kw'>end</span>

  <span class='comment'># fix up left recursion
</span>  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='id identifier rubyid_m'>m</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
  <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Inst </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_inst'>inst</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_input_file'>input_file</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_input_line'>input_line</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(ast, symtab, what)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Type check an abstract syntax tree</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ast</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An abstract syntax tree</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The compilation context</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>what</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A description of what you are type checking (for error messages)</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>AstNode::TypeError if a type error is found</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


171
172
173
174
175
176
177
178
179
180
181
182
183
184
185
186
187</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 171</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_ast'>ast</span><span class='comma'>,</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='comma'>,</span> <span class='id identifier rubyid_what'>what</span><span class='rparen'>)</span>
  <span class='comment'># type check
</span>  <span class='kw'>begin</span>
    <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>While type checking </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_what'>what</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">InternalError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>While type checking </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_what'>what</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_what'>what</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_backtrace'>backtrace</span>
    <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>