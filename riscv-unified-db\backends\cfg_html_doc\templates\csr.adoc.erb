:tabs-sync-option:

[csr:#<%= csr.name %>-def]
= <%= csr.name %>

*<%= csr.long_name %>*

<%= cfg_arch.render_erb(csr.description) %>

== Attributes
[%autowidth]
|===
h| CSR Address    | <%= "0x#{csr.address.to_s(16)}" %>
<%- if csr.priv_mode == 'VS' -%>
h| Virtual CSR Address    | <%= "0x#{csr.virtual_address.to_s(16)}" %>
<%- end -%>
h| Defining extension a| <%= csr.defined_by_condition.to_asciidoc %>
h| Length         | <%= csr.length_pretty %>
h| Privilege Mode | <%= csr.priv_mode %>
|===

== Format
<%- unless csr.dynamic_length? || csr.fields.any? { |f| f.dynamic_location? } -%>
<%# CSR has a known static length, so there is only one format to display -%>
.<%= csr.name %> format
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= JSON.dump csr.wavedrom_desc(cfg_arch, cfg_arch.param_values["MXLEN"], exclude_unimplemented: true) %>
....
<%- else -%>
<%# CSR has a dynamic length, or a field has a dynamic location,
    so there is more than one format to display -%>
This CSR format changes dynamically.

.<%= csr.name %> Format when <%= csr.length_cond32 %>
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= JSON.dump csr.wavedrom_desc(cfg_arch, 32, exclude_unimplemented: true) %>
....

.<%= csr.name %> Format when <%= csr.length_cond64 %>
[wavedrom, ,svg,subs='attributes',width="100%"]
....
<%= JSON.dump csr.wavedrom_desc(cfg_arch, 64, exclude_unimplemented: true) %>
....


<%- end -%>

== Field Summary

[%autowidth,float="center",align="center",cols="^,<,<,<",options="header",role="stretch"]
|===
|Name | Location | Type | Reset Value

<%- csr.possible_fields.each do |field| -%>
| `xref:<%=csr.name%>-<%=field.name%>-def[<%= field.name %>]`
| <%= field.location_pretty(cfg_arch.multi_xlen? ? nil : cfg_arch.possible_xlens[0]) %>
| <%= field.type_pretty(cfg_arch.multi_xlen? ? nil : cfg_arch.possible_xlens[0]) %>
| <%= field.reset_value %>

<%- end -%>
|===

== Fields

<%- if csr.possible_fields.empty? -%>
This CSR has no fields. However, it must still exist (not cause an `Illegal Instruction` trap) and always return zero on a read.
<%- else -%>

<%- csr.possible_fields.each do |field| -%>
[[<%=csr.name%>-<%=field.name%>-def]]
=== `<%= field.name %>`

[.csr-field-info]
--
Location::
`<%=field.csr.name%>[<%= field.location_pretty %>]`

Description::
<%= cfg_arch.render_erb(field.description).gsub("\n\n", "\n+\n") %>

Type::
[%autowidth]
|===

| <%= field.type %> | <%= field.type_desc %>
|===

Reset value::
<%= field.reset_value %>

<%- if field.has_custom_sw_write? -%>
Software write::
This field has special behavior when written by software (_e.g._, through `csrrw`).
+
When software tries to write `csr_value`, the field will be written with the return value of the function below.
+
<%- if cfg_arch.multi_xlen? && csr.defined_in_all_bases? && field.defined_in_all_bases? -%>
[tabs]
====
RV32::
+
[source,idl,subs="specialchars,macros"]
----
<%= field.pruned_sw_write_ast(32).gen_adoc %>
----

RV64::
+
[source,idl,subs="specialchars,macros"]
----
<%= field.pruned_sw_write_ast(64).gen_adoc %>
----
<%- else -%>
<%- xlen = !cfg_arch.multi_xlen? ? cfg_arch.mxlen : (!csr.defined_in_all_bases? ? csr.base : field.base) -%>
[source,idl,subs="specialchars,macros"]
----
<%= field.pruned_sw_write_ast(xlen).gen_adoc %>
----
<%- end -%>
<%- end -%>

--

<%- end -%>
<%- end -%>

<%- if csr.has_custom_sw_read? -%>
== Software read

This CSR may return a value that is different from what is stored in hardware.

[tabs]
====
Pruned::
+
[source,idl,subs="specialchars,macros"]
----
<%= csr.pruned_sw_read_ast(cfg_arch.multi_xlen? ? nil : cfg_arch.possible_xlens[0]).gen_adoc %>
----

Original::
+
[source,idl,subs="specialchars,macros"]
----
<%= csr.type_checked_sw_read_ast(cfg_arch.multi_xlen? ? nil : cfg_arch.possible_xlens[0]).gen_adoc %>
----
====
<%- end -%>
