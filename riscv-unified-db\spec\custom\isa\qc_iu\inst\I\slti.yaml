# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# add hints using slti

hints:
  - { $ref: inst/Xqci/qc.pcoredump.yaml# }
  - { $ref: inst/Xqci/qc.psyscalli.yaml# }
  - { $ref: inst/Xqci/qc.pputci.yaml# }
  - { $ref: inst/Xqci/qc.ppregs.yaml# }
  - { $ref: inst/Xqci/qc.ppreg.yaml# }
  - { $ref: inst/Xqci/qc.pputc.yaml# }
  - { $ref: inst/Xqci/qc.pputs.yaml# }
  - { $ref: inst/Xqci/qc.psyscall.yaml# }
  - { $ref: inst/Xqci/qc.pexit.yaml# }
