# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Smdbltrp
long_name: Double trap
description: |
  The `Smdbltrp` extension addresses a double trap in M-mode.
  When the `Smrnmi` extension is implemented, it enables invocation of the RNMI handler on a
  double trap in M-mode to handle the critical error.
  If the `Smrnmi` extension is not implemented or if a double trap occurs during the RNMI
  handler's execution, this extension helps transition the hart to a critical error state and
  enables signaling the critical error to the platform.

  To improve error diagnosis and resolution, this extension supports debugging harts in a critical
  error state. The extension introduces a mechanism to enter Debug Mode instead of asserting a
  critical-error signal to the platform when the hart is in a critical error state.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
