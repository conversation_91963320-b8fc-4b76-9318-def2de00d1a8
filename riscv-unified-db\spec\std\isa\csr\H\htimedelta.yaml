# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: htimedelta
long_name: Hypervisor time delta
description: |
  The `htimedelta` CSR is a 64-bit read/write register that contains the delta
  between the value of the `time` CSR and the value returned in VS-mode or VU-mode.
  That is, reading the `time` CSR in VS or VU mode returns the sum of the contents
  of `htimedelta` and the actual value of `time`.

  [NOTE]
  Because overflow is ignored when summing `htimedelta` and `time`, large values of
  `htimedelta` may be used to represent negative time offsets.

address: 0x605
writable: true
priv_mode: S
definedBy: H
length: 64
fields:
  DELTA:
    location: 63-0
    description: Signed delta
    type: RW
    reset_value: UNDEFINED_LEGAL
