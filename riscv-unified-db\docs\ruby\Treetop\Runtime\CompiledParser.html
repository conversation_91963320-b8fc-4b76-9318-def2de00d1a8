<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Treetop::Runtime::CompiledParser
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Treetop::Runtime::CompiledParser";
  relpath = '../../';
</script>


  <script type="text/javascript" charset="utf-8" src="../../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../../_index.html">Index (C)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../../Treetop.html" title="Treetop (module)">Treetop</a></span></span> &raquo; <span class='title'><span class='object_link'><a href="../Runtime.html" title="Treetop::Runtime (module)">Runtime</a></span></span>
     &raquo; 
    <span class="title">CompiledParser</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Treetop::Runtime::CompiledParser
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Treetop::Runtime::CompiledParser</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>open up Treetop::Runtime::CompiledParser and add a few utility functions so we can track where the code is coming from</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#input_file-instance_method" title="#input_file (instance method)">#<strong>input_file</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute input_file.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#instantiate_node-instance_method" title="#instantiate_node (instance method)">#<strong>instantiate_node</strong>(node_type, *args)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>override instatiate_node so we can set the input file.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#orig_instantiate_node-instance_method" title="#orig_instantiate_node (instance method)">#<strong>orig_instantiate_node</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>alias instantiate_node so we can call it from the override.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#set_input_file-instance_method" title="#set_input_file (instance method)">#<strong>set_input_file</strong>(filename, starting_line = 0)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="input_file-instance_method">
  
    #<strong>input_file</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute input_file.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


10
11
12</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 10</span>

<span class='kw'>def</span> <span class='id identifier rubyid_input_file'>input_file</span>
  <span class='ivar'>@input_file</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="instantiate_node-instance_method">
  
    #<strong>instantiate_node</strong>(node_type, *args)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>override instatiate_node so we can set the input file</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


21
22
23
24
25</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 21</span>

<span class='kw'>def</span> <span class='id identifier rubyid_instantiate_node'>instantiate_node</span><span class='lparen'>(</span><span class='id identifier rubyid_node_type'>node_type</span><span class='comma'>,</span><span class='op'>*</span><span class='id identifier rubyid_args'>args</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_node'>node</span> <span class='op'>=</span> <span class='id identifier rubyid_orig_instantiate_node'>orig_instantiate_node</span><span class='lparen'>(</span><span class='id identifier rubyid_node_type'>node_type</span><span class='comma'>,</span> <span class='op'>*</span><span class='id identifier rubyid_args'>args</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_node'>node</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_input_file'>input_file</span><span class='comma'>,</span> <span class='ivar'>@starting_line</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='int'>0</span> <span class='op'>:</span> <span class='ivar'>@starting_line</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_node'>node</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="orig_instantiate_node-instance_method">
  
    #<strong>orig_instantiate_node</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>alias instantiate_node so we can call it from the override</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


18</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 18</span>

<span class='kw'>alias</span> <span class='id identifier rubyid_orig_instantiate_node'>orig_instantiate_node</span> <span class='id identifier rubyid_instantiate_node'>instantiate_node</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="set_input_file-instance_method">
  
    #<strong>set_input_file</strong>(filename, starting_line = 0)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


12
13
14
15</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl.rb', line 12</span>

<span class='kw'>def</span> <span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_filename'>filename</span><span class='comma'>,</span> <span class='id identifier rubyid_starting_line'>starting_line</span> <span class='op'>=</span> <span class='int'>0</span><span class='rparen'>)</span>
  <span class='ivar'>@input_file</span> <span class='op'>=</span> <span class='id identifier rubyid_filename'>filename</span>
  <span class='ivar'>@starting_line</span> <span class='op'>=</span> <span class='id identifier rubyid_starting_line'>starting_line</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>