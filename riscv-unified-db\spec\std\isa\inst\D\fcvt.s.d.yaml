# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: fcvt.s.d
long_name: Floating-point Convert Double-precision to Single-precision
description:
  - id: inst-fcvt.s.d-behaviour
    normative: false
    text: |
      `fcvt.s.d` converts a double-precision floating-point number to a single-precision floating-point number.
       This is encoded in the OP-FP major opcode space and both the source and destination are floating-point
       registers. The `rs2` field encodes the datatype of the source, and the `fmt` field encodes the datatype
       of the destination. `fcvt.s.d` rounds according to the RM field
definedBy: D
assembly: fd, fs1, rm
encoding:
  match: 010000000001-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
