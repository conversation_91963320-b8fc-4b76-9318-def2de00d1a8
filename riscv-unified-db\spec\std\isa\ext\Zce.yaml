# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zce
long_name: Compressed instructions for microcontrollers
description: |
  The Zce extension is intended to be used for microcontrollers, and includes all relevant Zc
  extensions.

  * Specifying `Zce` on RV32 without `F` includes `<PERSON>ca`, `Zcb`, `Zcmp`, `Zcmt`
  * Specifying `Zce` on RV32 with `F` includes `<PERSON>ca`, `Zcb`, `Zcmp`, `Zcmt` and `Zcf`
  * Specifying `Zce` on RV64 always includes `<PERSON>ca`, `Zcb`, `Zcmp`, `Zcmt`
  * `Zcf` doesn't exist for RV64

  Therefore common ISA strings can be updated as follows to include the relevant Zc extensions, for
  example:

  * RV32IMC becomes RV32IM_Zce
  * RV32IMCF becomes RV32IMF_Zce
type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-04
    repositories:
      - url: https://github.com/riscv/riscv-code-size-reduction
        branch: main
    contributors:
      - name: Tariq Kurd
      - name: Ibrahim Abu Kharmeh
      - name: Torbjørn Viem Ness
      - name: Matteo Perotti
      - name: Nidal Faour
      - name: Bill Traynor
      - name: <PERSON> Sene
      - name: Xinlong Wu
      - name: sinan
      - name: Jeremy Bennett
      - name: Heda Chen
      - name: Alasdair Armstrong
      - name: Graeme Smecher
      - name: Nicolas Brunie
      - name: Jiawei
    implies:
      - name: Zca
        version: "1.0.0"
      - name: Zcb
        version: "1.0.0"
      - name: Zcmp
        version: "1.0.0"
      - name: Zcmt
        version: "1.0.0"

      # TODO: this implication is conditional!!! (see description)
      # So it should look something like this:

      # if:
      #   allOf:
      #     param:
      #       XLEN: 32
      #     extensions:
      #       - F
      # then:
      #   - [Zca, "1.0.0"]
      #   - [Zcb, "1.0.0"]
      #   - [Zcmp, "1.0.0"]
      #   - [Zcmt, "1.0.0"]
      #   - [Zf, "1.0.0"]
      # else:
      #   # TODO: this implication is conditional!!! (see description)
      #   - [Zca, "1.0.0"]
      #   - [Zcb, "1.0.0"]
      #   - [Zcmp, "1.0.0"]
      #   - [Zcmt, "1.0.0"]
conflicts:
  anyOf:
    - allOf: [C, D]
    - Zcd
