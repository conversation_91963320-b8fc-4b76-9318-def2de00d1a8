# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zicbom
long_name: Cache block management instructions
description: Cache block management instructions
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2022-05
params:
  CACHE_BLOCK_SIZE:
    description: |
      The observable size of a cache block, in bytes
    also_defined_in: [Zicboz, Zicbop]
    schema:
      type: integer
      minimum: 1
      maximum: 0xFFFFFFFFFFFFFFFF
  FORCE_UPGRADE_CBO_INVAL_TO_FLUSH:
    description: |
      When true, an implementation prohibits setting `menvcfg.CBIE` == `11` such that all `cbo.inval`
      instructions either trap (when `menvcfg.CBIE` == '00') or flush (when `menvcfg.CBIE` == '01').

      When false, an implementation allows a true INVAL operation for `cbo.inval`, and thus supports
      the setting `menvcfg.CBIE` == `11`.
    schema:
      type: boolean
