<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Instruction::Encoding
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Instruction::Encoding";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (E)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Instruction.html" title="Instruction (class)">Instruction</a></span></span>
     &raquo; 
    <span class="title">Encoding</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Instruction::Encoding
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Instruction::Encoding</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>represents an instruction encoding</p>


  </div>
</div>
<div class="tags">
  

</div><h2>Defined Under Namespace</h2>
<p class="children">
  
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="Encoding/Field.html" title="Instruction::Encoding::Field (class)">Field</a></span>
    
  
</p>




  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#decode_variables-instance_method" title="#decode_variables (instance method)">#<strong>decode_variables</strong>  &#x21d2; Array&lt;DecodeVariable&gt; </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of decode variables.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#format-instance_method" title="#format (instance method)">#<strong>format</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Format, as a string of 0,1 and -,.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#opcode_fields-instance_method" title="#opcode_fields (instance method)">#<strong>opcode_fields</strong>  &#x21d2; Array&lt;Field&gt; </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of fields containing opcodes.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(format, decode_vars)  &#x21d2; Encoding </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Encoding.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#size-instance_method" title="#size (instance method)">#<strong>size</strong>  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Size, in bits, of the encoding.</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(format, decode_vars)  &#x21d2; <tt><span class='object_link'><a href="" title="Instruction::Encoding (class)">Encoding</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Encoding.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>format</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Format of the encoding, as 0’s, 1’s and -‘s (for decode variables)</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>decode_vars</span>
      
      
        <span class='type'>(<tt>Array&lt;Hash&lt;String,Object&gt;&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of decode variable defintions from the arch spec</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


947
948
949
950
951
952
953
954
955
956
957
958
959
960
961
962
963
964
965</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 947</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_format'>format</span><span class='comma'>,</span> <span class='id identifier rubyid_decode_vars'>decode_vars</span><span class='rparen'>)</span>
  <span class='ivar'>@format</span> <span class='op'>=</span> <span class='id identifier rubyid_format'>format</span>

  <span class='ivar'>@opcode_fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_msb'>msb</span> <span class='op'>=</span> <span class='ivar'>@format</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
  <span class='ivar'>@format</span><span class='period'>.</span><span class='id identifier rubyid_split'>split</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
      <span class='id identifier rubyid_msb'>msb</span> <span class='op'>-=</span> <span class='int'>1</span>
    <span class='kw'>else</span>
      <span class='ivar'>@opcode_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="Encoding/Field.html" title="Instruction::Encoding::Field (class)">Field</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Encoding/Field.html#initialize-instance_method" title="Instruction::Encoding::Field#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='comma'>,</span> <span class='lparen'>(</span><span class='id identifier rubyid_msb'>msb</span> <span class='op'>-</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>+</span> <span class='int'>1</span><span class='rparen'>)</span><span class='op'>..</span><span class='id identifier rubyid_msb'>msb</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_msb'>msb</span> <span class='op'>-=</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>

  <span class='ivar'>@decode_variables</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_decode_vars'>decode_vars</span><span class='op'>&amp;.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_var'>var</span><span class='op'>|</span>
    <span class='ivar'>@decode_variables</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="../DecodeVariable.html" title="DecodeVariable (class)">DecodeVariable</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="../DecodeVariable.html#initialize-instance_method" title="DecodeVariable#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='comma'>,</span> <span class='id identifier rubyid_var'>var</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="decode_variables-instance_method">
  
    #<strong>decode_variables</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="../DecodeVariable.html" title="DecodeVariable (class)">DecodeVariable</a></span>&gt;</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of decode variables.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="../DecodeVariable.html" title="DecodeVariable (class)">DecodeVariable</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of decode variables</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


918
919
920</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 918</span>

<span class='kw'>def</span> <span class='id identifier rubyid_decode_variables'>decode_variables</span>
  <span class='ivar'>@decode_variables</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="format-instance_method">
  
    #<strong>format</strong>  &#x21d2; <tt>String</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns format, as a string of 0,1 and -,.</p>


  </div>
</div>
<div class="tags">
  
  <div class="examples">
    <p class="tag_title">Examples:</p>
    
      
        <p class="example_title"><div class='inline'>
<p>Format of ‘sd`</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_sd'>sd</span><span class='period'>.</span><span class='id identifier rubyid_format'>format</span> <span class='comment'>#=&gt; &#39;-----------------011-----0100011&#39;</span></code></pre>
    
  </div>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>format, as a string of 0,1 and -,</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


910
911
912</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 910</span>

<span class='kw'>def</span> <span class='id identifier rubyid_format'>format</span>
  <span class='ivar'>@format</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="opcode_fields-instance_method">
  
    #<strong>opcode_fields</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Encoding/Field.html" title="Instruction::Encoding::Field (class)">Field</a></span>&gt;</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of fields containing opcodes.</p>


  </div>
</div>
<div class="tags">
  
  <div class="examples">
    <p class="tag_title">Examples:</p>
    
      
        <p class="example_title"><div class='inline'>
<p>opcode_fields of ‘sd`</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_sd'>sd</span><span class='period'>.</span><span class='id identifier rubyid_opcode_fields'>opcode_fields</span> <span class='comment'>#=&gt; [Field(&#39;011&#39;, ...), Field(&#39;0100011&#39;, ...)]</span></code></pre>
    
  </div>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Encoding/Field.html" title="Instruction::Encoding::Field (class)">Field</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of fields containing opcodes</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


915
916
917</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 915</span>

<span class='kw'>def</span> <span class='id identifier rubyid_opcode_fields'>opcode_fields</span>
  <span class='ivar'>@opcode_fields</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="size-instance_method">
  
    #<strong>size</strong>  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Size, in bits, of the encoding.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Size, in bits, of the encoding</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


968
969
970</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 968</span>

<span class='kw'>def</span> <span class='id identifier rubyid_size'>size</span>
  <span class='ivar'>@format</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>