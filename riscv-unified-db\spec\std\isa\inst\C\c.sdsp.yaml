# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.sdsp
long_name: Store doubleword to stack
description: |
  Stores a 64-bit value in register rs2 to memory.
  It computes an effective address by adding the zero-extended offset, scaled by 8,
  to the stack pointer, x2.
  It expands to `sd` `xs2, offset(x2)`.
definedBy:
  anyOf:
    - C
    - Zca
    - Zclsd
assembly: xs2, imm(sp)
encoding:
  RV32:
    match: 111-----------10
    variables:
      - name: xs2
        location: 6-2
        not: [1, 3, 5, 7]
      - name: imm
        location: 9-7|12-10
        left_shift: 3
  RV64:
    match: 111-----------10
    variables:
      - name: xs2
        location: 6-2
      - name: imm
        location: 9-7|12-10
        left_shift: 3
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  XReg virtual_address = X[2] + imm;

  if (xlen() == 32) {
    if (implemented?(ExtensionName::Zclsd)) {
      Bits<64> data = {X[creg2reg(xs2) + 1], X[creg2reg(xs2)]};
      write_memory<64>(virtual_address, data, $encoding);
    } else {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else {
    write_memory<64>(virtual_address, X[creg2reg(xs2)], $encoding);
  }
