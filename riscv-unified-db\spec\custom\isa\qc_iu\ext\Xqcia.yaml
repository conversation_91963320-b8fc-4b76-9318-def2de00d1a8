# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqcia
type: unprivileged
long_name: Qualcomm arithmetic
versions:
- version: "0.1.0"
  state: development
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Add information about instruction formats of each instruction
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix description and functionality of qc.wrapi instruction
- version: "0.4.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Rename qc.slasat -> qc.shlsat
    - Rename qc.sllsat -> qc.shlusat
- version: "0.5.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix typos in description of qc.shlsat and qc.shlusat instructions
    - Fix bug in qc.shlsat that caused wrong IDL code result
    - Fix clobbering of saturation results in [add|addu|sub]sat instructions
- version: "0.6.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix wrong mantissa bit selection in qc.norm, qc.normu and qc.normeu instructions
    - Fix wrong exponent calculation in qc.normeu instruction
- version: "0.7.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code of qc.shlusat instruction (should use zero-extension and not sign-extension)
    - Fix IDL code of qc.shlusat and qc.shlsat instructions (should compare values of xlen()*2 bits)
    - Fix IDL code of qc.shlusat and qc.shlsat instructions because change in IDL operators (width adjustment)
    - Fix IDL code of qc.subsat, qc.addusat and qc.addsat instructions because change in IDL operators (width adjustment)
description: |
  The Xqcia extension includes eleven instructions to perform integer arithmetic.

doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
