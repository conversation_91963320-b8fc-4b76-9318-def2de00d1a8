# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Smaia
long_name: Advanced Interrupt Architecture, M-mode extension
description: Advanced Interrupt Architecture, M-mode extension
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-06
    url: https://github.com/riscv/riscv-aia/releases/download/1.0/riscv-interrupts-1.0.pdf
