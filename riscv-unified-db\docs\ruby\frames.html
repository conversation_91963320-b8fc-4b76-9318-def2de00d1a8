<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
	<title>Documentation by YARD 0.9.36</title>
</head>
<script type="text/javascript">
var mainUrl = 'index.html';
try {
    var match = decodeURIComponent(window.location.hash).match(/^#!(.+)/);
    var name = match ? match[1] : mainUrl;
    var url = new URL(name, location.href);
    window.top.location.replace(url.origin === location.origin ? name : mainUrl);
} catch (e) {
    window.top.location.replace(mainUrl);
}
</script>
<noscript>
  <h1>Oops!</h1>
  <h2>YARD requires JavaScript!</h2>
</noscript>
</html>
