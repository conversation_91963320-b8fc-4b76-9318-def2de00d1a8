<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: DecodeVariable
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "DecodeVariable";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (D)</a> &raquo;
    
    
    <span class="title">DecodeVariable</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: DecodeVariable
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">DecodeVariable</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/opcodes.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>decode field constructions from YAML file, rather than riscv-opcodes eventually, we will move so that all instructions use the YAML file,</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#alias-instance_method" title="#alias (instance method)">#<strong>alias</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>alias of this field, or nil if none.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#left_shift-instance_method" title="#left_shift (instance method)">#<strong>left_shift</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>amount the field is left shifted before use, or nil is there is no left shift.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the name of the field.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#bits-instance_method" title="#bits (instance method)">#<strong>bits</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns bits of the encoding that make up the field, as an array   Each item of the array is either:     - A number, to represent a single bit     - A range, to represent a continugous range of bits.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#eql%3F-instance_method" title="#eql? (instance method)">#<strong>eql?</strong>(other)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extract-instance_method" title="#extract (instance method)">#<strong>extract</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>return code to extract the field.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extract_location-instance_method" title="#extract_location (instance method)">#<strong>extract_location</strong>(location)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#grouped_encoding_fields-instance_method" title="#grouped_encoding_fields (instance method)">#<strong>grouped_encoding_fields</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>array of constituent encoding fields.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#hash-instance_method" title="#hash (instance method)">#<strong>hash</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(inst, field_data)  &#x21d2; DecodeVariable </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of DecodeVariable.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#inst_pos_to_var_pos-instance_method" title="#inst_pos_to_var_pos (instance method)">#<strong>inst_pos_to_var_pos</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#sext%3F-instance_method" title="#sext? (instance method)">#<strong>sext?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>true if the field should be sign extended.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#size-instance_method" title="#size (instance method)">#<strong>size</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the number of bits in the field, _including any implicit ones_.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#size_in_encoding-instance_method" title="#size_in_encoding (instance method)">#<strong>size_in_encoding</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>the number of bits in the field, _not including any implicit ones_.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#split%3F-instance_method" title="#split? (instance method)">#<strong>split?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns true if the field is encoded across more than one groups of bits.</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(inst, field_data)  &#x21d2; <tt><span class='object_link'><a href="" title="DecodeVariable (class)">DecodeVariable</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of DecodeVariable.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


143
144
145
146
147
148
149
150
151
152
153
154
155
156</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 143</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_inst'>inst</span><span class='comma'>,</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='rparen'>)</span>
  <span class='ivar'>@inst</span> <span class='op'>=</span> <span class='id identifier rubyid_inst'>inst</span>
  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='ivar'>@left_shift</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>left_shift</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='int'>0</span> <span class='op'>:</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>left_shift</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='ivar'>@sext</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>sign_extend</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='kw'>false</span> <span class='op'>:</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>sign_extend</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='ivar'>@alias</span> <span class='op'>=</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>alias</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='kw'>nil</span> <span class='op'>:</span> <span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>alias</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_extract_location'>extract_location</span><span class='lparen'>(</span><span class='id identifier rubyid_field_data'>field_data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='ivar'>@decode_variable</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='ivar'>@alias</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='id identifier rubyid_name'>name</span>
    <span class='kw'>else</span>
      <span class='ivar'>@decode_variable</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='ivar'>@alias</span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="alias-instance_method">
  
    #<strong>alias</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>alias of this field, or nil if none</p>

<p>used, e.g., when a field reprsents more than one variable (like rs1/rd for destructive instructions)</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


51
52
53</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 51</span>

<span class='kw'>def</span> <span class='kw'>alias</span>
  <span class='ivar'>@alias</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="left_shift-instance_method">
  
    #<strong>left_shift</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>amount the field is left shifted before use, or nil is there is no left shift</p>

<p>For example, if the field is <a href="5:3">offset</a>, left_shift is 3</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


56
57
58</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 56</span>

<span class='kw'>def</span> <span class='id identifier rubyid_left_shift'>left_shift</span>
  <span class='ivar'>@left_shift</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the name of the field</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


46
47
48</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 46</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="bits-instance_method">
  
    #<strong>bits</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns bits of the encoding that make up the field, as an array</p>

<pre class="code ruby"><code class="ruby"> Each item of the array is either:
   - A number, to represent a single bit
   - A range, to represent a continugous range of bits

The array is ordered from encoding MSB (at index 0) to LSB (at index n-1)
</code></pre>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


177
178
179
180
181</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 177</span>

<span class='kw'>def</span> <span class='id identifier rubyid_bits'>bits</span>
  <span class='ivar'>@encoding_fields</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ef'>ef</span><span class='op'>|</span>
    <span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span> <span class='op'>?</span> <span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_first'>first</span> <span class='op'>:</span> <span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="eql?-instance_method">
  
    #<strong>eql?</strong>(other)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


158
159
160</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 158</span>

<span class='kw'>def</span> <span class='id identifier rubyid_eql?'>eql?</span><span class='lparen'>(</span><span class='id identifier rubyid_other'>other</span><span class='rparen'>)</span>
  <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_eql?'>eql?</span><span class='lparen'>(</span><span class='id identifier rubyid_other'>other</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extract-instance_method">
  
    #<strong>extract</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>return code to extract the field</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


199
200
201
202
203
204
205
206
207
208
209
210
211
212
213
214
215
216
217
218
219
220
221
222</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 199</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extract'>extract</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>=</span> <span class='int'>0</span>
  <span class='id identifier rubyid_bits'>bits</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_b'>b</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_op'>op</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_op'>op</span>
      <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>+=</span> <span class='int'>1</span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Range</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_op'>op</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_end'>end</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_begin'>begin</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_op'>op</span>
      <span class='id identifier rubyid_so_far'>so_far</span> <span class='op'>+=</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>&lt;&lt;</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@left_shift</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;d0</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@left_shift</span><span class='period'>.</span><span class='id identifier rubyid_zero?'>zero?</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_ops'>ops</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>&gt;</span> <span class='int'>1</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>{</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ops'>ops</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>, </span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_ops'>ops</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
  <span class='id identifier rubyid_ops'>ops</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>sext(</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ops'>ops</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_sext?'>sext?</span>
  <span class='id identifier rubyid_ops'>ops</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extract_location-instance_method">
  
    #<strong>extract_location</strong>(location)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 58</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extract_location'>extract_location</span><span class='lparen'>(</span><span class='id identifier rubyid_location'>location</span><span class='rparen'>)</span>
  <span class='ivar'>@encoding_fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>

  <span class='kw'>if</span> <span class='id identifier rubyid_location'>location</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span>
    <span class='ivar'>@encoding_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_location'>location</span><span class='op'>..</span><span class='id identifier rubyid_location'>location</span><span class='rparen'>)</span>
    <span class='kw'>return</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_location_string'>location_string</span> <span class='op'>=</span> <span class='id identifier rubyid_location'>location</span>
  <span class='id identifier rubyid_parts'>parts</span> <span class='op'>=</span> <span class='id identifier rubyid_location_string'>location_string</span><span class='period'>.</span><span class='id identifier rubyid_split'>split</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>|</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='id identifier rubyid_parts'>parts</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_part'>part</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_part'>part</span> <span class='op'>=~</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>^([0-9]+)$</span><span class='regexp_end'>/</span></span>
      <span class='id identifier rubyid_bit'>bit</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
      <span class='ivar'>@encoding_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_bit'>bit</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='op'>..</span><span class='id identifier rubyid_bit'>bit</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='rparen'>)</span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_part'>part</span> <span class='op'>=~</span> <span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>^([0-9]+)-([0-9]+)$</span><span class='regexp_end'>/</span></span>
      <span class='id identifier rubyid_msb'>msb</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_lsb'>lsb</span> <span class='op'>=</span> <span class='op'>::</span><span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>2</span><span class='rparen'>)</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>range must be specified &#39;msb-lsb&#39;</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_msb'>msb</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span> <span class='op'>&gt;=</span> <span class='id identifier rubyid_lsb'>lsb</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span>

      <span class='ivar'>@encoding_fields</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_lsb'>lsb</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='op'>..</span><span class='id identifier rubyid_msb'>msb</span><span class='period'>.</span><span class='id identifier rubyid_to_i'>to_i</span><span class='rparen'>)</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>location format error</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="grouped_encoding_fields-instance_method">
  
    #<strong>grouped_encoding_fields</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>array of constituent encoding fields</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


119
120
121
122
123
124
125
126
127
128
129
130
131
132
133
134
135
136
137
138
139
140
141</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 119</span>

<span class='kw'>def</span> <span class='id identifier rubyid_grouped_encoding_fields'>grouped_encoding_fields</span>
  <span class='id identifier rubyid_sorted_encoding_fields'>sorted_encoding_fields</span> <span class='op'>=</span> <span class='ivar'>@encoding_fields</span><span class='period'>.</span><span class='id identifier rubyid_sort'>sort</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='comma'>,</span> <span class='id identifier rubyid_b'>b</span><span class='op'>|</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='op'>&lt;=&gt;</span> <span class='id identifier rubyid_a'>a</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='rbrace'>}</span>
  <span class='comment'># need to group encoding_fields if they are consecutive
</span>  <span class='id identifier rubyid_grouped_fields'>grouped_fields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='id identifier rubyid_sorted_encoding_fields'>sorted_encoding_fields</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_sorted_encoding_fields'>sorted_encoding_fields</span><span class='lbracket'>[</span><span class='int'>1</span><span class='op'>..</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ef'>ef</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='lparen'>(</span><span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='op'>+</span> <span class='int'>1</span><span class='rparen'>)</span> <span class='op'>==</span> <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span><span class='period'>.</span><span class='id identifier rubyid_first'>first</span>
      <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='lbracket'>[</span><span class='op'>-</span><span class='int'>1</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='lparen'>(</span><span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_first'>first</span><span class='op'>..</span><span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span><span class='rparen'>)</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_grouped_fields'>grouped_fields</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='id identifier rubyid_size'>size</span>
      <span class='lbracket'>[</span><span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rbracket'>]</span>
    <span class='kw'>else</span>
      <span class='lbracket'>[</span><span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_inst_range_to_var_range'>inst_range_to_var_range</span><span class='lparen'>(</span><span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rbracket'>]</span>
    <span class='kw'>end</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_grouped_fields'>grouped_fields</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_f'>f</span><span class='op'>|</span>
      <span class='const'><span class='object_link'><a href="EncodingField.html" title="EncodingField (class)">EncodingField</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="EncodingField.html#initialize-instance_method" title="EncodingField#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_inst_range_to_var_range'>inst_range_to_var_range</span><span class='lparen'>(</span><span class='id identifier rubyid_f'>f</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_f'>f</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="hash-instance_method">
  
    #<strong>hash</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


162
163
164</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 162</span>

<span class='kw'>def</span> <span class='id identifier rubyid_hash'>hash</span>
  <span class='ivar'>@name</span><span class='period'>.</span><span class='id identifier rubyid_hash'>hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="inst_pos_to_var_pos-instance_method">
  
    #<strong>inst_pos_to_var_pos</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


84
85
86
87
88
89
90
91
92
93
94
95
96</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 84</span>

<span class='kw'>def</span> <span class='id identifier rubyid_inst_pos_to_var_pos'>inst_pos_to_var_pos</span>
  <span class='id identifier rubyid_s'>s</span> <span class='op'>=</span> <span class='id identifier rubyid_size'>size</span>
  <span class='id identifier rubyid_map'>map</span> <span class='op'>=</span> <span class='const'>Array</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='int'>32</span><span class='comma'>,</span> <span class='kw'>nil</span><span class='rparen'>)</span>
  <span class='ivar'>@encoding_fields</span><span class='period'>.</span><span class='id identifier rubyid_sort'>sort</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='comma'>,</span> <span class='id identifier rubyid_b'>b</span><span class='op'>|</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='op'>&lt;=&gt;</span> <span class='id identifier rubyid_a'>a</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ef'>ef</span><span class='op'>|</span>
    <span class='id identifier rubyid_ef'>ef</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_to_a'>to_a</span><span class='period'>.</span><span class='id identifier rubyid_reverse'>reverse</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ef_i'>ef_i</span><span class='op'>|</span>
      <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_s'>s</span> <span class='op'>&lt;=</span> <span class='int'>0</span>

      <span class='id identifier rubyid_map'>map</span><span class='lbracket'>[</span><span class='id identifier rubyid_ef_i'>ef_i</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_s'>s</span> <span class='op'>-</span> <span class='int'>1</span>
      <span class='id identifier rubyid_s'>s</span> <span class='op'>-=</span> <span class='int'>1</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_map'>map</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="sext?-instance_method">
  
    #<strong>sext?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>true if the field should be sign extended</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


194
195
196</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 194</span>

<span class='kw'>def</span> <span class='id identifier rubyid_sext?'>sext?</span>
  <span class='ivar'>@sext</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="size-instance_method">
  
    #<strong>size</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the number of bits in the field, _including any implicit ones_</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


184
185
186</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 184</span>

<span class='kw'>def</span> <span class='id identifier rubyid_size'>size</span>
  <span class='id identifier rubyid_size_in_encoding'>size_in_encoding</span> <span class='op'>+</span> <span class='ivar'>@left_shift</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="size_in_encoding-instance_method">
  
    #<strong>size_in_encoding</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>the number of bits in the field, _not including any implicit ones_</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


189
190
191</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 189</span>

<span class='kw'>def</span> <span class='id identifier rubyid_size_in_encoding'>size_in_encoding</span>
  <span class='id identifier rubyid_bits'>bits</span><span class='period'>.</span><span class='id identifier rubyid_reduce'>reduce</span><span class='lparen'>(</span><span class='int'>0</span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_sum'>sum</span><span class='comma'>,</span> <span class='id identifier rubyid_f'>f</span><span class='op'>|</span> <span class='id identifier rubyid_sum'>sum</span> <span class='op'>+</span> <span class='lparen'>(</span><span class='id identifier rubyid_f'>f</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Integer</span><span class='rparen'>)</span> <span class='op'>?</span> <span class='int'>1</span> <span class='op'>:</span> <span class='id identifier rubyid_f'>f</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="split?-instance_method">
  
    #<strong>split?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns true if the field is encoded across more than one groups of bits</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


167
168
169</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/opcodes.rb', line 167</span>

<span class='kw'>def</span> <span class='id identifier rubyid_split?'>split?</span>
  <span class='ivar'>@encoding_fields</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>&gt;</span> <span class='int'>1</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>