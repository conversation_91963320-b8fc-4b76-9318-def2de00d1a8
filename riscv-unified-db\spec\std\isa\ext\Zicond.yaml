# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zicond
long_name: Integer Conditional Operations
description: |
  The "Conditional" operations extension provides a simple solution that provides most of the
  benefit and all of the flexibility one would desire to support conditional arithmetic and
  conditional-select/move operations, while remaining true to the RISC-V design philosophy.
  The instructions follow the format for R-type instructions with 3 operands (_i.e._, 2 source
  operands and 1 destination operand).

  Using these instructions, branchless sequences can be implemented (typically in two-instruction
  sequences) without the need for instruction fusion, special provisions during the decoding of
  architectural instructions, or other microarchitectural provisions.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
