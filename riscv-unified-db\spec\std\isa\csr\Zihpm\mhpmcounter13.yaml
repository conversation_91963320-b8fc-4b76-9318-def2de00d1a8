# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/mhpmcounterN.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mhpmcounter13
long_name: Machine Hardware Performance Counter 13
address: 0xB0D
priv_mode: M
length: 64
description: Programmable hardware performance counter.
definedBy: Smhpm
fields:
  COUNT:
    location: 63-0
    description: |
      [when="HPM_COUNTER_EN[13] == true"]
      --
      Performance counter for event selected in `mhpmevent13.EVENT`.

      Increments every time event occurs unless:

        * `mcountinhibit.HPM13` <%- if ext?(:Smcdeleg) -%>or its alias `scountinhibit.HPM13`<%- end -%> is set
        <%- if ext?(:Sscofpmf) -%>
        * `mhpmevent13.MINH` is set and the current privilege level is M
        <%- if ext?(:S) -%>
        * `mhpmevent13.SINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent13..SINH`<%- end -%> is set and the current privilege level is (H)S
        <%- end -%>
        <%- if ext?(:U) -%>
        * `mhpmevent13.UINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent13.SINH`<%- end -%> is set and the current privilege level is U
        <%- end -%>
        <%- if ext?(:H) -%>
        * `mhpmevent13.VSINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent13.SINH`<%- end -%> is set and the current privilege level is VS
        * `mhpmevent13.VUINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent13.SINH`<%- end -%> is set and the current privilege level is VU
        <%- end -%>
        <%- end -%>
      --

      [when="HPM_COUNTER_EN[13] == false"]
      Unimplemented performance counter. Must be read-only 0 (access does not cause trap).

    type(): "return (HPM_COUNTER_EN[13]) ? CsrFieldType::RWH : CsrFieldType::RO;"
    reset_value(): "return (HPM_COUNTER_EN[13]) ? UNDEFINED_LEGAL : 0;"
sw_read(): |
  if (HPM_COUNTER_EN[13]) {
    return read_hpm_counter(13);
  } else {
    return 0;
  }
