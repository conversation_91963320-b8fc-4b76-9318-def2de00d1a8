<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Treetop::Runtime::SyntaxNode
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Treetop::Runtime::SyntaxNode";
  relpath = '../../';
</script>


  <script type="text/javascript" charset="utf-8" src="../../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../../_index.html">Index (S)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../../Treetop.html" title="Treetop (module)">Treetop</a></span></span> &raquo; <span class='title'><span class='object_link'><a href="../Runtime.html" title="Treetop::Runtime (module)">Runtime</a></span></span>
     &raquo; 
    <span class="title">SyntaxNode</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Treetop::Runtime::SyntaxNode
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Treetop::Runtime::SyntaxNode</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb<span class="defines">,<br />
  lib/idl/passes/prune.rb,<br /> lib/idl/passes/find_return_values.rb</span>
</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>open up SyntaxNode to add utiilities that need to apply to all nodes (not just Asts)</p>


  </div>
</div>
<div class="tags">
  

</div><div id="subclasses">
  <h2>Direct Known Subclasses</h2>
  <p class="children"><span class='object_link'><a href="../../Idl/AryAccessSyntaxNode.html" title="Idl::AryAccessSyntaxNode (class)">Idl::AryAccessSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/AryElementAssignmentSyntaxNode.html" title="Idl::AryElementAssignmentSyntaxNode (class)">Idl::AryElementAssignmentSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/AstNode.html" title="Idl::AstNode (class)">Idl::AstNode</a></span>, <span class='object_link'><a href="../../Idl/BinaryExpressionRightSyntaxNode.html" title="Idl::BinaryExpressionRightSyntaxNode (class)">Idl::BinaryExpressionRightSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/BuiltinTypeNameSyntaxNode.html" title="Idl::BuiltinTypeNameSyntaxNode (class)">Idl::BuiltinTypeNameSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/BuiltinVariableSyntaxNode.html" title="Idl::BuiltinVariableSyntaxNode (class)">Idl::BuiltinVariableSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/ConditionalStatementSyntaxNode.html" title="Idl::ConditionalStatementSyntaxNode (class)">Idl::ConditionalStatementSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/DontCareReturnSyntaxNode.html" title="Idl::DontCareReturnSyntaxNode (class)">Idl::DontCareReturnSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/EnumRefSyntaxNode.html" title="Idl::EnumRefSyntaxNode (class)">Idl::EnumRefSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/ForLoopSyntaxNode.html" title="Idl::ForLoopSyntaxNode (class)">Idl::ForLoopSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/FunctionBodySyntaxNode.html" title="Idl::FunctionBodySyntaxNode (class)">Idl::FunctionBodySyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/FunctionCallExpressionSyntaxNode.html" title="Idl::FunctionCallExpressionSyntaxNode (class)">Idl::FunctionCallExpressionSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/IdSyntaxNode.html" title="Idl::IdSyntaxNode (class)">Idl::IdSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/IfSyntaxNode.html" title="Idl::IfSyntaxNode (class)">Idl::IfSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/InstructionOperationSyntaxNode.html" title="Idl::InstructionOperationSyntaxNode (class)">Idl::InstructionOperationSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/ParenExpressionSyntaxNode.html" title="Idl::ParenExpressionSyntaxNode (class)">Idl::ParenExpressionSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/ReplicationExpressionSyntaxNode.html" title="Idl::ReplicationExpressionSyntaxNode (class)">Idl::ReplicationExpressionSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/SignCastSyntaxNode.html" title="Idl::SignCastSyntaxNode (class)">Idl::SignCastSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/TernaryOperatorExpressionSyntaxNode.html" title="Idl::TernaryOperatorExpressionSyntaxNode (class)">Idl::TernaryOperatorExpressionSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/VariableAssignmentSyntaxNode.html" title="Idl::VariableAssignmentSyntaxNode (class)">Idl::VariableAssignmentSyntaxNode</a></span>, <span class='object_link'><a href="../../Idl/VariableDeclarationWithInitializationSyntaxNode.html" title="Idl::VariableDeclarationWithInitializationSyntaxNode (class)">Idl::VariableDeclarationWithInitializationSyntaxNode</a></span></p>
</div>




  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#input_file-instance_method" title="#input_file (instance method)">#<strong>input_file</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute input_file.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#clone-instance_method" title="#clone (instance method)">#<strong>clone</strong>  &#x21d2; SyntaxNode </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A deep clone of the node.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#find_ancestor-instance_method" title="#find_ancestor (instance method)">#<strong>find_ancestor</strong>(klass)  &#x21d2; SyntaxNode<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The first ancestor that is_a?(klass), or nil if none is found.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#has_template_ancestor%3F-instance_method" title="#has_template_ancestor? (instance method)">#<strong>has_template_ancestor?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this node is part of a template function.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#is_function_name%3F-instance_method" title="#is_function_name? (instance method)">#<strong>is_function_name?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this SyntaxNode represents a function name (overriden in the parser).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#lineno-instance_method" title="#lineno (instance method)">#<strong>lineno</strong>  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The current line number.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#pass_find_return_values-instance_method" title="#pass_find_return_values (instance method)">#<strong>pass_find_return_values</strong>(values, current_conditions)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#prune-instance_method" title="#prune (instance method)">#<strong>prune</strong>(symtab)  &#x21d2; AstNode </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new abstract syntax tree with all dead/unreachable code removed.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#set_input_file-instance_method" title="#set_input_file (instance method)">#<strong>set_input_file</strong>(filename, starting_line = 0)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>remember where the code for this SyntaxNode comes from.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_ast-instance_method" title="#to_ast (instance method)">#<strong>to_ast</strong>  &#x21d2; SyntaxNode </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>convert SyntaxNode into an AstNode.</p>
</div></span>
  
</li>

      
    </ul>
  


  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="input_file-instance_method">
  
    #<strong>input_file</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute input_file.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


12
13
14</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 12</span>

<span class='kw'>def</span> <span class='id identifier rubyid_input_file'>input_file</span>
  <span class='ivar'>@input_file</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="clone-instance_method">
  
    #<strong>clone</strong>  &#x21d2; <tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns A deep clone of the node.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A deep clone of the node</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 78</span>

<span class='kw'>def</span> <span class='id identifier rubyid_clone'>clone</span>
  <span class='id identifier rubyid_new_elements'>new_elements</span> <span class='op'>=</span> <span class='kw'>nil</span>
  <span class='kw'>unless</span> <span class='id identifier rubyid_terminal?'>terminal?</span>
    <span class='id identifier rubyid_new_elements'>new_elements</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_child'>child</span><span class='op'>|</span>
      <span class='id identifier rubyid_new_elements'>new_elements</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_child'>child</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_new_node'>new_node</span> <span class='op'>=</span> <span class='kw'>super</span>
  <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_instance_exec'>instance_exec</span> <span class='kw'>do</span>
    <span class='ivar'>@input</span> <span class='op'>=</span> <span class='id identifier rubyid_input'>input</span>
    <span class='ivar'>@interval</span> <span class='op'>=</span> <span class='id identifier rubyid_interval'>interval</span>
    <span class='ivar'>@elements</span> <span class='op'>=</span> <span class='id identifier rubyid_new_elements'>new_elements</span>
    <span class='ivar'>@comprehensive_elements</span> <span class='op'>=</span> <span class='kw'>nil</span>
  <span class='kw'>end</span>
  <span class='kw'>unless</span> <span class='id identifier rubyid_terminal?'>terminal?</span>
    <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_child'>child</span><span class='op'>|</span>
      <span class='id identifier rubyid_child'>child</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='id identifier rubyid_new_node'>new_node</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_new_node'>new_node</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="find_ancestor-instance_method">
  
    #<strong>find_ancestor</strong>(klass)  &#x21d2; <tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the first ancestor that is_a?(klass), or nil if none is found.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the first ancestor that is_a?(klass), or nil if none is found</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


67
68
69
70
71
72
73
74
75</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 67</span>

<span class='kw'>def</span> <span class='id identifier rubyid_find_ancestor'>find_ancestor</span><span class='lparen'>(</span><span class='id identifier rubyid_klass'>klass</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='kw'>nil</span>
  <span class='kw'>elsif</span> <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='id identifier rubyid_klass'>klass</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_parent'>parent</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_find_ancestor'>find_ancestor</span><span class='lparen'>(</span><span class='id identifier rubyid_klass'>klass</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="has_template_ancestor?-instance_method">
  
    #<strong>has_template_ancestor?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this node is part of a template function.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this node is part of a template function</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


15
16
17
18
19
20
21
22
23
24
25</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 15</span>

<span class='kw'>def</span> <span class='id identifier rubyid_has_template_ancestor?'>has_template_ancestor?</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='kw'>nil</span>
  <span class='kw'>elsif</span> <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>BitTypeAst</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>FunctionCallTemplateArguments</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_parent_expression'>parent_expression</span><span class='period'>.</span><span class='id identifier rubyid_node_class_name'>node_class_name</span><span class='rparen'>)</span>
    <span class='kw'>true</span>
  <span class='kw'>elsif</span> <span class='id identifier rubyid_parent_expression'>parent_expression</span><span class='period'>.</span><span class='id identifier rubyid_node_class_name'>node_class_name</span> <span class='op'>==</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ParenExpressionAst</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>false</span> <span class='comment'># parens isolate the &gt;, so we can allow at this point
</span>  <span class='kw'>else</span>
    <span class='id identifier rubyid_parent'>parent</span><span class='period'>.</span><span class='id identifier rubyid_has_template_ancestor?'>has_template_ancestor?</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="is_function_name?-instance_method">
  
    #<strong>is_function_name?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this SyntaxNode represents a function name (overriden in the parser).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this SyntaxNode represents a function name (overriden in the parser)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


28</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 28</span>

<span class='kw'>def</span> <span class='id identifier rubyid_is_function_name?'>is_function_name?</span> <span class='op'>=</span> <span class='kw'>false</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="lineno-instance_method">
  
    #<strong>lineno</strong>  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the current line number.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the current line number</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


62
63
64</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 62</span>

<span class='kw'>def</span> <span class='id identifier rubyid_lineno'>lineno</span>
  <span class='id identifier rubyid_input'>input</span><span class='lbracket'>[</span><span class='int'>0</span><span class='op'>..</span><span class='id identifier rubyid_interval'>interval</span><span class='period'>.</span><span class='id identifier rubyid_first'>first</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_count'>count</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>\n</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>+</span> <span class='int'>1</span> <span class='op'>+</span> <span class='lparen'>(</span><span class='ivar'>@starting_line</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='int'>0</span> <span class='op'>:</span> <span class='ivar'>@starting_line</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="pass_find_return_values-instance_method">
  
    #<strong>pass_find_return_values</strong>(values, current_conditions)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


8
9
10
11
12</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/find_return_values.rb', line 8</span>

<span class='kw'>def</span> <span class='id identifier rubyid_pass_find_return_values'>pass_find_return_values</span><span class='lparen'>(</span><span class='id identifier rubyid_values'>values</span><span class='comma'>,</span> <span class='id identifier rubyid_current_conditions'>current_conditions</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_children'>children</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_c'>c</span><span class='op'>|</span>
    <span class='id identifier rubyid_c'>c</span><span class='period'>.</span><span class='id identifier rubyid_pass_find_return_values'>pass_find_return_values</span><span class='lparen'>(</span><span class='id identifier rubyid_values'>values</span><span class='comma'>,</span> <span class='id identifier rubyid_current_conditions'>current_conditions</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="prune-instance_method">
  
    #<strong>prune</strong>(symtab)  &#x21d2; <tt>AstNode</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns A new abstract syntax tree with all dead/unreachable code removed.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="../../Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Context of the compilation</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>AstNode</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A new abstract syntax tree with all dead/unreachable code removed</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/prune.rb', line 11</span>

<span class='kw'>def</span> <span class='id identifier rubyid_prune'>prune</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_new_elements'>new_elements</span> <span class='op'>=</span> <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>?</span> <span class='lbracket'>[</span><span class='rbracket'>]</span> <span class='op'>:</span> <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_prune'>prune</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
  <span class='id identifier rubyid_new_node'>new_node</span> <span class='op'>=</span> <span class='id identifier rubyid_clone'>clone</span> <span class='comment'># self.class.new(input, interval, new_elements)
</span>  <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_clear'>clear</span>
  <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_concat'>concat</span><span class='lparen'>(</span><span class='id identifier rubyid_new_elements'>new_elements</span><span class='rparen'>)</span>

  <span class='comment'># extension_modules.each do |m|
</span>    <span class='comment'># new_node.extend m
</span>  <span class='comment'># end
</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="../../Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="../../Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='rparen'>)</span>
    <span class='kw'>begin</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="../../Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="../../Idl/Declaration.html" title="Idl::Declaration (module)">Declaration</a></span></span><span class='rparen'>)</span>
        <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_add_symbol'>add_symbol</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="../../Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="../../Idl/Executable.html" title="Idl::Executable (module)">Executable</a></span></span><span class='rparen'>)</span>
        <span class='id identifier rubyid_new_node'>new_node</span><span class='period'>.</span><span class='id identifier rubyid_execute'>execute</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span> <span class='comment'># to update values
</span>      <span class='kw'>end</span>
    <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="../../Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="../../Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="../../Idl/AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span>
      <span class='comment'># new_node.type_check(symtab)
</span>    <span class='kw'>end</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_new_node'>new_node</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="set_input_file-instance_method">
  
    #<strong>set_input_file</strong>(filename, starting_line = 0)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>remember where the code for this SyntaxNode comes from</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>filename</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Filename</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>starting_line</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>0</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Starting line in the file</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


52
53
54
55
56
57
58
59</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 52</span>

<span class='kw'>def</span> <span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_filename'>filename</span><span class='comma'>,</span> <span class='id identifier rubyid_starting_line'>starting_line</span> <span class='op'>=</span> <span class='int'>0</span><span class='rparen'>)</span>
  <span class='ivar'>@input_file</span> <span class='op'>=</span> <span class='id identifier rubyid_filename'>filename</span>
  <span class='ivar'>@starting_line</span> <span class='op'>=</span> <span class='id identifier rubyid_starting_line'>starting_line</span>
  <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='period'>.</span><span class='id identifier rubyid_times'>times</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_i'>i</span><span class='op'>|</span>
    <span class='id identifier rubyid_elements'>elements</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_set_input_file'>set_input_file</span><span class='lparen'>(</span><span class='id identifier rubyid_filename'>filename</span><span class='comma'>,</span> <span class='id identifier rubyid_starting_line'>starting_line</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>?</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='ivar'>@starting_line</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_ast-instance_method">
  
    #<strong>to_ast</strong>  &#x21d2; <tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
  <div class="note notetag">
    <strong>Note:</strong>
    <div class='inline'>
<p>This may alter the SyntaxTree. You shouldn’t use pointers within the tree from before a call to to_ast</p>
</div>
  </div>


<p>convert SyntaxNode into an AstNode</p>

<p>Mostly, there is a 1:1 correspondence between SyntaxNode and AstNode. A few exceptions:</p>

<pre class="code ruby"><code class="ruby">* Left recusrion needs fixed up, so BinaryExpreesions are converted
* If statements are converted to a more friendly format
</code></pre>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="" title="Treetop::Runtime::SyntaxNode (class)">SyntaxNode</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A fixed syntax tree</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


40
41
42
43
44
45
46</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 40</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_ast'>to_ast</span>
  <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_length'>length</span><span class='period'>.</span><span class='id identifier rubyid_times'>times</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_i'>i</span><span class='op'>|</span>
    <span class='id identifier rubyid_elements'>elements</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_elements'>elements</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
    <span class='id identifier rubyid_elements'>elements</span><span class='lbracket'>[</span><span class='id identifier rubyid_i'>i</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='kw'>self</span>
  <span class='kw'>end</span>
  <span class='kw'>self</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>