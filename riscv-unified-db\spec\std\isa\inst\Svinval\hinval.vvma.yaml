# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: hinval.vvma
long_name: Invalidate cached address translations
definedBy:
  allOf:
    - Svinval
    - H
encoding:
  match: 0010011----------000000001110011
  variables:
    - name: xs2
      location: 24-20
    - name: xs1
      location: 19-15
description: |
  `hinval.vvma` has the same semantics as `sinval.vma` except that it combines with
  `sfence.w.inval` and `sfence.inval.ir` to replace `hfence.vvma`.
access:
  s: always
  u: never
  vs: never
  vu: never
assembly: xs1, xs2
operation(): |
  XReg vaddr = X[xs1];
  Bits<ASID_WIDTH> asid = X[xs2][ASID_WIDTH-1:0];
  Bits<VMID_WIDTH> vmid = CSR[hgatp].VMID;

  if (mode() == PrivilegeMode::U) {
    raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  if ((CSR[misa].H == 1) &&
      (mode() == PrivilegeMode::VS || mode() == PrivilegeMode::VU)) {
    raise (ExceptionCode::VirtualInstruction, mode(), $encoding);
  }

  # note: this will default to "all"
  VmaOrderType vma_type;
  vma_type.vsmode = true;
  vma_type.single_vmid = true;
  vma_type.vmid = vmid;

  if ((xs1 == 0) && (xs2 == 0)) {
    # invalidate all translations, from all addresses and all ASIDs
    # includes global mappings
    vma_type.global = true;

    invalidate_translations(vma_type);

  } else if ((xs1 == 0) && (xs2 != 0)) {
    # invalidates all translations from ASID 'asid'
    # does not affect global mappings
    vma_type.single_asid = true;
    vma_type.asid = asid;

    invalidate_translations(vma_type);

  } else if ((xs1 != 0) && (xs2 == 0)) {
    # invalidate all translations from leaf page tables containing 'vaddr'
    # does not affect global mappings
    if (canonical_vaddr?(vaddr)) {
      vma_type.single_vaddr = true;
      vma_type.vaddr = vaddr;

      invalidate_translations(vma_type);

    }
    # else, silently do nothing

  } else {
    # invalidate all translations from leaf page tables for address space 'asid' containing 'vaddr'
    # does not affect global mappings
    if (canonical_vaddr?(vaddr)) {
      vma_type.single_asid = true;
      vma_type.asid = asid;
      vma_type.single_vaddr = true;
      vma_type.vaddr = vaddr;

      invalidate_translations(vma_type);

    }
    # else, silently do nothing
  }
