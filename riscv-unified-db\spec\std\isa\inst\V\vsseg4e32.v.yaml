# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: vsseg4e32.v
long_name: No synopsis available
description: |
  No description available.
definedBy: V
assembly: vs3, (xs1), vm
encoding:
  match: 011000-00000-----110-----0100111
  variables:
    - name: vm
      location: 25-25
    - name: xs1
      location: 19-15
    - name: vs3
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
