# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: A
type: unprivileged
long_name: Atomic instructions
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "2.1.0"
    state: ratified
    ratification_date: 2019-12
    contributors:
      - name: Unknown
        email: <EMAIL>
        company: Unknown
    implies:
      - name: Zaamo
        version: "1.0.0"
      - name: Zalrsc
        version: "1.0.0"
description: |

  The atomic-instruction extension, named `A`, contains
  instructions that atomically read-modify-write memory to support
  synchronization between multiple RISC-V harts running in the same memory
  space. The two forms of atomic instruction provided are
  load-reserved/store-conditional instructions and atomic fetch-and-op
  memory instructions. Both types of atomic instruction support various
  memory consistency orderings including unordered, acquire, release, and
  sequentially consistent semantics. These instructions allow RISC-V to
  support the RCsc memory consistency model. cite:[Gharachorloo90memoryconsistency]

  [NOTE]
  ====
  After much debate, the language community and architecture community
  appear to have finally settled on release consistency as the standard
  memory consistency model and so the RISC-V atomic support is built
  around this model.
  ====

  The `A` extension comprises instructions provided by the `Zaamo` and `Zalrsc`
  extensions.

  = Specifying Ordering of Atomic Instructions

  The base RISC-V ISA has a relaxed memory model, with the `FENCE`
  instruction used to impose additional ordering constraints. The address
  space is divided by the execution environment into memory and I/O
  domains, and the `FENCE` instruction provides options to order accesses to
  one or both of these two address domains.

  To provide more efficient support for release consistency cite:[Gharachorloo90memoryconsistency], each atomic
  instruction has two bits, _aq_ and _rl_, used to specify additional
  memory ordering constraints as viewed by other RISC-V harts. The bits
  order accesses to one of the two address domains, memory or I/O,
  depending on which address domain the atomic instruction is accessing.
  No ordering constraint is implied to accesses to the other domain, and a
  FENCE instruction should be used to order across both domains.

  If both bits are clear, no additional ordering constraints are imposed
  on the atomic memory operation. If only the _aq_ bit is set, the atomic
  memory operation is treated as an _acquire_ access, i.e., no following
  memory operations on this RISC-V hart can be observed to take place
  before the acquire memory operation. If only the _rl_ bit is set, the
  atomic memory operation is treated as a _release_ access, i.e., the
  release memory operation cannot be observed to take place before any
  earlier memory operations on this RISC-V hart. If both the _aq_ and _rl_
  bits are set, the atomic memory operation is _sequentially consistent_
  and cannot be observed to happen before any earlier memory operations or
  after any later memory operations in the same RISC-V hart and to the
  same address domain.
params:
  MISALIGNED_AMO:
    description: |
      whether or not the implementation supports misaligned atomics in main memory
    schema:
      type: boolean
  LRSC_RESERVATION_STRATEGY:
    description: |
      Strategy used to handle reservation sets.

        * "reserve naturally-aligned 64-byte region": Always reserve the 64-byte block containing the LR/SC address
        * "reserve naturally-aligned 128-byte region": Always reserve the 128-byte block containing the LR/SC address
        * "reserve exactly enough to cover the access": Always reserve exactly the LR/SC access, and no more
        * "custom": Custom behavior, leading to an 'unpredictable' call on any LR/SC
    schema:
      type: string
      enum:
        - reserve naturally-aligned 64-byte region
        - reserve naturally-aligned 128-byte region
        - reserve exactly enough to cover the access
        - custom
  LRSC_FAIL_ON_VA_SYNONYM:
    description: |
      Whether or not an `sc.l`/`sc.d` will fail if its VA does not match the VA of the prior
      `lr.l`/`lr.d`, even if the physical address of the SC and LR are the same
    schema:
      type: boolean
  LRSC_MISALIGNED_BEHAVIOR:
    description: |
      What to do when an LR/SC address is misaligned and MISALIGNED_AMO == false.

        * 'always raise misaligned exception': self-explainitory
        * 'always raise access fault': self-explainitory
        * 'custom': Custom behavior; misaligned LR/SC may sometimes raise a misaligned exception and sometimes raise a access fault. Will lead to an 'unpredictable' call on any misaligned LR/SC access
    schema:
      type: string
      enum:
        - always raise misaligned exception
        - always raise access fault
        - custom
  LRSC_FAIL_ON_NON_EXACT_LRSC:
    description: |
      Whether or not a Store Conditional fails if its physical address and size do not
      exactly match the physical address and size of the last Load Reserved in program order
      (independent of whether or not the SC is in the current reservation set)
    schema:
      type: boolean
  MUTABLE_MISA_A:
    description: |
      When the `A` extensions is supported, indicates whether or not
      the extension can be disabled in the `misa.A` bit.
    schema:
      type: boolean
