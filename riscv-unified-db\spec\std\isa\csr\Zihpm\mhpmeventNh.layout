# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

<%- raise "'hpm_num' must be defined" if hpm_num.nil? -%>

$schema: csr_schema.json#
kind: csr
name: <%= "mhpmevent#{hpm_num}h" %>
long_name: Machine Hardware Performance Counter <%= hpm_num %> Control, High half
address: 0x<%= (0x720 + hpm_num).to_s(16).upcase %>
priv_mode: M
length: 32
base: 32
description: |
  Alias of `mhpmevent<%= hpm_num %>`[63:32].

  Introduced with the `Sscofpmf` extension. Prior to that, there was no way to access the upper
  32-bits of `mhpmevent#{hpm_num}`.
definedBy: Sscofpmf
fields:
  OF:
    location: 31
    alias: mhpmevent<%= hpm_num %>.OF
    description: |
      Alias of mhpmevent<%= hpm_num %>.OF.
    type(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return CsrFieldType::RWH;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  MINH:
    location: 30
    alias: mhpmevent<%= hpm_num %>.MINH
    description: |
      Alias of mhpmevent<%= hpm_num %>.MINH.
    type(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  SINH:
    location: 29
    alias: mhpmevent<%= hpm_num %>.SINH
    description: |
      Alias of mhpmevent<%= hpm_num %>.SINH.
    type(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::S) && (CSR[misa].S == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::S)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  UINH:
    location: 28
    alias: mhpmevent<%= hpm_num %>.UINH
    description: |
      Alias of mhpmevent<%= hpm_num %>.UINH.
    type(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::U) && (CSR[misa].U == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::U)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  VSINH:
    location: 27
    alias: mhpmevent<%= hpm_num %>.VSINH
    description: |
      Alias of mhpmevent<%= hpm_num %>.VSINH.
    type(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::H) && (CSR[misa].H == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::H)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  VUINH:
    location: 26
    alias: mhpmevent<%= hpm_num %>.VUINH
    description: |
      Alias of mhpmevent<%= hpm_num %>.VUINH.
    type(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::H) && (CSR[misa].H == 1'b1)) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if ((HPM_COUNTER_EN[<%= hpm_num %>]) && implemented?(ExtensionName::H)) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    definedBy: Sscofpmf
  EVENT:
    location: 25-0
    description: High part of event selector for performance counter `mhpmcounter<%= hpm_num %>`.
    alias: mhpmevent<%= hpm_num %>.EVENT[57:32]
    type(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (HPM_COUNTER_EN[<%= hpm_num %>]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
