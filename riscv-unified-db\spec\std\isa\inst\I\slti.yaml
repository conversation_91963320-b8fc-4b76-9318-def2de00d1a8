# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: slti
long_name: Set on less than immediate
description: |
  Places the value 1 in register `xd` if register `xs1` is less than the sign-extended immediate
  when both are treated as signed numbers, else 0 is written to `xd`.
definedBy: I
assembly: xd, xs1, imm
encoding:
  match: -----------------010-----0010011
  variables:
    - name: imm
      location: 31-20
    - name: xs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |
  X[xd] = ($signed(X[xs1]) < $signed(imm)) ? '1 : '0;

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let xs1_val = X(xs1);
    let immext : xlenbits = sign_extend(imm);
    let result : xlenbits = match op {
      RISCV_ADDI  => xs1_val + immext,
      RISCV_SLTI  => zero_extend(bool_to_bits(xs1_val <_s immext)),
      RISCV_SLTIU => zero_extend(bool_to_bits(xs1_val <_u immext)),
      RISCV_ANDI  => xs1_val & immext,
      RISCV_ORI   => xs1_val | immext,
      RISCV_XORI  => xs1_val ^ immext
    };
    X(xd) = result;
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
