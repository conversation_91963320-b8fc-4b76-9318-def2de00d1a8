# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zkne
long_name: "NIST Suite: AES Encryption"
description: |
  Instructions for accelerating the encryption and key-schedule functions of the AES block cipher.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
