# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: ori
long_name: Or immediate
description: Or an immediate to the value in xs1, and store the result in xd
definedBy: I
assembly: xd, xs1, imm
encoding:
  match: -----------------110-----0010011
  variables:
    - name: imm
      location: 31-20
    - name: xs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |
  if (implemented?(ExtensionName::Zicbop)) {
    if (xd == 0) {
      if (imm[4:0] == 0) {
        # prefetch.i instruction
        Bits<12> offset = {imm[11:5], xd};
        prefetch_instruction(offset);
      } else if (imm[4:0] == 1) {
        # prefetch.r instruction
        Bits<12> offset = {imm[11:5], xd};
        prefetch_read(offset);
      } else if (imm[4:0] == 3) {
        # prefetch.r instruction
        Bits<12> offset = {imm[11:5], xd};
        prefetch_write(offset);
      }
    }
  }
  X[xd] = X[xs1] | $signed(imm);
pseudoinstructions:
  - when: (xd == 0) && (imm[4:0] == 0)
    to: prefetch.i offset
  - when: (xd == 0) && (imm[4:0] == 1)
    to: prefetch.r offset
  - when: (xd == 0) && (imm[4:0] == 3)
    to: prefetch.w offset

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let xs1_val = X(xs1);
    let immext : xlenbits = sign_extend(imm);
    let result : xlenbits = match op {
      RISCV_ADDI  => xs1_val + immext,
      RISCV_SLTI  => zero_extend(bool_to_bits(xs1_val <_s immext)),
      RISCV_SLTIU => zero_extend(bool_to_bits(xs1_val <_u immext)),
      RISCV_ANDI  => xs1_val & immext,
      RISCV_ORI   => xs1_val | immext,
      RISCV_XORI  => xs1_val ^ immext
    };
    X(xd) = result;
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
