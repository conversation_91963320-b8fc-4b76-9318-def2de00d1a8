# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqcisync
type: unprivileged
long_name: Qualcomm non-memory-mapped devices synchronization and delay
versions:
- version: "0.1.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON> Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix decoding of qc.c.delay instruction (state that immediate cannot be 0)
    - Add requirement to include Zca extension since has 16-bit instructions
  requires: { name: <PERSON><PERSON>, version: ">= 1.0.0" }
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>sher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON> Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code for qc.c.sync, qc.c.syncr, qc.c.syncwf and qc.c.syncwl instructions because change in IDL '<<' operator
  requires: { name: Zca, version: ">= 1.0.0" }
description: |
  The Xqcisync extension includes nine instructions, eight for non-memory-mapped devices synchronization and delay instruction.
  Synchronization instructions are kind of IO fences that work with special devices synchronization signals.

doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
