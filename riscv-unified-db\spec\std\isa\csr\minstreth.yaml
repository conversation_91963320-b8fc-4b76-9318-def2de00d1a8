# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: minstreth
long_name: Machine Instructions Retired Counter
address: 0xB82
writable: true
description: |
  Upper half of 64-bit instructions retired counters.

  See `minstret` for details.
priv_mode: M
length: 32
base: 32
fields:
  COUNT:
    alias:
      - minstret.COUNT[63:32]
    location: 31-0
    type: RW-H
    description: |
      *Instructions retired counter*

      Upper half of `minstret`.
    reset_value: UNDEFINED_LEGAL
    affectedBy: [Zic<PERSON>r, Smcntrpmf, Smcdeleg, Ssccfg]
    sw_write(csr_value): |
      CSR[mcycle].COUNT = {csr_value.COUNT[31:0], CSR[minstret].COUNT[31:0]};
      return csr_value.COUNT;
definedBy: Zicntr
sw_read(): |
  return CSR[minstret].COUNT[63:32];
