# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hstateen0h
long_name: Upper 32 bits of Hypervisor State Enable 0 Register
address: 0x61C
priv_mode: S
length: 32
base: 32
description:
  - id: csr-hstateen0h-purpose
    normative: true
    text: |
      For RV64 harts, the Smstateen/Ssstateen extension adds four new 64-bit CSRs at machine level: `mstateen0` (Machine State Enable 0),
      `mstateen1`, `mstateen2`, and `mstateen3`. If supervisor mode is implemented, another four CSRs are defined at
      supervisor level: `sstateen0`, `sstateen1`, `sstateen2`, and `sstateen3`. And if the hypervisor extension is implemented,
      another set of CSRs is added: `hstateen0`, `hstateen1`, `hstateen2`, and `hstateen3`.

      For RV32, the registers listed above are 32-bit, and for the machine-level and hypervisor CSRs there is a corresponding
      set of high-half CSRs for the upper 32 bits of each register: `mstateen0h`, `mstateen1h`, `mstateen2h`, `mstateen3h`, `hstateen0h`,
      `hstateen1h`, `hstateen2h`, and `hstateen3h`.

definedBy:
  allOf:
    - H
    - Smstateen
    - Ssstateen
fields:
  SE0:
    long_name: sstateen0 access control
    location: 31
    alias: hstateen0.SE0
    sw_write(csr_value): |
      if (CSR[mstateen0].SE0 == 1'b0){
        return 0;
      }
      CSR[hstateen0].SE0 = csr_value.SE0;
      return csr_value.SE0;
    description: |
      The SE0 bit in `hstateen0h` controls access to the `sstateen0` CSR.
    type: RW
    reset_value: UNDEFINED_LEGAL
  ENVCFG:
    long_name: senvcfg access control
    location: 30
    definedBy:
      name: S
      version: ">= 1.11"
    alias: hstateen0.ENVCFG
    sw_write(csr_value): |
      if (CSR[mstateen0].ENVCFG == 1'b0){
        return 0;
      }
      CSR[hstateen0].ENVCFG = csr_value.ENVCFG;
      return csr_value.ENVCFG;
    description: |
      The ENVCFG bit in `hstateen0h` controls access to the `senvcfg` CSRs.
    type: RW
    reset_value: UNDEFINED_LEGAL
  CSRIND:
    long_name: siselect and sireg* access control
    location: 28
    definedBy: Sscsrind
    alias: hstateen0.CSRIND
    sw_write(csr_value): |
      if (CSR[mstateen0].CSRIND == 1'b0){
        return 0;
      }
      CSR[hstateen0].CSRIND = csr_value.CSRIND;
      return csr_value.CSRIND;
    description: |
      The CSRIND bit in `hstateen0h` controls access to the `siselect` and the
      `sireg*`, (really `vsiselect` and `vsireg*`) CSRs provided by the Sscsrind
      extensions.
    type: RW
    reset_value: UNDEFINED_LEGAL
  AIA:
    long_name: Ssaia state access control
    location: 27
    definedBy: Ssaia
    alias: hstateen0.AIA
    sw_write(csr_value): |
      if (CSR[mstateen0].AIA == 1'b0){
        return 0;
      }
      CSR[hstateen0].AIA = csr_value.AIA;
      return csr_value.AIA;
    description: |
      The AIA bit in `hstateen0h` controls access to all state introduced by
      the Ssaia extension and is not controlled by either the CSRIND or the
      IMSIC bits of `hstateen0`.
    type: RW
    reset_value: UNDEFINED_LEGAL
  IMSIC:
    long_name: IMSIC state access control
    location: 26
    definedBy: Ssaia
    alias: hstateen0.IMSIC
    sw_write(csr_value): |
      if (CSR[mstateen0].IMSIC == 1'b0){
        return 0;
      }
      CSR[hstateen0].IMSIC = csr_value.IMSIC;
      return csr_value.IMSIC;
    description: |
      The IMSIC bit in `hstateen0h` controls access to the guest IMSIC state,
      including CSRs `stopei` (really `vstopei`), provided by the Ssaia extension.

      Setting the IMSIC bit in `hstateen0h` to zero prevents a virtual machine
      from accessing the hart’s IMSIC the same as setting `hstatus.`VGEIN = 0.
    type: RW
    reset_value: UNDEFINED_LEGAL
  CONTEXT:
    long_name: scontext access control
    location: 25
    definedBy: Sdtrig
    alias: hstateen0.CONTEXT
    sw_write(csr_value): |
      if (CSR[mstateen0].CONTEXT == 1'b0){
        return 0;
      }
      CSR[hstateen0].CONTEXT = csr_value.CONTEXT;
      return csr_value.CONTEXT;
    description: |
      The CONTEXT bit in `hstateen0h` controls access to the `scontext` CSR provided
      by the Sdtrig extension.
    type: RW
    reset_value: UNDEFINED_LEGAL
  CTR:
    long_name: ctr access control
    location: 22
    alias: hstateen0.CTR
    sw_write(csr_value): |
      if (CSR[mstateen0].CTR == 1'b0){
        return 0;
      }
      CSR[hstateen0].CTR = csr_value.CTR;
      return csr_value.CTR;
    description: |
      If the H extension is implemented and `mstateen0.CTR=1`, the `hstateen0.CTR` bit controls access to
      supervisor CTR state when V=1. This state includes `sctrctl` (really `vsctrctl`), `sctrstatus`, and `sireg*`
      (really `vsireg*`) when `siselect` (really `vsiselect`) is in 0x200..0x2FF. `hstateen0.CTR` is read-only 0 when
      `mstateen0.CTR=0`.
    type: RW
    reset_value: 0
sw_read(): return $bits(CSR[hstateen0])[63:32];
