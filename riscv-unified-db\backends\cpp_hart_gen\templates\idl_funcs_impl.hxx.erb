#pragma once

// THIS FILE IS AUTOGENERATED
// IT IS NOT STANDALONE. IT IS A FUNCTION LIST FOR THE Hart CLASS

// This contains all IDL functions that either templates or constexpr
// Since either needs to be inlined, they must go in the hart header rather than a cxx file

#include "udb/hart.hpp"

#define __UDB_CONSTEXPR_FUNC_CALL <%= name_of(:hart, cfg_arch) %><SocType>::
#define __UDB_CONST_GLOBAL(global_name) <%= name_of(:hart, cfg_arch) %><SocType>::global_name
#define __UDB_MUTABLE_GLOBAL(global_name) global_name
#define __UDB_STRUCT(struct_name) <%= name_of(:cfg, cfg_arch) %>_ ## struct_name ## _Struct
#define __UDB_STATIC_PARAM(param_name) <%= name_of(:params, cfg_arch) %>::param_name ## _VALUE
#define __UDB_RUNTIME_PARAM(param_name) m_params.param_name
#define __UDB_CSR_BY_NAME(csr_name) m_csrs.csr_name
#define __UDB_CSR_BY_ADDR(csr_addr) (*csr(csr_addr))
#define __UDB_FUNC_CALL this->
#define __UDB_PC m_pc
#define __UDB_SET_PC(new_pc) m_next_pc = new_pc
#define __UDB_XLEN xlen()

namespace udb {

<%# need to get symtab at function scope -%>
<%- cfg_arch.reachable_functions.each do |func| -%>
<%- next if func.builtin? || func.generated? -%>
<%- next unless func.templated? || func.constexpr?(cfg_arch.symtab) # non-templated functions come next -%>
<%- symtab = cfg_arch.symtab.global_clone.push(func) -%>

<%- if func.name == "ary_includes?" -%>
template <SocModel SocType>
template <unsigned ARY_SIZE, typename ElementType, typename ValueType>
constexpr bool <%= name_of(:hart, cfg_arch) %><SocType>::ary_includes_Q_(const std::array<ElementType, ARY_SIZE>& ary, const ValueType& value) {
  return std::ranges::find(ary, static_cast<ElementType>(value)) != std::ranges::end(ary);
}
template <SocModel SocType>
template <typename ElementType, typename ValueType>
bool <%= name_of(:hart, cfg_arch) %><SocType>::ary_includes_Q_(const std::vector<ElementType>& ary, const ValueType& value) {
  return std::ranges::find(ary, static_cast<ElementType>(value)) != std::ranges::end(ary);
}
<%- else -%>
//
// <%= func.description.gsub("\n", "\n// ") %>
template <SocModel SocType>
<%= func.gen_cpp_prototype(symtab, 0, include_semi: false, qualifiers: func.constexpr?(cfg_arch.symtab) ? "constexpr" : "", cpp_class: "#{name_of(:hart, cfg_arch)}<SocType>") %> {
  <%- func.apply_template_and_arg_syms(symtab) -%>
  <%- pruned_func = func.prune(symtab).freeze_tree(symtab) -%>
  <%= pruned_func.body.gen_cpp(symtab) %>
}
<%- end -%>
<%- symtab.release -%>
<%- end -%>


<%- cfg_arch.reachable_functions.each do |func| -%>
<%- next if func.builtin? || func.generated? -%>
<%- next if func.constexpr?(cfg_arch.symtab) # constexpr funcs are class methods -%>
<%- next if func.templated? # templated functions have to go in idl_funcs_impl.hxx -%>
<%- symtab = cfg_arch.symtab.global_clone.push(nil) -%>

//
// <%= func.description.gsub("\n", "\n// ") %>
template <SocModel SocType>
<%= func.gen_cpp_prototype(symtab, 0, include_semi: false, cpp_class: "#{name_of(:hart, cfg_arch)}<SocType>") %> {
  <%- func.apply_template_and_arg_syms(symtab) -%>
  <%- pruned_func = func.prune(symtab).freeze_tree(symtab) -%>
  <%= pruned_func.body.gen_cpp(symtab) %>
}

<%- symtab.release -%>
<%- end -%>

} // namespace udb

#undef __UDB_CONSTEXPR_FUNC_CALL
#undef __UDB_CONST_GLOBAL
#undef __UDB_MUTABLE_GLOBAL
#undef __UDB_STRUCT
#undef __UDB_STATIC_PARAM
#undef __UDB_RUNTIME_PARAM
#undef __UDB_CSR_BY_NAME
#undef __UDB_CSR_BY_ADDR
#undef __UDB_FUNC_CALL
#undef __UDB_PC
#undef __UDB_SET_PC
#undef __UDB_XLEN
