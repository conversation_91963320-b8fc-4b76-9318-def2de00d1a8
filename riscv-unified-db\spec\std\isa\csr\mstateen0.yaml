# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mstateen0
long_name: Machine State Enable 0 Register
address: 0x30C
priv_mode: M
length: 64
description:
  - id: csr-mstateen0-purpose
    normative: true
    text: |
      Each bit of a `stateen` CSR controls less-privileged access to an extension’s state,
      or an extension that was not deemed "worthy" of a full XS field in `sstatus` like the
      FS and VS fields for the F and V extensions.
  - id: csr-mstateen0-num-justification
    normative: false
    text: |
      The number of registers provided at each level is four because it is believed that
      4 * 64 = 256 bits for machine and hypervisor levels, and 4 * 32 = 128 bits for
      supervisor level, will be adequate for many years to come, perhaps for as long as
      the RISC-V ISA is in use.
      The exact number four is an attempted compromise between providing too few bits on
      the one hand and going overboard with CSRs that will never be used on the other.
  - id: csr-mstateen0-scope
    normative: true
    text: |
      The `stateen` registers at each level control access to state at all less-privileged
      levels, but not at its own level.
  - id: csr-mstateen0-effect
    normative: true
    text: |
      When a `stateen` CSR prevents access to state for a privilege mode, attempting to execute
      in that privilege mode an instruction that implicitly updates the state without reading
      it may or may not raise an illegal instruction or virtual instruction exception.
      Such cases must be disambiguated by being explicitly specified one way or the other.
      In some cases, the bits of the `stateen` CSRs will have a dual purpose as enables for the
      ISA extensions that introduce the controlled state.
  - id: csr-mstateen0-bit-allocation
    normative: true
    text: |
      For every bit with a defined purpose in an `sstateen` CSR, the same bit is defined in the matching
      `mstateen` CSR to control access below machine level to the same state. The upper 32 bits of an
      `mstateen` CSR (or for RV32, the corresponding high-half CSR) control access to state that is inherently
      inaccessible to user level, so no corresponding enable bits in the supervisor-level `sstateen` CSR are
      applicable. The intention is to allocate bits for this purpose starting at the most-significant end, bit 63,
      through to bit 32, and then on to the next-higher `mstateen` CSR. If the rate that bits are being allocated
      from the least-significant end for `sstateen` CSRs is sufficiently low, allocation from the most-significant
      end of `mstateen` CSRs may be allowed to encroach on the lower 32 bits before jumping to the next-higher
      `mstateen` CSR. In that case, the bit positions of "encroaching" bits will remain forever read-only zeros in
      the matching `sstateen` CSRs.
  - id: csr-mstateen0-zero
    normative: true
    text: |
      For every bit in an `mstateen` CSR that is zero (whether read-only zero or set to zero),
      the same bit appears as read-only zero in the matching `hstateen` and `sstateen` CSRs.
  - id: csr-mstateen0-read-only
    normative: true
    text: |
      A bit in a supervisor-level `sstateen` CSR cannot be read-only one unless the same bit is read-only one in the matching
      `mstateen` CSR and, if it exists, in the matching `hstateen` CSR. A bit in an `hstateen` CSR cannot be read-only one unless
      the same bit is read-only one in the matching `mstateen` CSR. Bit 63 of each `mstateen` CSR may be read-only zero only if
      the hypervisor extension is not implemented and the matching supervisor-level `sstateen` CSR is all read-only zeros.

definedBy: Smstateen
fields:
  SE0:
    long_name: hstateen0, hstateen0h, and sstateen0 access control
    location: 63
    description: |
      The SE0 bit in `mstateen0` controls access to the `hstateen0`, `hstateen0h`, and the `sstateen0` CSRs.
    type: RW
    reset_value: 0
  ENVCFG:
    long_name: henvcfg, henvcfgh, and senvcfg access control
    location: 62
    definedBy:
      name: S
      version: ">= 1.11"
    description: |
      The ENVCFG bit in `mstateen0` controls access to the `henvcfg`, `henvcfgh`, and the `senvcfg` CSRs.
    type: RW
    reset_value: 0
  CSRIND:
    long_name: siselect, sireg*, vsiselect, and vsireg* access control
    location: 60
    definedBy: Sscsrind
    description: |
      The CSRIND bit in `mstateen0` controls access to the `siselect`, `sireg*`, `vsiselect`, and the `vsireg*`
      CSRs provided by the Sscsrind extensions.
    type: RW
    reset_value: 0
  AIA:
    long_name: Ssaia state access control
    location: 59
    definedBy: Ssaia
    description: |
      The AIA bit in `mstateen0` controls access to all state introduced by the Ssaia extension and is not
      controlled by either the CSRIND or the IMSIC bits.
    type: RW
    reset_value: 0
  IMSIC:
    long_name: IMSIC state access control
    location: 58
    definedBy: Ssaia
    description: |
      The IMSIC bit in `mstateen0` controls access to the IMSIC state, including CSRs `stopei` and `vstopei`,
      provided by the Ssaia extension.
    type: RW
    reset_value: 0
  CONTEXT:
    long_name: scontext and hcontext access control
    location: 57
    definedBy: Sdtrig
    description: |
      The CONTEXT bit in `mstateen0` controls access to the `scontext` and `hcontext` CSRs provided by the
      Sdtrig extension.
    type: RW
    reset_value: 0
  P1P13:
    long_name: hedelegh access control
    location: 56
    description: |
      The P1P13 bit in `mstateen0` controls access to the `hedelegh` introduced by Privileged Specification
      Version 1.13.
    type: RW
    reset_value: 0
  SRMCFG:
    long_name: srmcfg access control
    location: 55
    definedBy: Ssqosid
    description: |
      The SRMCFG bit in `mstateen0` controls access to the `srmcfg`` CSR introduced by the Ssqosid Chapter 18
      extension.
    type: RW
    reset_value: 0
  CTR:
    long_name: ctr access control
    location: 54
    description: |
      When Smstateen is implemented, the `mstateen0.CTR` bit controls access to CTR register state from
      privilege modes less privileged than M-mode.
    type: RW
    reset_value: 0
  JVT:
    long_name: jvt access control
    location: 2
    definedBy: Zcmt
    description: |
      The JVT bit controls access to the `jvt` CSR provided by the Zcmt extension.
    type: RW
    reset_value: 0
  FCSR:
    long_name: fcsr access control
    location: 1
    #definedBy:
    #anyOf: [Zhinx, Zfinx, Zdinx]
    description: |
      The FCSR bit controls access to `fcsr` for the case when floating-point instructions
      operate on `x` registers instead of `f` registers as specified by the Zfinx and related
      extensions (Zdinx, etc.). Whenever `misa.F` = 1, FCSR bit of `mstateen0` is read-only
      zero (and hence read-only zero in `hstateen0` and `sstateen0` too). For convenience,
      when the `stateen` CSRs are implemented and `misa.F` = 0, then if the FCSR bit of a
      controlling `stateen0` CSR is zero, all floating-point instructions cause an illegal
      instruction trap (or virtual instruction trap, if relevant), as though they all access
      `fcsr`, regardless of whether they really do.
    type: RW
    reset_value: 0
  C:
    long_name: custom state access control
    location: 0
    description: |
      The C bit controls access to any and all custom state. The C bit of these registers is
      not custom state itself; it is a standard field of a standard CSR, either `mstateen0`,
      `hstateen0`, or `sstateen0`.
    type: RW
    reset_value: 0
