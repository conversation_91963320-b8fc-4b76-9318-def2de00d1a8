= Configuration of <%= cfg_arch.name %>

== Extensions

|===
| Name | Version
<%- cfg_arch.transitive_implemented_extension_versions.sort{ |a,b| a.name <=> b.name }.each do |e| -%>
| `<%= e.name %>` | <%= e.version_spec %>
<%- end -%>
|===

== Parameters

<%- cfg_arch.params_with_value.sort_by { |param| param.name }.each do |param| -%>
=== *<%= param.name %>*

[cols="1,4,1"]
|===
| Value | Description | From Extension

| <%= param.value %>
a| <%= param.desc %>
a| <%= param.exts.map { |e| "`#{e.name}`" }.join(", ")%>
|===

<%- end -%>
