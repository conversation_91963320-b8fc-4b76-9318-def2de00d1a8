# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.addi16sp
long_name: Add a sign-extended non-zero immediate
description: |
  C.ADDI16SP adds the non-zero sign-extended 6-bit immediate to the value in the stack pointer (sp=x2), where the immediate is scaled to represent multiples of 16 in the range (-512,496).
  C.ADDI16SP is used to adjust the stack pointer in procedure prologues and epilogues.
  It expands into `addi x2, x2, nzimm[9:4]`.
  C.ADDI16SP is only valid when nzimm &ne; 0; the code point with nzimm=0 is reserved.
definedBy:
  anyOf:
    - C
    - Zca
assembly: sp, imm
encoding:
  match: 011-00010-----01
  variables:
    - name: imm
      location: 12|4-3|5|2|6
      left_shift: 4
      not: 0
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  X[2] = X[2] + $signed(imm);
