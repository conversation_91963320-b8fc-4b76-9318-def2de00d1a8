# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvksh
long_name: "ShangMi Suite: SM3 Secure Hash"
description: |
  Instructions for accelerating functions of the SM3 Hash Function.

  All of these instructions work on 256-bit element groups comprised of eight 32-bit elements.

  To help avoid side-channel timing attacks, these instructions shall be implemented with data-independent timing.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
