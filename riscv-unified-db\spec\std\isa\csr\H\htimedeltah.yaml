# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: htimedeltah
long_name: Hypervisor time delta, upper half
description: |
  Upper half of the `htimedelta` CSR.

address: 0x615
writable: true
priv_mode: S
definedBy: H
length: 32
base: 32
fields:
  DELTA:
    location: 31-0
    description: Signed delta
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      CSR[htimedelta].DELTA = {csr_value.DELTA, CSR[htimedelta].DELTA[31:0]};
      return csr_value.DELTA;
sw_read(): |
  return CSR[htimedelta].DELTA[31:0];
