# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
kind: csr
name: mcyclecfg
long_name: Machine Cycle Counter Configuration
address: 0x321
priv_mode: M
length: 64
definedBy: Smcntrpmf
description: |
  The `mcyclecfg` CSR is a 64-bit machine-level register that configures privilege
  mode filtering for the cycle counter. Each inhibit bit (xINH) suppresses
  counting of events in the corresponding privilege mode when set.

  | Field   | Description                                             |
  |---------|---------------------------------------------------------|
  | MINH    | If set, then counting of events in M-mode is inhibited.       |
  | SINH    | If set, then counting of events in S/HS-mode is inhibited.    |
  | UINH    | If set, then counting of events in U-mode is inhibited.       |
  | VSINH   | If set, then counting of events in VS-mode is inhibited.      |
  | VUINH   | If set, then counting of events in VU-mode is inhibited.      |

  When all xINH bits are zero, event counting is enabled in all modes.

  For each bit in 61:58, if the associated privilege mode is not implemented,
  the bit is read-only zero.

  Bit 63 is hardwired to 0, as these counters do not generate overflow interrupts.

  Bits [57:0] are reserved (WPRI) and read as zero.

  For RV32, the upper 32 bits of `mcyclecfg` are accessed via `mcyclecfgh` (CSR 0x721).

  Note: Although CSR address 0x320 would have been a more natural choice,
  it is already allocated to `mcountinhibit`.

fields:
  MINH:
    location: 62
    base: 64
    type: RW
    definedBy: M
    description: If set, then counting of events in M-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  SINH:
    location: 61
    base: 64
    type: RW
    definedBy: S
    description: If set, then counting of events in S/HS-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  UINH:
    location: 60
    base: 64
    type: RW
    definedBy: U
    description: If set, then counting of events in U-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  VSINH:
    location: 59
    base: 64
    type: RW
    definedBy: H
    description: If set, then counting of events in VS-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  VUINH:
    location: 58
    base: 64
    type: RW
    definedBy: H
    description: If set, then counting of events in VU-mode is inhibited.
    reset_value: UNDEFINED_LEGAL
