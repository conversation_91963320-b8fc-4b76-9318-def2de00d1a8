# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: V
type: unprivileged
long_name: Variable-length vector
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
description: |
  TODO
params:
  MUTABLE_MISA_V:
    description: |
      Indicates whether or not the `V` extension can be disabled with the `misa.V` bit.
    schema:
      type: boolean
  HW_MSTATUS_VS_DIRTY_UPDATE:
    description: |
      Indicates whether or not hardware will write to `mstatus.VS`

      Values are:
      [separator="!"]
      !===
      h! never  ! Hardware never writes `mstatus.VS`
      h! precise  ! Hardware writes `mstatus.VS` to the Dirty (3) state precisely when V registers are modified
      h! imprecise ! Hardware writes `mstatus.VS` imprecisely. This will result in a call to unpredictable() on any attempt to read `mstatus` or write vector state.
      !===
    schema:
      type: string
      enum: ["never", "precise", "imprecise"]
  MSTATUS_VS_LEGAL_VALUES:
    description: |
      The set of values that mstatus.VS will accept from a software write.
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1, 2, 3]
      maxItems: 4
      uniqueItems: true
    also_defined_in: S
    extra_validation: |
      assert MSTATUS_VS_LEGAL_VALUES.include?(0) && MSTATUS_VS_LEGAL_VALUES.include?(3) if ext?(:V)

      # if HW is writing VS, then Dirty (3) better be a supported value
      assert MSTATUS_VS_LEGAL_VALUES.include?(3) if ext?(:V) && (HW_MSTATUS_VS_DIRTY_UPDATE != "never")
