<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Instruction::Encoding::Field
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Instruction::Encoding::Field";
  relpath = '../../';
</script>


  <script type="text/javascript" charset="utf-8" src="../../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../../_index.html">Index (F)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../../Instruction.html" title="Instruction (class)">Instruction</a></span></span> &raquo; <span class='title'><span class='object_link'><a href="../Encoding.html" title="Instruction::Encoding (class)">Encoding</a></span></span>
     &raquo; 
    <span class="title">Field</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Instruction::Encoding::Field
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Instruction::Encoding::Field</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>represents an encoding field (contiguous set of bits that form an opcode or decode variable slot)</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Either string of 0’s ans 1’s or a bunch of dashses.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#range-instance_method" title="#range (instance method)">#<strong>range</strong>  &#x21d2; Range </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Range of bits in the parent corresponding to this field.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(name, range)  &#x21d2; Field </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Field.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#opcode%3F-instance_method" title="#opcode? (instance method)">#<strong>opcode?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not the field represents part of the opcode (i.e., not a decode variable).</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(name, range)  &#x21d2; <tt><span class='object_link'><a href="" title="Instruction::Encoding::Field (class)">Field</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Field.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Either string of 0’s ans 1’s or a bunch of dashses</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>range</span>
      
      
        <span class='type'>(<tt>Range</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Range of the field in the parent CSR</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


934
935
936
937</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 934</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='id identifier rubyid_range'>range</span><span class='rparen'>)</span>
  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_name'>name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
  <span class='ivar'>@range</span> <span class='op'>=</span> <span class='id identifier rubyid_range'>range</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>String</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Either string of 0’s ans 1’s or a bunch of dashses.</p>


  </div>
</div>
<div class="tags">
  
  <div class="examples">
    <p class="tag_title">Examples:</p>
    
      
        <p class="example_title"><div class='inline'>
<p>Field of a decode variable</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_encoding'>encoding</span><span class='period'>.</span><span class='id identifier rubyid_opcode_fields'>opcode_fields</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span> <span class='comment'>#=&gt; &#39;-----&#39; (for imm5)</span></code></pre>
    
      
        <p class="example_title"><div class='inline'>
<p>Field of an opcode</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_encoding'>encoding</span><span class='period'>.</span><span class='id identifier rubyid_opcode_fields'>opcode_fields</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span> <span class='comment'>#=&gt; &#39;0010011&#39; (for funct7)</span></code></pre>
    
  </div>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Either string of 0’s ans 1’s or a bunch of dashses</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


927
928
929</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 927</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="range-instance_method">
  
    #<strong>range</strong>  &#x21d2; <tt>Range</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Range of bits in the parent corresponding to this field.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Range</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Range of bits in the parent corresponding to this field</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


930
931
932</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 930</span>

<span class='kw'>def</span> <span class='id identifier rubyid_range'>range</span>
  <span class='ivar'>@range</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="opcode?-instance_method">
  
    #<strong>opcode?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not the field represents part of the opcode (i.e., not a decode variable).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not the field represents part of the opcode (i.e., not a decode variable)</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


940
941
942</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 940</span>

<span class='kw'>def</span> <span class='id identifier rubyid_opcode?'>opcode?</span>
  <span class='id identifier rubyid_name'>name</span><span class='period'>.</span><span class='id identifier rubyid_match?'>match?</span><span class='lparen'>(</span><span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>^[01]+$</span><span class='regexp_end'>/</span></span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>