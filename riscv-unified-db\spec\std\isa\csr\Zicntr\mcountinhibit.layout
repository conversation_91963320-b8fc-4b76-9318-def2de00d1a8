# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mcountinhibit
long_name: Machine Counter Inhibit
address: 0x320
priv_mode: M
length: 32
description: |
  Bits to inhibit (stops counting) performance counters.

  The counter-inhibit register `mcountinhibit` is a *WARL* register that
  controls which of the hardware performance-monitoring counters
  increment. The settings in this register only control whether the
  counters increment; their accessibility is not affected by the setting
  of this register.

  When the CY, IR, or HPM__n__ bit in the `mcountinhibit` register is clear,
  the `mcycle`, `minstret`, or `mhpmcountern` register increments as usual.
  When the CY, IR, or HPM_n_ bit is set, the corresponding counter does
  not increment.

  The `mcycle` CSR may be shared between harts on the same core, in which
  case the `mcountinhibit.CY` field is also shared between those harts,
  and so writes to `mcountinhibit.CY` will be visible to those harts.

  If the `mcountinhibit` register is not implemented, the implementation
  behaves as though the register were set to zero.

  [NOTE]
  ====
  When the `mcycle` and `minstret` counters are not needed, it is desirable
  to conditionally inhibit them to reduce energy consumption. Providing a
  single CSR to inhibit all counters also allows the counters to be
  atomically sampled.

  Because the `mtime` counter can be shared between multiple cores, it
  cannot be inhibited with the `mcountinhibit` mechanism.
  ====

definedBy:
  anyOf:
    - name: Sm
    - name: Smhpm
fields:
  CY:
    location: 0
    definedBy: Sm
    description: When set, `mcycle.COUNT` stops counting in all privilege modes.
    type(): |
      return COUNTINHIBIT_EN[0] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[0] ? UNDEFINED_LEGAL : 0;
  IR:
    location: 2
    definedBy: Sm
    description: When set, `minstret.COUNT` stops counting in all privilege modes.
    type(): |
      return COUNTINHIBIT_EN[2] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[2] ? UNDEFINED_LEGAL : 0;
  <%- (3..31).each do |hpm_num| -%>
  HPM<%= hpm_num %>:
    location: <%= hpm_num %>
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[<%= hpm_num %>] == true"]
      When set, `hpmcounter<%= hpm_num %>.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[<%= hpm_num %>] == false"]
      Since hpmcounter<%= hpm_num %> is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[<%= hpm_num %>] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[<%= hpm_num %>] ? UNDEFINED_LEGAL : 0;
  <%- end -%>
