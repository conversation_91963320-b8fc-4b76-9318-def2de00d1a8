# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
kind: csr
name: vsireg
long_name: Virtual Supervisor Indirect Register Alias
address: 0x251
virtual_address: 0x251
priv_mode: VS
length: VSXLEN
definedBy: Smcsrind
description:
  - id: csr-vsireg-purpose
    normative: true
    text: |
      The `vsireg` CSR is one of several alias registers used to indirectly access
      virtual supervisor-level CSRs in VS-mode or VU-mode.

  - id: csr-vsireg-selection-mechanism
    normative: true
    text: |
      The register addressed by `vsireg` is selected by the current value of the `vsiselect` CSR.

  - id: csr-vsireg-indirect-access-benefit
    normative: false
    text: |
      The alias mechanism allows indirect CSR access, which helps in virtualization and future extensibility.

  - id: csr-vsireg-access-exception
    normative: true
    text: |
      A virtual instruction exception is raised for attempts from VS-mode or VU-mode
      to directly access `vsiselect` or `vsireg`, or from VU-mode to access `siselect` or `sireg`.

  - id: csr-vsireg-unimplemented-target
    normative: true
    text: |
      The behavior of accesses to `vsireg` when `vsiselect` holds a value that is
      not implemented at the HS level is UNSPECIFIED.

  - id: csr-vsireg-unimplemented-target-recommendation
    normative: false
    text: |
      Implementations are recommended to raise an illegal instruction exception for
      accesses to unimplemented targets via `vsireg`.

  - id: csr-vsireg-width
    normative: true
    text: |
      The width of `vsireg` is always the current `XLEN`, not `VSXLEN`.
      For example, if `HSXLEN = 64` and `VSXLEN = 32`, then `vsireg` is 64 bits wide
      when accessed from HS-mode (RV64), but 32 bits when accessed from VS-mode (RV32).

fields:
  VALUE:
    long_name: Indirectly Selected Register Value
    location_rv32: 31-0
    location_rv64: 63-0
    type: RW
    description:
      - id: csr-vsireg-value-indirect-data
        normative: true
        text: |
          The data read from or written to the register selected by the current
          value of the `vsiselect` CSR.
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 1);
      if (!handle.valid) {
        unimplemented_csr($encoding);
      }
      if (!handle.writable) {
        raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
      csr_sw_write(handle, csr_value.VALUE);
      return csr_hw_read(handle);
sw_read(): |
  Csr handle = indirect_csr_lookup(CSR[vsiselect].VALUE, 1);
  if (!handle.valid) {
    unimplemented_csr($encoding);
  }
  return csr_sw_read(handle);
