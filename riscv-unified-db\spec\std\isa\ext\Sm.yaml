# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Sm
type: privileged
long_name: Machine mode
versions:
  - version: "1.11.0"
    state: ratified
    ratification_date: 2019-12
    defined_by: UDB
    changes:
      - Moved Machine spec to *Ratified* status.
      - Improvements to the description and commentary.
      - Specified which interrupt sources are reserved for standard use.
      - Allocated some synchronous exception causes for custom use.
      - Specified the priority ordering of synchronous exceptions.
      - Added specification that xRET instructions may, but are not required
        to, clear LR reservations if A extension present.
      - Made the `mstatus`.MPP field *WARL*, rather than *WLRL*.
      - Made the unused `__x__ip` fields *WPRI*, rather than *WIRI*.
      - Made the unused `misa` fields *WARL*, rather than *WIRI*.
      - Rectified an editing error that misdescribed the mechanism by which
        `mstatus.__x__IE` is written upon an exception.
      - Described scheme for emulating misaligned AMOs.
      - Specified the behavior of the `misa` and `__x__epc` registers in systems
        with variable IALIGN.
      - Specified the behavior of writing self-contradictory values to the
        `misa` register.
      - Specified contents of CSRs across XLEN modification.
      - Moved PLIC chapter into its own document.
  - version: "1.12.0"
    state: ratified
    ratification_date: 2021-12
    defined_by: UDB
    changes:
      - Changed MRET to clear `mstatus`.MPRV when leaving M-mode.
      - Relaxed I/O regions have been specified to follow RVWMO. The previous
        specification implied that PPO rules other than fences and
        acquire/release annotations did not apply.
      - Constrained the LR/SC reservation set size and shape when using
        page-based virtual memory.
      - PMP changes require an SFENCE.VMA on any hart that implements
        page-based virtual memory, even if VM is not currently enabled.
      - Removed the N extension.
      - Defined the mandatory RV32-only CSR `mstatush`, which contains most of
        the same fields as the upper 32 bits of RV64's `mstatus`.
      - Defined the mandatory CSR `mconfigptr`, which if nonzero contains the
        address of a configuration data structure.
      - Defined optional `mseccfg` and `mseccfgh` CSRs, which control the
        machine's security configuration.
      - Defined `menvcfg` CSR (and RV32-only `menvcfgh`), which control various characteristics
        of the execution environment.
      - Designated part of SYSTEM major opcode for custom use.
      - Permitted the unconditional delegation of less-privileged interrupts.
      - Added optional big-endian and bi-endian support.
      - Made priority of load/store/AMO address-misaligned exceptions
        implementation-defined relative to load/store/AMO page-fault and
        access-fault exceptions.
      - Software breakpoint exceptions are permitted to write either 0 or the
        `pc` to `__x__tval`.
      - Specified relaxed constraints for implicit reads of non-idempotent regions.
  - version: "1.13.0"
    state: frozen
    ratification_date: 2023-12
    defined_by: UDB
    changes:
      - Redefined `misa`.MXL to be read-only, making MXLEN a constant.
      - Defined the `misa`.B field to reflect that the B extension has been
        implemented.
      - Defined the `misa`.V field to reflect that the V extension has been
        implemented.
      - Defined the RV32-only `medelegh` CSR.
      - Defined the misaligned atomicity granule PMA, superseding the proposed Zam
        extension.
      - Defined hardware error and software check exception codes.
      - Specified synchronization requirements when changing the PBMTE fields
        in `menvcfg` and `henvcfg`.
      - Exposed count-overflow interrupts to VS-mode via the Shlcofideleg extension.
      - Relaxed behavior of some HINTs when MXLEN > XLEN.
      - Transliterated the document from LaTeX into AsciiDoc.
      - Included all ratified extensions through March 2024.
      - Clarified that "platform- or custom-use" interrupts are actually
        "platform-use interrupts", where the platform can choose to make some custom.
      - Clarified semantics of explicit accesses to CSRs wider than XLEN bits.
      - Clarified that MXLEN&#8805;SXLEN.
      - Clarified that WFI is not a HINT instruction.
      - Clarified that, for a given exception cause, `__x__tval` might sometimes
        be set to a nonzero value but sometimes not.
      - Clarified exception behavior of unimplemented or inaccessible CSRs.
      - Replaced the concept of vacant memory regions with inaccessible memory or I/O regions.
      - Clarified that timer and count-overflow interrupts' arrival in
        interrupt-pending registers is not immediate.
      - Clarified that MXR affects only explicit memory accesses.
description: |
  This chapter describes the machine-level operations available in machine-mode (M-mode), which is
  the highest privilege mode in a RISC-V hart. M-mode is used for low-level access to a hardware
  platform and is the first mode entered at reset. M-mode can also be used to implement features that
  are too difficult or expensive to implement in hardware directly. The RISC-V machine-level ISA
  contains a common core that is extended depending on which other privilege levels are supported and
  other details of the hardware implementation.
  This chapter describes the RISC-V machine-level architecture, which
  contains a common core that is used with various supervisor-level
  address translation and protection schemes.
interrupt_codes:
  - num: 1
    name: Supervisor software interrupt
    var: SupervisorSoftware
  - num: 3
    name: Machine software interrupt
    var: MachineSoftware
  - num: 5
    name: Supervisor timer interrupt
    var: SupervisorTimer
  - num: 7
    name: Machine timer interrupt
    var: MachineTimer
  - num: 9
    name: Supervisor external interrupt
    var: SupervisorExternal
  - num: 11
    name: Machine external interrupt
    var: MachineExternal
exception_codes:
  - num: 0
    name: Instruction address misaligned
    var: InstructionAddressMisaligned
  - num: 1
    name: Instruction access fault
    var: InstructionAccessFault
  - num: 2
    name: Illegal instruction
    var: IllegalInstruction
  - num: 3
    name: Breakpoint
    var: Breakpoint
  - num: 4
    name: Load address misaligned
    var: LoadAddressMisaligned
  - num: 5
    name: Load access fault
    var: LoadAccessFault
  - num: 6
    name: Store/AMO address misaligned
    var: StoreAmoAddressMisaligned
  - num: 7
    name: Store/AMO access fault
    var: StoreAmoAccessFault
  - num: 8
    name: Environment call from <%- if ext?(:H) -%>V<%- end -%>U-mode
    var: Ucall
  - num: 9
    name: Environment call from <%- if ext?(:H) -%>H<%- end -%>S-mode
    var: Scall
  - num: 11
    name: Environment call from M-mode
    var: Mcall
  - num: 12
    name: Instruction page fault
    var: InstructionPageFault
  - num: 13
    name: Load page fault
    var: LoadPageFault
  - num: 15
    name: Store/AMO page fault
    var: StoreAmoPageFault
  - num: 18
    name: Software Check
    var: SoftwareCheck
    when:
      version: ">= 1.13.0"
params:
  MXLEN:
    description: |
      XLEN in M-mode
    schema:
      type: integer
      enum: [32, 64]
  PRECISE_SYNCHRONOUS_EXCEPTIONS:
    description: |
      Whether or not all synchronous exceptions are precise.

      If false, any exception not otherwise mandated to precise (e.g., PMP violation)
      will cause execution to enter an unpredictable state.
    schema:
      type: boolean
      default: true
  TRAP_ON_ECALL_FROM_M:
    description: |
      Whether or not an ECALL-from-M-mode causes a synchronous exception.

      The spec states that implementations may handle ECALLs transparently
      without raising a trap, in which case the EEI must provide a builtin.
    schema:
      type: boolean
      default: true
  TRAP_ON_EBREAK:
    description: |
      Whether or not an EBREAK causes a synchronous exception.

      The spec states that implementations may handle EBREAKs transparently
      without raising a trap, in which case the EEI must provide a builtin.
    schema:
      type: boolean
      default: true
  ARCH_ID:
    description: |
      Vendor-specific architecture ID in `marchid`
    schema:
      type: integer
      minimum: 0
      maximum: 0xffffffffffffffff
  IMP_ID:
    description: |
      Vendor-specific implementation ID in `mimpid`
    schema:
      type: integer
      minimum: 0
      maximum: 0xffffffffffffffff
  VENDOR_ID_BANK:
    description: |
      JEDEC Vendor ID bank, for `mvendorid`
    schema:
      type: integer
      minimum: 0
      maximum: ********
  VENDOR_ID_OFFSET:
    description: |
      Vendor JEDEC code offset, for `mvendorid`
    schema:
      type: integer
      minimum: 0
      maximum: 127
  MISALIGNED_LDST:
    description: |
      Does the implementation perform non-atomic misaligned loads and stores to main memory
      (does *not* affect misaligned support to device memory)?
      If not, the implementation always throws a misaligned exception.
    schema:
      type: boolean
  MISALIGNED_LDST_EXCEPTION_PRIORITY:
    description: |
      The relative priority of a load/store/AMO exception vs. load/store/AMO page-fault
      or access-fault exceptions.

      May be one of:

      [separator="!"]
      !===
      ! low  ! Misaligned load/store/AMO exceptions are always lower priority than load/store/AMO page-fault and access-fault exceptions.
      ! high ! Misaligned load/store/AMO exceptions are always higher priority than load/store/AMO page-fault and access-fault exceptions.
      !===

      MISALIGNED_LDST_EXCEPTION_PRIORITY cannot be "high" when MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE
      is non-zero, since the atomicity of an access cannot be determined in that case until after
      address translation.
    schema:
      type: string
      enum: ["low", "high"]
      extra_validation: |
        assert (MISALIGNED_LDST_EXCEPTION_PRIORITY == "low") if MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE.positive?
  MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE:
    description: |
      The maximum granule size, in bytes, that the hart can atomically perform a
      misaligned load/store/AMO without raising a Misaligned exception. When MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE is 0, the hart
      cannot atomically perform a misaligned load/store/AMO. When a power of two, the hart can
      atomically load/store/AMO a misaligned access that is fully contained in a
      MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE-aligned region.

      [NOTE]
      Even if the hart is capable of performing a misaligned load/store/AMO atomically,
      a misaligned exception may still occur if the access does not have the appropriate
      Misaligned Atomicity Granule PMA set.
    schema:
      type: integer
      # can't be larger than a page, since there is no way to reconcile that with virtual memory
      enum: [0, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096]
  MISALIGNED_SPLIT_STRATEGY:
    description: |
      When misaligned accesses are supported, this determines the *order* in the implementation appears
      to process the load/store, which determines how/which exceptions will be reported

      Options:

       * by_byte: The load/store appears to be broken into byte-sized accesses that processed sequentially from smallest address to largest address
       * custom: Something else. Will result in a call to unpredictable() in the execution
    schema:
      type: string
      enum: ["by_byte", "custom"]
  TRAP_ON_ILLEGAL_WLRL:
    description: |
      When true, writing an illegal value to a WLRL CSR field raises an `IllegalInstruction` exception.

      When false, writing an illegal value to a WLRL CSR field is `unpredictable`.
    schema:
      type: boolean
  TRAP_ON_UNIMPLEMENTED_INSTRUCTION:
    description: |
      When true, fetching an unimplemented instruction from the custom encoding space will cause
      an `IllegalInstruction` exception.

      When false, fetching an unimplemented instruction is `UNPREDICTABLE`.
    schema:
      type: boolean
  TRAP_ON_RESERVED_INSTRUCTION:
    description: |
      When true, fetching an unimplemented and/or undefined instruction from the standard/reserved
      encoding space will cause an `IllegalInstruction` exception.

      When false, fetching such an instruction is `UNPREDICTABLE`.
    schema:
      type: boolean
  TRAP_ON_UNIMPLEMENTED_CSR:
    description: |
      When true, accessing an unimplemented CSR (via a `Zicsr` instruction) will cause an `IllegalInstruction` exception.

      When false, accessing an unimplemented CSR (via a `Zicsr` instruction) is `unpredictable`.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_BREAKPOINT:
    description: |
      When true, `mtval` is written with the virtual PC of the EBREAK instruction (same information as `mepc`).

      When false, `mtval` is written with 0 on an EBREAK instruction.

      Regardless, `mtval` is always written with a virtual PC when an external breakpoint is generated
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_LOAD_MISALIGNED:
    description: |
      When true, `mtval` is written with the virtual address of a load instruction when the
      address is misaligned and MISALIGNED_LDST is false.

      When false, `mtval` is written with 0 when a load address is misaligned and
      MISALIGNED_LDST is false.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_STORE_AMO_MISALIGNED:
    description: |
      When true, `mtval` is written with the virtual address of a store instruction when the
      address is misaligned and MISALIGNED_LDST is false.

      When false, `mtval` is written with 0 when a store address is misaligned and
      MISALIGNED_LDST is false.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_INSTRUCTION_MISALIGNED:
    description: |
      When true, `mtval` is written with the virtual PC when an instruction fetch is misaligned.

      When false, `mtval` is written with 0 when an instruction fetch is misaligned.

      Note that when IALIGN=16 (i.e., when the `C` or one of the `Zc*` extensions are implemented),
      it is impossible to generate a misaligned fetch, and so this parameter has no effect.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_LOAD_ACCESS_FAULT:
    description: |
      When true, `mtval` is written with the virtual address of a load when it causes a
      `LoadAccessFault`.

      WHen false, `mtval` is written with 0 when a load causes a `LoadAccessFault`.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_STORE_AMO_ACCESS_FAULT:
    description: |
      When true, `mtval` is written with the virtual address of a store when it causes a
      `StoreAmoAccessFault`.

      WHen false, `mtval` is written with 0 when a store causes a `StoreAmoAccessFault`.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_INSTRUCTION_ACCESS_FAULT:
    description: |
      When true, `mtval` is written with the virtual PC of an instructino when fetch causes an
      `InstructionAccessFault`.

      WHen false, `mtval` is written with 0 when an instruction fetch causes an
      `InstructionAccessFault`.
    schema:
      type: boolean
  REPORT_ENCODING_IN_MTVAL_ON_ILLEGAL_INSTRUCTION:
    description: |
      When true, `mtval` is written with the encoding of an instruction that causes an
      `IllegalInstruction` exception.

      When false `mtval` is written with 0 when an `IllegalInstruction` exception occurs.
    schema:
      type: boolean
  MTVAL_WIDTH:
    description: |
      The number of implemented bits in the `mtval` CSR.
      This is the CSR that may be written when a trap is taken into M-mode with exception-specific information to
      assist software in handling the trap (e.g., address associated with exception).

      Must be greater than or equal to _max_(`PHYS_ADDR_WIDTH`, `VA_SIZE`)
    schema:
      type: integer
      maximum: 64
  CONFIG_PTR_ADDRESS:
    description: |
      Physical address of the unified discovery configuration data structure.
      This address is reported in the `mconfigptr` CSR.
    schema:
      type: integer
      minimum: 0
      maximum: 0xFFFFFFFFFFFFFFFF
    when:
      name: Sm
      version: ">= 1.12.0"
  PMA_GRANULARITY:
    description: |
      log2 of the smallest supported PMA region.

      Generally, for systems with an MMU, should not be smaller than 12,
      as that would preclude caching PMP results in the TLB along with
      virtual memory translations
    schema:
      type: integer
      minimum: 2
      maximum: 66
  PHYS_ADDR_WIDTH:
    description: |
      Number of bits in the physical address space.
    schema:
      type: integer
      minimum: 1
      maximum: 64
  M_MODE_ENDIANNESS:
    description: |
      Endianness of data in M-mode. Can be one of:

      [separator="!"]
      !===
      h! little  ! M-mode data is always little endian
      h! big     ! M-mode data is always big endian
      h! dynamic ! M-mode data can be either little or big endian,
                   depending on the CSR field `mstatus.MBE`
      !===
    schema:
      type: string
      enum: [little, big, dynamic]
      # TODO: Only little available in Sm 1.11
  MISA_CSR_IMPLEMENTED:
    description: |
      Whether or not the `misa` CSR returns zero or a non-zero value.

      Possible values:

      true::
        The `misa` CSR returns a non-zero value.

      false::
        The `misa` CSR is read-only-0.
    schema:
      type: boolean
  MTVEC_MODES:
    description: |
      Modes supported by `mtvec.MODE`. If only one, it is assumed to be read-only with that value.
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1]
      minItems: 1
      maxItems: 2
      uniqueItems: true
  MTVEC_BASE_ALIGNMENT_DIRECT:
    description: |
      Byte alignment for `mtvec.BASE` when `mtvec.MODE` is Direct.

      Cannot be less than 4-byte alignment.
    schema:
      type: integer
      minimum: 4
      maximum: 64
      default: 4
      extra_validation: |
        # must be a power of two
        assert MTVEC_BASE_ALIGNMENT_DIRECT.to_s(2).count('1') == 1
  MTVEC_BASE_ALIGNMENT_VECTORED:
    description: |
      Byte alignment for `mtvec.BASE` when `mtvec.MODE` is Vectored.

      Cannot be less than 4-byte alignment.
    schema:
      type: integer
      minimum: 4
      default: 4
      extra_validation: |
        # must be a power of two
        assert MTVEC_BASE_ALIGNMENT_VECTORED.to_s(2).count('1') == 1
