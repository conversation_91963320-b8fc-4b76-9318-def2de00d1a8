# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zicntr/mcountinhibit.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mcountinhibit
long_name: Machine Counter Inhibit
address: 0x320
priv_mode: M
length: 32
description: |
  Bits to inhibit (stops counting) performance counters.

  The counter-inhibit register `mcountinhibit` is a *WARL* register that
  controls which of the hardware performance-monitoring counters
  increment. The settings in this register only control whether the
  counters increment; their accessibility is not affected by the setting
  of this register.

  When the CY, IR, or HPM__n__ bit in the `mcountinhibit` register is clear,
  the `mcycle`, `minstret`, or `mhpmcountern` register increments as usual.
  When the CY, IR, or HPM_n_ bit is set, the corresponding counter does
  not increment.

  The `mcycle` CSR may be shared between harts on the same core, in which
  case the `mcountinhibit.CY` field is also shared between those harts,
  and so writes to `mcountinhibit.CY` will be visible to those harts.

  If the `mcountinhibit` register is not implemented, the implementation
  behaves as though the register were set to zero.

  [NOTE]
  ====
  When the `mcycle` and `minstret` counters are not needed, it is desirable
  to conditionally inhibit them to reduce energy consumption. Providing a
  single CSR to inhibit all counters also allows the counters to be
  atomically sampled.

  Because the `mtime` counter can be shared between multiple cores, it
  cannot be inhibited with the `mcountinhibit` mechanism.
  ====

definedBy:
  anyOf:
    - name: Sm
    - name: Smhpm
fields:
  CY:
    location: 0
    definedBy: Sm
    description: When set, `mcycle.COUNT` stops counting in all privilege modes.
    type(): |
      return COUNTINHIBIT_EN[0] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[0] ? UNDEFINED_LEGAL : 0;
  IR:
    location: 2
    definedBy: Sm
    description: When set, `minstret.COUNT` stops counting in all privilege modes.
    type(): |
      return COUNTINHIBIT_EN[2] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[2] ? UNDEFINED_LEGAL : 0;
  HPM3:
    location: 3
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[3] == true"]
      When set, `hpmcounter3.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[3] == false"]
      Since hpmcounter3 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[3] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[3] ? UNDEFINED_LEGAL : 0;
  HPM4:
    location: 4
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[4] == true"]
      When set, `hpmcounter4.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[4] == false"]
      Since hpmcounter4 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[4] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[4] ? UNDEFINED_LEGAL : 0;
  HPM5:
    location: 5
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[5] == true"]
      When set, `hpmcounter5.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[5] == false"]
      Since hpmcounter5 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[5] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[5] ? UNDEFINED_LEGAL : 0;
  HPM6:
    location: 6
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[6] == true"]
      When set, `hpmcounter6.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[6] == false"]
      Since hpmcounter6 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[6] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[6] ? UNDEFINED_LEGAL : 0;
  HPM7:
    location: 7
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[7] == true"]
      When set, `hpmcounter7.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[7] == false"]
      Since hpmcounter7 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[7] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[7] ? UNDEFINED_LEGAL : 0;
  HPM8:
    location: 8
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[8] == true"]
      When set, `hpmcounter8.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[8] == false"]
      Since hpmcounter8 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[8] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[8] ? UNDEFINED_LEGAL : 0;
  HPM9:
    location: 9
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[9] == true"]
      When set, `hpmcounter9.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[9] == false"]
      Since hpmcounter9 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[9] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[9] ? UNDEFINED_LEGAL : 0;
  HPM10:
    location: 10
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[10] == true"]
      When set, `hpmcounter10.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[10] == false"]
      Since hpmcounter10 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[10] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[10] ? UNDEFINED_LEGAL : 0;
  HPM11:
    location: 11
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[11] == true"]
      When set, `hpmcounter11.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[11] == false"]
      Since hpmcounter11 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[11] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[11] ? UNDEFINED_LEGAL : 0;
  HPM12:
    location: 12
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[12] == true"]
      When set, `hpmcounter12.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[12] == false"]
      Since hpmcounter12 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[12] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[12] ? UNDEFINED_LEGAL : 0;
  HPM13:
    location: 13
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[13] == true"]
      When set, `hpmcounter13.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[13] == false"]
      Since hpmcounter13 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[13] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[13] ? UNDEFINED_LEGAL : 0;
  HPM14:
    location: 14
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[14] == true"]
      When set, `hpmcounter14.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[14] == false"]
      Since hpmcounter14 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[14] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[14] ? UNDEFINED_LEGAL : 0;
  HPM15:
    location: 15
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[15] == true"]
      When set, `hpmcounter15.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[15] == false"]
      Since hpmcounter15 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[15] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[15] ? UNDEFINED_LEGAL : 0;
  HPM16:
    location: 16
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[16] == true"]
      When set, `hpmcounter16.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[16] == false"]
      Since hpmcounter16 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[16] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[16] ? UNDEFINED_LEGAL : 0;
  HPM17:
    location: 17
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[17] == true"]
      When set, `hpmcounter17.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[17] == false"]
      Since hpmcounter17 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[17] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[17] ? UNDEFINED_LEGAL : 0;
  HPM18:
    location: 18
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[18] == true"]
      When set, `hpmcounter18.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[18] == false"]
      Since hpmcounter18 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[18] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[18] ? UNDEFINED_LEGAL : 0;
  HPM19:
    location: 19
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[19] == true"]
      When set, `hpmcounter19.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[19] == false"]
      Since hpmcounter19 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[19] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[19] ? UNDEFINED_LEGAL : 0;
  HPM20:
    location: 20
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[20] == true"]
      When set, `hpmcounter20.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[20] == false"]
      Since hpmcounter20 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[20] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[20] ? UNDEFINED_LEGAL : 0;
  HPM21:
    location: 21
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[21] == true"]
      When set, `hpmcounter21.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[21] == false"]
      Since hpmcounter21 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[21] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[21] ? UNDEFINED_LEGAL : 0;
  HPM22:
    location: 22
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[22] == true"]
      When set, `hpmcounter22.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[22] == false"]
      Since hpmcounter22 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[22] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[22] ? UNDEFINED_LEGAL : 0;
  HPM23:
    location: 23
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[23] == true"]
      When set, `hpmcounter23.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[23] == false"]
      Since hpmcounter23 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[23] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[23] ? UNDEFINED_LEGAL : 0;
  HPM24:
    location: 24
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[24] == true"]
      When set, `hpmcounter24.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[24] == false"]
      Since hpmcounter24 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[24] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[24] ? UNDEFINED_LEGAL : 0;
  HPM25:
    location: 25
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[25] == true"]
      When set, `hpmcounter25.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[25] == false"]
      Since hpmcounter25 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[25] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[25] ? UNDEFINED_LEGAL : 0;
  HPM26:
    location: 26
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[26] == true"]
      When set, `hpmcounter26.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[26] == false"]
      Since hpmcounter26 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[26] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[26] ? UNDEFINED_LEGAL : 0;
  HPM27:
    location: 27
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[27] == true"]
      When set, `hpmcounter27.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[27] == false"]
      Since hpmcounter27 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[27] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[27] ? UNDEFINED_LEGAL : 0;
  HPM28:
    location: 28
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[28] == true"]
      When set, `hpmcounter28.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[28] == false"]
      Since hpmcounter28 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[28] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[28] ? UNDEFINED_LEGAL : 0;
  HPM29:
    location: 29
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[29] == true"]
      When set, `hpmcounter29.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[29] == false"]
      Since hpmcounter29 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[29] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[29] ? UNDEFINED_LEGAL : 0;
  HPM30:
    location: 30
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[30] == true"]
      When set, `hpmcounter30.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[30] == false"]
      Since hpmcounter30 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[30] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[30] ? UNDEFINED_LEGAL : 0;
  HPM31:
    location: 31
    definedBy: Smhpm
    description: |
      [when="COUNTINHIBIT_EN[31] == true"]
      When set, `hpmcounter31.COUNT` stops counting in all privilege modes.

      [when="COUNTINHIBIT_EN[31] == false"]
      Since hpmcounter31 is not implemented, this field is read-only zero.
    type(): |
      return COUNTINHIBIT_EN[31] ? CsrFieldType::RW : CsrFieldType::RO;
    reset_value(): |
      return COUNTINHIBIT_EN[31] ? UNDEFINED_LEGAL : 0;
