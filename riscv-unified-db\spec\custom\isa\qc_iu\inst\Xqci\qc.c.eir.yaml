# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.c.eir
long_name: <PERSON>ore interrupts (Register)
description: |
  Globally restore interrupts, write bit 3 of rs1 to `mstatus.MIE` and `qc.mcause.MIE`.
  Instruction encoded in CI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqciint
assembly: " xs1"
base: 32
encoding:
  match: 0001-----0000110
  variables:
    - name: rs1
      location: 11-7
      not: 0
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  Bits<1> mie_val = (X[rs1] >> 3) & 1;
  CSR[mstatus].MIE = mie_val;
  XReg pre_qc_mcause = CSR[qc.mcause].sw_read();
  XReg pre_qc_mcause_masked = pre_qc_mcause & ~(32'b1<<26);
  CSR[qc.mcause].sw_write(pre_qc_mcause | (mie_val`<<26));
