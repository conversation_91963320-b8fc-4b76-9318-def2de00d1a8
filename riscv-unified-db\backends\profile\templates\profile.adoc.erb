<%= portfolio_design.include_erb("beginning.adoc.erb") %>

:revdate: <%= profile_release.ratification_date.nil? ? Date.today : profile_release.ratification_date %>
<%# unless profile_release.state == "ratified" -%>
:page-background-image: image:draft.png[opacity=20%]
<%# end -%>

= <%= profile_release.name %> Profile Release

// Preamble
<%=
 "TODO: revmark"
%>

[preface]
== Copyright and license information
This document is released under the
<% if profile_family.doc_license.url.nil? -%>
<%= profile_family.doc_license.name %>.
<% else -%>
<%= profile_family.doc_license.url %>[<%= profile_family.doc_license.name %>].
<% end -%>

<% if profile_release.ratification_date.nil? -%>
Copyright <%= Date.today.year %>
<% else -%>
Copyright <%= profile_release.ratification_date.year %>
<% end -%>
by <%= profile_family.company.name %>.

<% unless profile_release.contributors.nil? -%>
[preface]
== Acknowledgements

Contributors to the <%= profile_release.marketing_name %> Profile (in alphabetical order) include: +

<% profile_release.contributors.sort.each do |c| -%>
 * <%= c.name %> <<%= c.email %>> (<%= c.company %>)
<% end -%>

We express our gratitude to everyone that contributed to, reviewed or
improved this specification through their comments and questions.
<% end # no contributors -%>

== RISC-V Profiles

RISC-V was designed to provide a highly modular and extensible
instruction set, and includes a large and growing set of standard
extensions.  In addition, users may add their own custom
extensions. This flexibility can be used to highly optimize a
specialized design by including only the exact set of ISA features
required for an application, but the same flexibility also leads to a
combinatorial explosion in possible ISA choices.  Profiles specify a
much smaller common set of ISA choices that capture the most value for
most users, and which thereby enable the software community to focus
resources on building a rich software ecosystem with application and
operating system portability across different implementations.

NOTE: Another pragmatic concern is the long and unwieldy ISA strings
required to encode common sets of extensions, which will continue to
grow as new extensions are defined.

Each profile is built on a standard base ISA plus a set of mandatory
ISA extensions, and provides a small set of standard ISA options to
extend the mandatory components.  Profiles provide a convenient
shorthand for describing the ISA portions of hardware and software
platforms, and also guide the development of common software
toolchains shared by different platforms that use the same profile.
The intent is that the software ecosystem focus on supporting the
profiles' mandatory base and standard options, instead of attempting
to support every possible combination of individual extensions.
Similarly, hardware vendors should aim to structure their offerings
around standard profiles to increase the likelihood their designs will
have mainstream software support.

NOTE: Profiles are not intended to prohibit the use of combinations of
individual ISA extensions or the addition of custom extensions, which
can continue to be used for more specialized applications albeit
without the expectation of widespread software support or portability
between hardware platforms.

NOTE: As RISC-V evolves over time, the set of ISA features will grow,
and new platforms will be added that may need different profiles.  To
manage this evolution, RISC-V is adopting a model of regular annual
releases of new ISA profiles, following an ISA roadmap managed by the
RISC-V Technical Steering Committee.  The architecture profiles will
also be used for branding and to advertise compatibility with the
RISC-V standard.


=== Profiles versus Platforms

Profiles only describe ISA features, not a complete execution
environment.

A _software_ _platform_ is a specification for an execution
environment, in which software targeted for that software platform can
run.

A _hardware_ _platform_ is a specification for a hardware system
(which can be viewed as a physical realization of an execution
environment).

Both software and hardware platforms include specifications for many
features beyond details of the ISA used by RISC-V harts in the
platform (e.g., boot process, calling convention, behavior of
environment calls, discovery mechanism, presence of certain
memory-mapped hardware devices, etc.).  Architecture profiles factor
out ISA-specific definitions from platform definitions to allow ISA
profiles to be reused across different platforms, and to be used by
tools (e.g., compilers) that are common across many different
platforms.

A platform can add additional constraints on top of those in a
profile.  For example, mandating an extension that is a standard
option in the underlying profile, or constraining some
implementation-specific parameter in the profile to lie within a
certain range.

A platform cannot remove mandates or reduce other requirements in a
profile.

NOTE: A new profile should be proposed if existing profiles do not
match the needs of a new platform.


=== Components of a Profile

==== Profile Family

Every profile is a member of a _profile_ _family_.  A profile family
is a set of profiles that share the same base ISA but which vary in
highest-supported privilege mode.  The initial two types of family
are:

- generic unprivileged instructions (I)
- application processors running rich operating systems (A)

NOTE: More profile families may be added over time.

A profile family may be updated no more than annually, and the release
calendar year is treated as part of the profile family name.

Each profile family is described in more detail below.

==== Profile Privilege Mode

RISC-V has a layered architecture supporting multiple privilege modes,
and most RISC-V platforms support more than one privilege mode.
Software is usually written assuming a particular privilege mode
during execution.  For example, application code is written assuming
it will be run in user mode, and kernel code is written assuming it
will be run in supervisor mode.

NOTE: Software can be run in a mode different than the one for which
it was written. For example, privileged code using privileged ISA
features can be run in a user-mode execution environment, but will
then cause traps into the enclosing execution environment when
privileged instructions are executed.  This behavior might be
exploited, for example, to emulate a privileged execution environment
using a user-mode execution environment.

The profile for a privilege mode describes the ISA features for an
execution environment that has the eponymous privilege mode as the
most-privileged mode available, but also includes all supported
lower-privilege modes.  In general, available instructions vary by
privilege mode, and the behavior of RISC-V instructions can depend on
the current privilege mode.  For example, an S-mode profile includes
U-mode as well as S-mode and describes the behavior of instructions
when running in different modes in an S-mode execution environment,
such as how an `ecall` instruction in U-mode causes a contained trap
into an S-mode handler whereas an `ecall` in S-mode causes a requested
trap out to the execution environment.

A profile may specify that certain conditions will cause a requested
trap (such as an `ecall` made in the highest-supported privilege mode)
or fatal trap to the enclosing execution environment.  The profile
does not specify the behavior of the enclosing execution environment
iusually n handling requested or fatal traps.

NOTE: In particular, a profile does not specify the set of ECALLs
available in the outer execution environment.  This should be
documented in the appropriate binary interface to the outer execution
environment (e.g., Linux user ABI, or RISC-V SEE).

NOTE: In general, a profile can be implemented by an execution
environment using any hardware or software technique that provides
compatible functionality, including pure software emulation.

A profile does not specify any invisible traps.

NOTE: In particular, a profile does not constrain how invisible traps
to a more-privileged mode can be used to emulate profile features.

A more-privileged profile can always support running software to
implement a less-privileged profile from the same profile family.  For
example, a platform supporting the S-mode profile can run a
supervisor-mode operating system that provides user-mode execution
environments supporting the U-mode profile.

NOTE: Instructions in a U-mode profile, which are all executed in user
mode, have potentially different behaviors than instructions executed
in user mode in an S-mode profile.  For this reason, a U-mode profile
cannot be considered a subset of an S-mode profile.

==== Profile ISA Features

An architecture profile has a mandatory ratified base instruction set
(RV32I or RV64I for the current profiles).  The profile also includes
ratified ISA extensions placed into two categories:

. Mandatory
. Optional

As the name implies, _Mandatory_ _ISA_ _extensions_ are a required
part of the profile.  Implementations of the profile must provide
these.  The combination of the profile base ISA plus the mandatory ISA
extensions are termed the profile _mandates_, and software using the
profile can assume these always exist.

The _Optional_ category (also known as _options_) contains extensions
that may be added as options, and which are expected to be generally
supported as options by the software ecosystem for this profile.

NOTE: The level of "support" for an Optional extension will likely
vary greatly among different software components supporting a profile.
Users would expect that software claiming compatibility with a profile
would make use of any available supported options, but as a bare
minimum software should not report errors or warnings when supported
options are present in a system.

An optional extension may comprise many individually named and
ratified extensions but a profile option requires all constituent
extensions are present.  In particular, unless explicitly listed as a
profile option, individual extensions are not by themselves a profile
option even when required as part of a profile option.  For example,
the Zbkb extension is not by itself a profile option even though it is a
required component of the Zkn option.

NOTE: Profile optional extensions are intended to capture the
granularity at which the broad software ecosystem is expected to cope
with combinations of extensions.

All components of a ratified profile must themselves have been
ratified.

Platforms may provide a discovery mechanism to determine what optional
extensions are present.

Extensions that are not explicitly listed in the mandatory or optional
categories are termed _non-profile_ extensions, and are not considered
parts of the profile.  Some non-profile extensions can be added to an
implementation without conflicting with the mandatory or optional
components of a profile.  In this case, the implementation is still
compatible with the profile even though additional non-profile
extensions are present.  Other non-profile extensions added to an
implementation might alter or conflict with the behavior of the
mandatory or optional extensions in a profile, in which case the
implementation would not be compatible with the profile.

NOTE: Extensions that are released after a given profile is released
are by definition non-profile extensions.  For example, mandatory or
optional profile extensions for a new profile might be prototyped as
non-profile extensions on an earlier profile.

== <%= profile_family.name %> Profile Family

<%= profile_family.introduction %>

=== <%= profile_family.name %> Description

<%= profile_family.description %>

=== <%= profile_family.name %> Naming Scheme

<%= profile_family.naming_scheme %>

NOTE: Profile names are embeddable into RISC-V ISA naming strings.
This implies that there will be no standard ISA extension with a name
that matches the profile naming convention.  This allows tools that
process the RISC-V ISA naming string to parse and/or process a combined string.

=== <%= profile_family.name %> Profile Releases

The following profile releases are defined in this profile family:

<% profile_family.profile_releases.each do |profile_release| -%>
--
Name:: <%= profile_release.marketing_name %>
State:: <%= profile_release.state %>
<% unless profile_release.ratification_date.nil? -%>
Ratification date:: <%= profile_release.ratification_date %>
<% end # unless -%>
--
<% end # each profile_release -%>

== <%= profile_release.name %> Profile Release

<%= profile_release.introduction %>

<%= profile_release.marketing_name %> has <%= profile_release.in_scope_extensions.reduce(0) { |sum, ext| sum + ext.params.size } %>
associated implementation-defined parameters across all its defined profiles.

<% unless profile_release.description.nil? -%>
=== <%= profile_release.name %> Description

<%= profile_release.description %>
<% end # unless -%>

<% profile_release.profiles.each do |profile| -%>
=== <%= profile.marketing_name %> Profile

<%= profile.introduction %>

<% Udb::Presence.presence_types.each do |presence_type| -%>

==== <%= presence_type.capitalize %> Extensions

<%= profile.extensions_to_adoc(presence_type, 5).join("\n") %>
<% end -%>

<% unless profile.recommendations.empty? -%>
==== Recommendations

Recommendations are not strictly mandated but are included to guide implementers making design choices.

<% profile.recommendations.each do |recommendation| -%>
* <%= recommendation.text %>
<% end # each recommendation -%>
<% end # unless recommendations empty -%>

==== Implementation-dependencies

<%= profile.marketing_name %> has <%= profile.in_scope_extensions.reduce(0) { |sum, ext| sum + ext.params.size } %>
associated implementation-defined parameters.

<% profile.in_scope_extensions.each do |ext| -%>
<% ext.params.sort_by { |p| p.name }.each do |param| -%>
<%= param.name %>::
+
--
<%= param.desc %>
--
<% end -%>
<%  end -%>

<% end # each profile -%>

<<<
[appendix]
== Profile Comparisons

=== <%= profile_family.processor_kind %> Profile Releases

The <%= profile_family.processor_kind %> processor kind has <%= profile_family.profile_releases_matching_processor_kind.size %> processor
profile releases that reference a total of <%= profile_family.in_scope_extensions_matching_processor_kind.size %> extensions.

.Extension Presence
|===
| Extension | <%= profile_family.profile_releases_matching_processor_kind.map(&:marketing_name).join(" | ") %>

<% profile_family.in_scope_extensions_matching_processor_kind.sort_by(&:name).each do |ext| -%>
| <%= ext.name %> | <%= profile_family.profile_releases_matching_processor_kind.map { |profile_release| profile_release.extension_presence(ext.name) }.join(" | ") %>
<% end -%>
|===

=== <%= profile_family.marketing_name %> Profile Releases

The <%= profile_family.marketing_name %> Profile Family has <%= profile_family.profile_releases.size %> releases that
reference a total of <%= profile_family.in_scope_extensions.size %> extensions.

.Extension Presence
|===
| Extension | <%= profile_family.profile_releases.map(&:marketing_name).join(" | ") %>

<% profile_family.in_scope_extensions.each do |ext| -%>
| <%= ext.name %> | <%= profile_family.profile_releases.map { |profile_release| profile_release.extension_presence(ext.name) }.join(" | ") %>
<% end -%>
|===

=== <%= profile_release.marketing_name %> Profiles

The <%= profile_release.marketing_name %> Profile Release has <%= profile_release.profiles.size %> profiles that
reference a total of <%= profile_release.in_scope_extensions.size %> extensions.

[NOTE]
Extensions present in a profile are also present in higher-privileged profiles in the same profile release.

.Extension Presence
|===
| Extension | <%= profile_release.profiles.map(&:marketing_name).join(" | ") %>

<% profile_release.in_scope_extensions.each do |ext| -%>
| <%= ext.name %> | <%= profile_release.profiles.map { |profile| profile.extension_presence(ext.name) }.join(" | ") %>
<% end -%>
|===

<%= portfolio_design.include_erb("ext_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("inst_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("csr_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("idl_func_appendix.adoc.erb") %>
