# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Sstvecd
long_name: Direct exception vectoring
description: |
  `stvec.MODE` must be capable of holding the value 0 (Direct).
  When `stvec.MODE`=Direct, `stvec.BASE` must be capable of holding any valid
  four-byte-aligned address.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    url: https://github.com/riscv/riscv-profiles/releases/tag/v1.0
    repositories:
      - url: https://github.com/riscv/riscv-profiles
        branch: main
    contributors:
      - name: Krste <PERSON>
        company: SiFive, Inc.
    param_constraints:
      STVEC_MODE_DIRECT:
        schema:
          const: true
