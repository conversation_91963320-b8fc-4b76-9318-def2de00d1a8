# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fmv.w.x
long_name: Single-precision floating-point move from integer
description: |
  Moves the single-precision value encoded in IEEE 754-2008 standard encoding
  from the lower 32 bits of integer register `xs1` to the floating-point
  register `fd`. The bits are not modified in the transfer, and in particular,
  the payloads of non-canonical NaNs are preserved.
definedBy: F
assembly: fd, xs1
encoding:
  match: 111100000000-----000-----1010011
  variables:
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |
  check_f_ok($encoding);

  Bits<32> sp_value = X[xs1][31:0];

  if (implemented?(ExtensionName::D)) {
    f[fd] = nan_box<32, 64>(sp_value);
  } else {
    f[fd] = sp_value;
  }

  mark_f_state_dirty();

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let rs1_val_X            = X(rs1);
    let rd_val_S             = rs1_val_X [31..0];
    F(rd) = nan_box (rd_val_S);
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
