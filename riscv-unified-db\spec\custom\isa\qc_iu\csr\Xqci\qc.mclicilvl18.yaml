# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl18
long_name: IRQ Level 18
address: 0xbd2
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 144-151
fields:
  IRQ144:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ144 level
  IRQ145:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ145 level
  IRQ146:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ146 level
  IRQ147:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ147 level
  IRQ148:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ148 level
  IRQ149:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ149 level
  IRQ150:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ150 level
  IRQ151:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ151 level
