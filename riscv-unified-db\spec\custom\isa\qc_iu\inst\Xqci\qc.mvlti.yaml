# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.mvlti
long_name: Conditional move if less than (Immediate)
description: |
  Move `rs3` to `rd` if the value in `rs1` is less than value of `imm`.
  Instruction encoded in R4 instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcicm
base: 32
encoding:
  match: -----10----------100-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: imm
      location: 24-20
    - name: rs3
      location: 31-27
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, imm, xs3"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if ($signed(X[rs1]) < $signed(imm)) {
    X[rd] = X[rs3];
  }
