<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::AstNode
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::AstNode";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (A)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">AstNode</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::AstNode
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next">Idl::AstNode</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="AstNodeFuncs.html" title="Idl::AstNodeFuncs (module)">AstNodeFuncs</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb<span class="defines">,<br />
  lib/idl/ast.rb,<br /> lib/idl/passes/prune.rb,<br /> lib/idl/passes/gen_adoc.rb</span>
</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>set up a default</p>


  </div>
</div>
<div class="tags">
  

</div><div id="subclasses">
  <h2>Direct Known Subclasses</h2>
  <p class="children"><span class='object_link'><a href="ArrayLiteralAst.html" title="Idl::ArrayLiteralAst (class)">ArrayLiteralAst</a></span>, <span class='object_link'><a href="AryElementAccessAst.html" title="Idl::AryElementAccessAst (class)">AryElementAccessAst</a></span>, <span class='object_link'><a href="AryElementAssignmentAst.html" title="Idl::AryElementAssignmentAst (class)">AryElementAssignmentAst</a></span>, <span class='object_link'><a href="AryRangeAccessAst.html" title="Idl::AryRangeAccessAst (class)">AryRangeAccessAst</a></span>, <span class='object_link'><a href="AssignmentAst.html" title="Idl::AssignmentAst (class)">AssignmentAst</a></span>, <span class='object_link'><a href="BinaryExpressionAst.html" title="Idl::BinaryExpressionAst (class)">BinaryExpressionAst</a></span>, <span class='object_link'><a href="BitfieldAccessExpressionAst.html" title="Idl::BitfieldAccessExpressionAst (class)">BitfieldAccessExpressionAst</a></span>, <span class='object_link'><a href="BitfieldDefinitionAst.html" title="Idl::BitfieldDefinitionAst (class)">BitfieldDefinitionAst</a></span>, <span class='object_link'><a href="BitsCastAst.html" title="Idl::BitsCastAst (class)">BitsCastAst</a></span>, <span class='object_link'><a href="BuiltinTypeNameAst.html" title="Idl::BuiltinTypeNameAst (class)">BuiltinTypeNameAst</a></span>, <span class='object_link'><a href="BuiltinVariableAst.html" title="Idl::BuiltinVariableAst (class)">BuiltinVariableAst</a></span>, <span class='object_link'><a href="ConcatenationExpressionAst.html" title="Idl::ConcatenationExpressionAst (class)">ConcatenationExpressionAst</a></span>, <span class='object_link'><a href="ConditionalStatementAst.html" title="Idl::ConditionalStatementAst (class)">ConditionalStatementAst</a></span>, <span class='object_link'><a href="CsrFieldReadExpressionAst.html" title="Idl::CsrFieldReadExpressionAst (class)">CsrFieldReadExpressionAst</a></span>, <span class='object_link'><a href="CsrReadExpressionAst.html" title="Idl::CsrReadExpressionAst (class)">CsrReadExpressionAst</a></span>, <span class='object_link'><a href="CsrSoftwareReadAst.html" title="Idl::CsrSoftwareReadAst (class)">CsrSoftwareReadAst</a></span>, <span class='object_link'><a href="CsrSoftwareWriteAst.html" title="Idl::CsrSoftwareWriteAst (class)">CsrSoftwareWriteAst</a></span>, <span class='object_link'><a href="CsrWriteAst.html" title="Idl::CsrWriteAst (class)">CsrWriteAst</a></span>, <span class='object_link'><a href="DontCareLvalueAst.html" title="Idl::DontCareLvalueAst (class)">DontCareLvalueAst</a></span>, <span class='object_link'><a href="DontCareReturnAst.html" title="Idl::DontCareReturnAst (class)">DontCareReturnAst</a></span>, <span class='object_link'><a href="ElseIfAst.html" title="Idl::ElseIfAst (class)">ElseIfAst</a></span>, <span class='object_link'><a href="EnumDefinitionAst.html" title="Idl::EnumDefinitionAst (class)">EnumDefinitionAst</a></span>, <span class='object_link'><a href="EnumRefAst.html" title="Idl::EnumRefAst (class)">EnumRefAst</a></span>, <span class='object_link'><a href="ExecutionCommentAst.html" title="Idl::ExecutionCommentAst (class)">ExecutionCommentAst</a></span>, <span class='object_link'><a href="FieldNameAst.html" title="Idl::FieldNameAst (class)">FieldNameAst</a></span>, <span class='object_link'><a href="ForLoopAst.html" title="Idl::ForLoopAst (class)">ForLoopAst</a></span>, <span class='object_link'><a href="FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">FunctionBodyAst</a></span>, <span class='object_link'><a href="FunctionCallExpressionAst.html" title="Idl::FunctionCallExpressionAst (class)">FunctionCallExpressionAst</a></span>, <span class='object_link'><a href="FunctionDefAst.html" title="Idl::FunctionDefAst (class)">FunctionDefAst</a></span>, <span class='object_link'><a href="GlobalAst.html" title="Idl::GlobalAst (class)">GlobalAst</a></span>, <span class='object_link'><a href="GlobalWithInitializationAst.html" title="Idl::GlobalWithInitializationAst (class)">GlobalWithInitializationAst</a></span>, <span class='object_link'><a href="IdAst.html" title="Idl::IdAst (class)">IdAst</a></span>, <span class='object_link'><a href="IfAst.html" title="Idl::IfAst (class)">IfAst</a></span>, <span class='object_link'><a href="IfBodyAst.html" title="Idl::IfBodyAst (class)">IfBodyAst</a></span>, <span class='object_link'><a href="InstructionOperationAst.html" title="Idl::InstructionOperationAst (class)">InstructionOperationAst</a></span>, <span class='object_link'><a href="IntLiteralAst.html" title="Idl::IntLiteralAst (class)">IntLiteralAst</a></span>, <span class='object_link'><a href="IsaAst.html" title="Idl::IsaAst (class)">IsaAst</a></span>, <span class='object_link'><a href="MultiVariableDeclarationAst.html" title="Idl::MultiVariableDeclarationAst (class)">MultiVariableDeclarationAst</a></span>, <span class='object_link'><a href="NoopAst.html" title="Idl::NoopAst (class)">NoopAst</a></span>, <span class='object_link'><a href="ParenExpressionAst.html" title="Idl::ParenExpressionAst (class)">ParenExpressionAst</a></span>, <span class='object_link'><a href="PostDecrementExpressionAst.html" title="Idl::PostDecrementExpressionAst (class)">PostDecrementExpressionAst</a></span>, <span class='object_link'><a href="PostIncrementExpressionAst.html" title="Idl::PostIncrementExpressionAst (class)">PostIncrementExpressionAst</a></span>, <span class='object_link'><a href="ReplicationExpressionAst.html" title="Idl::ReplicationExpressionAst (class)">ReplicationExpressionAst</a></span>, <span class='object_link'><a href="ReturnStatementAst.html" title="Idl::ReturnStatementAst (class)">ReturnStatementAst</a></span>, <span class='object_link'><a href="SignCastAst.html" title="Idl::SignCastAst (class)">SignCastAst</a></span>, <span class='object_link'><a href="StatementAst.html" title="Idl::StatementAst (class)">StatementAst</a></span>, <span class='object_link'><a href="StatementSyntaxNode.html" title="Idl::StatementSyntaxNode (class)">StatementSyntaxNode</a></span>, <span class='object_link'><a href="TernaryOperatorExpressionAst.html" title="Idl::TernaryOperatorExpressionAst (class)">TernaryOperatorExpressionAst</a></span>, <span class='object_link'><a href="UnaryOperatorExpressionAst.html" title="Idl::UnaryOperatorExpressionAst (class)">UnaryOperatorExpressionAst</a></span>, <span class='object_link'><a href="UserTypeNameAst.html" title="Idl::UserTypeNameAst (class)">UserTypeNameAst</a></span>, <span class='object_link'><a href="VariableAssignmentAst.html" title="Idl::VariableAssignmentAst (class)">VariableAssignmentAst</a></span>, <span class='object_link'><a href="VariableDeclarationAst.html" title="Idl::VariableDeclarationAst (class)">VariableDeclarationAst</a></span>, <span class='object_link'><a href="VariableDeclarationWithInitializationAst.html" title="Idl::VariableDeclarationWithInitializationAst (class)">VariableDeclarationWithInitializationAst</a></span></p>
</div>
<h2>Defined Under Namespace</h2>
<p class="children">
  
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">InternalError</a></span>, <span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span>, <span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span>
    
  
</p>







  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#children-instance_method" title="#children (instance method)">#<strong>children</strong>  &#x21d2; Array&lt;AstNode&gt; </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#children-instance_method" title="Idl::AstNodeFuncs#children (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of children, or an empty array for a terminal.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#gen_adoc-instance_method" title="#gen_adoc (instance method)">#<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#internal_error-instance_method" title="#internal_error (instance method)">#<strong>internal_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#internal_error-instance_method" title="Idl::AstNodeFuncs#internal_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise an internal error.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#nodes-instance_method" title="#nodes (instance method)">#<strong>nodes</strong>  &#x21d2; Array&lt;AstNode&gt; </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#nodes-instance_method" title="Idl::AstNodeFuncs#nodes (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>An array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#print_ast-instance_method" title="#print_ast (instance method)">#<strong>print_ast</strong>(indent = 0, indent_size: 2, io: $stdout)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#print_ast-instance_method" title="Idl::AstNodeFuncs#print_ast (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>pretty print the AST rooted at this node.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#prune-instance_method" title="#prune (instance method)">#<strong>prune</strong>(_symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_idl-instance_method" title="#to_idl (instance method)">#<strong>to_idl</strong>  &#x21d2; String </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#to_idl-instance_method" title="Idl::AstNodeFuncs#to_idl (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  <span class="abstract note title">abstract</span>
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return valid IDL representation of the node (and its subtree).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#type_check-instance_method" title="Idl::AstNodeFuncs#type_check (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  <span class="abstract note title">abstract</span>
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check this node and all children.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_error-instance_method" title="#type_error (instance method)">#<strong>type_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#type_error-instance_method" title="Idl::AstNodeFuncs#type_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise a type error.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#unindent-instance_method" title="#unindent (instance method)">#<strong>unindent</strong>(s)  &#x21d2; String </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#unindent-instance_method" title="Idl::AstNodeFuncs#unindent (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>unindent a multiline string, getting rid of all common leading whitespace (like &lt;&lt;~ heredocs).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#value_error-instance_method" title="#value_error (instance method)">#<strong>value_error</strong>(reason)  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="AstNodeFuncs.html#value_error-instance_method" title="Idl::AstNodeFuncs#value_error (method)">AstNodeFuncs</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>raise a value error, indicating that the value is not known at compile time.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="children-instance_method">
  
    #<strong>children</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#children-instance_method" title="Idl::AstNodeFuncs#children (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns list of children, or an empty array for a terminal.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>list of children, or an empty array for a terminal</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="gen_adoc-instance_method">
  
    #<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


4
5
6
7
8
9
10
11
12
13</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/gen_adoc.rb', line 4</span>

<span class='kw'>def</span> <span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='id identifier rubyid_indent'>indent</span> <span class='op'>=</span> <span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span> <span class='int'>2</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_adoc'>adoc</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_puts'>puts</span> <span class='kw'>self</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='kw'>if</span> <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
  <span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='kw'>next</span> <span class='kw'>unless</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="" title="Idl::AstNode (class)">AstNode</a></span></span><span class='rparen'>)</span>

    <span class='id identifier rubyid_adoc'>adoc</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='id identifier rubyid_indent'>indent</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_adoc'>adoc</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='op'>|</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'> </span><span class='tstring_end'>&#39;</span></span><span class='op'>*</span><span class='id identifier rubyid_indent'>indent</span><span class='embexpr_end'>}</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_a'>a</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="internal_error-instance_method">
  
    #<strong>internal_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#internal_error-instance_method" title="Idl::AstNodeFuncs#internal_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise an internal error</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="nodes-instance_method">
  
    #<strong>nodes</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#nodes-instance_method" title="Idl::AstNodeFuncs#nodes (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns an array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST).</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="" title="Idl::AstNode (class)">AstNode</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>an array of AST sub nodes (notably, excludes anything, like whitespace, that wasn’t subclassed to AST)</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="print_ast-instance_method">
  
    #<strong>print_ast</strong>(indent = 0, indent_size: 2, io: $stdout)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#print_ast-instance_method" title="Idl::AstNodeFuncs#print_ast (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>pretty print the AST rooted at this node</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>indent</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>0</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The starting indentation, in # of spaces</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>indent_size</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>2</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>The extra indentation applied to each level of the tree</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>io</span>
      
      
        <span class='type'>(<tt>IO</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>$stdout</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Where to write the output</p>
</div>
      
    </li>
  
</ul>


</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="prune-instance_method">
  
    #<strong>prune</strong>(_symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


43
44
45</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/prune.rb', line 43</span>

<span class='kw'>def</span> <span class='id identifier rubyid_prune'>prune</span><span class='lparen'>(</span><span class='id identifier rubyid__symtab'>_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_clone'>clone</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_idl-instance_method">
  
    #<strong>to_idl</strong>  &#x21d2; <tt>String</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#to_idl-instance_method" title="Idl::AstNodeFuncs#to_idl (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    <div class="note abstract">
  <strong>This method is abstract.</strong>
  <div class='inline'></div>
</div>

<p>Return valid IDL representation of the node (and its subtree)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL code for the node</p>
</div>
      
    </li>
  
</ul>
<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt>NotImplementedError</tt>)</span>
      
      
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#type_check-instance_method" title="Idl::AstNodeFuncs#type_check (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    <div class="note abstract">
  <strong>This method is abstract.</strong>
  <div class='inline'></div>
</div>
<p class="note returns_void">This method returns an undefined value.</p>
<p>type check this node and all children</p>

<p>Calls to #type and/or #value may depend on type_check being called first with the same symtab. If not, those functions may raise an AstNode::InternalError</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is a type error</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is an internal compiler error during type check</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_error-instance_method">
  
    #<strong>type_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#type_error-instance_method" title="Idl::AstNodeFuncs#type_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise a type error</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="unindent-instance_method">
  
    #<strong>unindent</strong>(s)  &#x21d2; <tt>String</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#unindent-instance_method" title="Idl::AstNodeFuncs#unindent (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>unindent a multiline string, getting rid of all common leading whitespace (like &lt;&lt;~ heredocs)</p>

<p>borrowed from <a href="https://stackoverflow.com/questions/33527064/multiline-strings-with-no-indent">stackoverflow.com/questions/33527064/multiline-strings-with-no-indent</a></p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>s</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A string (presumably with newlines)</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Unindented string</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="value_error-instance_method">
  
    #<strong>value_error</strong>(reason)  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="AstNodeFuncs.html#value_error-instance_method" title="Idl::AstNodeFuncs#value_error (method)">AstNodeFuncs</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>raise a value error, indicating that the value is not known at compile time</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>reason</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">AstNode::ValueError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>always</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>