# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvksed
long_name: "ShangMi Suite: SM4 Block Cipher"
description: |
  Instructions for accelerating encryption, decryption and key-schedule functions of the SM4 block cipher.

  The SM4 block cipher is specified in 32907-2016: {SM4} Block Cipher Algorithm (GB/T 32907-2016: SM4 Block Cipher Algorithm, 2016)

  There are other various sources available that describe the SM4 block cipher. While not the final version of the standard, RFC 8998 ShangMi (SM) Cipher Suites for TLS 1.3 is useful and easy to access.

  All of these instructions work on 128-bit element groups comprised of four 32-bit elements.

  To help avoid side-channel timing attacks, these instructions shall be implemented with data-independent timing.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
