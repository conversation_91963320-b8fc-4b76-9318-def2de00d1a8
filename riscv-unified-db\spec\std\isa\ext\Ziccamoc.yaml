# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ziccamoc
long_name: Cacheable and coherent PMAs provide `AMOCASQ` level PMA support
type: unprivileged
description: |
  Main memory regions with both the cacheability and coherence PMAs
  must provide `AMOCASQ` level PMA support.

  [NOTE]
  Ziccamoc is a new RVA23 profile-defined extension that ensures
  Compare and Swap instructions are properly supported in main memory
  regions.  The extension will be added to the PMA section of the
  privileged architecture manual.
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
