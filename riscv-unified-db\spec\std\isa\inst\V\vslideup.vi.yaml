# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: vslideup.vi
long_name: No synopsis available
description: |
  No description available.
definedBy: V
assembly: vd, vs2, imm, vm
encoding:
  match: 001110-----------011-----1010111
  variables:
    - name: vm
      location: 25-25
    - name: vs2
      location: 24-20
    - name: imm
      location: 19-15
    - name: vd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let SEW_pow  = get_sew_pow();
    let SEW      = get_sew();
    let LMUL_pow = get_lmul_pow();
    let VLEN_pow = get_vlen_pow();
    let num_elem = get_num_elem(LMUL_pow, SEW);

    if illegal_normal(vd, vm) then { handle_illegal(); return RETIRE_FAIL };

    let 'n = num_elem;
    let 'm = SEW;

    let vm_val  : vector('n, dec, bool)     = read_vmask(num_elem, vm, 0b00000);
    let imm_val : nat                       = unsigned(zero_extend(sizeof(xlen), simm));
    let vs2_val : vector('n, dec, bits('m)) = read_vreg(num_elem, SEW, LMUL_pow, vs2);
    let vd_val  : vector('n, dec, bits('m)) = read_vreg(num_elem, SEW, LMUL_pow, vd);
    result      : vector('n, dec, bits('m)) = undefined;
    mask        : vector('n, dec, bool)     = undefined;

    (result, mask) = init_masked_result(num_elem, SEW, LMUL_pow, vd_val, vm_val);

    foreach (i from 0 to (num_elem - 1)) {
      if mask[i] then {
        result[i] = match funct6 {
          VI_VSLIDEUP    => {
                              if (vs2 == vd) then { handle_illegal(); return RETIRE_FAIL };
                              if i >= imm_val then vs2_val[i - imm_val] else vd_val[i]
                            },
          VI_VSLIDEDOWN  => {
                              let VLMAX = int_power(2, LMUL_pow + VLEN_pow - SEW_pow);
                              assert(VLMAX > 0 & VLMAX <= 'n);
                              if i + imm_val < VLMAX then vs2_val[i + imm_val] else zeros()
                            },
          VI_VRGATHER    => {
                              if (vs2 == vd) then { handle_illegal(); return RETIRE_FAIL };
                              let VLMAX = int_power(2, LMUL_pow + VLEN_pow - SEW_pow);
                              assert(VLMAX > 0 & VLMAX <= 'n);
                              if imm_val < VLMAX then vs2_val[imm_val] else zeros()
                            }
        }
      }
    };

    write_vreg(num_elem, SEW, LMUL_pow, vd, result);
    vstart = zeros();
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
