# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: D
type: unprivileged
long_name: Double-precision floating-point
versions:
  - version: "2.2.0"
    state: ratified
    ratification_date: 2019-12
    changes:
      - Define NaN-boxing scheme, changed definition of FMAX and FMIN
    implies:
      name: F
      version: "2.2.0"
description: |
  The `D` extension adds
  double-precision floating-point computational instructions compliant
  with the https://ieeexplore.ieee.org/document/4610935[IEEE 754-2008] arithmetic standard. The D extension depends on
  the base single-precision instruction subset `F`.
  (((double-precision, floating point)))
  (((floating point, double precision)))

  = D Register State

  The D extension widens the 32 floating-point registers, `f0-f31`, to
  64 bits (FLEN=64 in <<fprs>>. The `f` registers can
  now hold either 32-bit or 64-bit floating-point values as described
  below in <<nanboxing>>.

  [NOTE]
  ====
  FLEN can be 32, 64, or 128 depending on which of the F, D, and Q
  extensions are supported. There can be up to four different
  floating-point precisions supported, including H, F, D, and Q.
  ====
  (((floating-point, supported precisions)))

  [[nanboxing]]
  = NaN Boxing of Narrower Values

  When multiple floating-point precisions are supported, then valid values
  of narrower _n_-bit types, _n_<FLEN, are represented in the lower _n_ bits of an FLEN-bit NaN value, in a process termed NaN-boxing. The upper bits of a valid NaN-boxed value must be all 1s. Valid NaN-boxed _n_-bit values
  therefore appear as negative quiet NaNs (qNaNs) when viewed as any wider
  _m_-bit value, _n_ < _m_ &#8804; FLEN. Any operation that writes a narrower result to an 'f' register must write all 1s to the uppermost FLEN-_n_ bits to yield a legal NaN-boxedvalue.
  (((floating-point, requirements)))

  [NOTE]
  ====
  Software might not know the current type of data stored in a
  floating-point register but has to be able to save and restore the
  register values, hence the result of using wider operations to transfer
  narrower values has to be defined. A common case is for callee-saved
  registers, but a standard convention is also desirable for features
  including varargs, user-level threading libraries, virtual machine
  migration, and debugging.
  ====

  Floating-point _n_-bit transfer operations move external
  values held in IEEE standard formats into and out of the `f` registers,
  and comprise floating-point loads and stores (FL__n__/FS__n__) and floating-point move instructions (FMV._n_.X/FMV.X._n_). A narrower _n_-bit transfer, _n_<FLEN, into the `f` registers will create a valid NaN-boxed value. A narrower
  _n_-bit transfer out of the floating-point registers will
  transfer the lower _n_ bits of the register ignoring the
  upper FLEN-_n_ bits.

  Apart from transfer operations described in the previous paragraph, all
  other floating-point operations on narrower __n__-bit
  operations, _n_<FLEN, check if the input operands are
  correctly NaN-boxed, i.e., all upper FLEN-_n_ bits are 1. If
  so, the _n_ least-significant bits of the input are used as
  the input value, otherwise the input value is treated as an
  _n_-bit canonical NaN.

  [TIP]
  ====
  Earlier versions of this document did not define the behavior of feeding
  the results of narrower or wider operands into an operation, except to
  require that wider saves and restores would preserve the value of a
  narrower operand. The new definition removes this
  implementation-specific behavior, while still accommodating both
  non-recoded and recoded implementations of the floating-point unit. The
  new definition also helps catch software errors by propagating NaNs if
  values are used incorrectly.

  Non-recoded implementations unpack and pack the operands to IEEE
  standard format on the input and output of every floating-point
  operation. The NaN-boxing cost to a non-recoded implementation is
  primarily in checking if the upper bits of a narrower operation
  represent a legal NaN-boxed value, and in writing all 1s to the upper
  bits of a result.

  Recoded implementations use a more convenient internal format to
  represent floating-point values, with an added exponent bit to allow all
  values to be held normalized. The cost to the recoded implementation is
  primarily the extra tagging needed to track the internal types and sign
  bits, but this can be done without adding new state bits by recoding
  NaNs internally in the exponent field. Small modifications are needed to
  the pipelines used to transfer values in and out of the recoded format,
  but the datapath and latency costs are minimal. The recoding process has
  to handle shifting of input subnormal values for wide operands in any
  case, and extracting the NaN-boxed value is a similar process to
  normalization except for skipping over leading-1 bits instead of
  skipping over leading-0 bits, allowing the datapath muxing to be shared.
  ====
params:
  MUTABLE_MISA_D:
    description: |
      Indicates whether or not the `D` extension can be disabled with the `misa.D` bit.
    schema:
      type: boolean
