<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::IsaAst
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::IsaAst";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (I)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">IsaAst</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::IsaAst
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></li>
          
            <li class="next">Idl::IsaAst</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>top-level AST node</p>


  </div>
</div>
<div class="tags">
  

</div>






  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#bitfields-instance_method" title="#bitfields (instance method)">#<strong>bitfields</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#children-instance_method" title="#children (instance method)">#<strong>children</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#enums-instance_method" title="#enums (instance method)">#<strong>enums</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>return array of EnumAsts.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#functions-instance_method" title="#functions (instance method)">#<strong>functions</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#globals-instance_method" title="#globals (instance method)">#<strong>globals</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#instructions-instance_method" title="#instructions (instance method)">#<strong>instructions</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  
  
  
  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="bitfields-instance_method">
  
    #<strong>bitfields</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


534
535
536
537
538
539
540
541
542</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 534</span>

<span class='kw'>def</span> <span class='id identifier rubyid_bitfields'>bitfields</span>
  <span class='kw'>return</span> <span class='ivar'>@bitfields</span> <span class='kw'>unless</span> <span class='ivar'>@bitfields</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@bitfields</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@bitfields</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="BitfieldDefinitionAst.html" title="Idl::BitfieldDefinitionAst (class)">BitfieldDefinitionAst</a></span></span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@bitfields</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="children-instance_method">
  
    #<strong>children</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


507
508
509</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 507</span>

<span class='kw'>def</span> <span class='id identifier rubyid_children'>children</span>
  <span class='id identifier rubyid_enums'>enums</span> <span class='op'>+</span> <span class='id identifier rubyid_bitfields'>bitfields</span> <span class='op'>+</span> <span class='id identifier rubyid_functions'>functions</span> <span class='op'>+</span> <span class='id identifier rubyid_globals'>globals</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="enums-instance_method">
  
    #<strong>enums</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>return array of EnumAsts</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


523
524
525
526
527
528
529
530
531
532</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 523</span>

<span class='kw'>def</span> <span class='id identifier rubyid_enums'>enums</span>
  <span class='kw'>return</span> <span class='ivar'>@enums</span> <span class='kw'>unless</span> <span class='ivar'>@enums</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@enums</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@enums</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="EnumDefinitionAst.html" title="Idl::EnumDefinitionAst (class)">EnumDefinitionAst</a></span></span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@enums</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="functions-instance_method">
  
    #<strong>functions</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


544
545
546
547
548
549
550
551
552
553</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 544</span>

<span class='kw'>def</span> <span class='id identifier rubyid_functions'>functions</span>
  <span class='kw'>return</span> <span class='ivar'>@functions</span> <span class='kw'>unless</span> <span class='ivar'>@functions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@functions</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@functions</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="FunctionDefAst.html" title="Idl::FunctionDefAst (class)">FunctionDefAst</a></span></span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@functions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="globals-instance_method">
  
    #<strong>globals</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


511
512
513
514
515
516
517
518
519
520</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 511</span>

<span class='kw'>def</span> <span class='id identifier rubyid_globals'>globals</span>
  <span class='kw'>return</span> <span class='ivar'>@globals</span> <span class='kw'>unless</span> <span class='ivar'>@globals</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@globals</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@globals</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="GlobalWithInitializationAst.html" title="Idl::GlobalWithInitializationAst (class)">GlobalWithInitializationAst</a></span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="GlobalAst.html" title="Idl::GlobalAst (class)">GlobalAst</a></span></span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@globals</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="instructions-instance_method">
  
    #<strong>instructions</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


564
565
566
567
568
569
570
571
572</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 564</span>

<span class='kw'>def</span> <span class='id identifier rubyid_instructions'>instructions</span>
  <span class='kw'>return</span> <span class='ivar'>@instructions</span> <span class='kw'>unless</span> <span class='ivar'>@instructions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@instructions</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@instructions</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span> <span class='kw'>if</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>InstructionDefinitionAst</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@instructions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


555
556
557
558
559
560
561
562</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 555</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_definitions'>definitions</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='kw'>next</span> <span class='kw'>unless</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="EnumDefinitionAst.html" title="Idl::EnumDefinitionAst (class)">EnumDefinitionAst</a></span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="BitfieldDefinitionAst.html" title="Idl::BitfieldDefinitionAst (class)">BitfieldDefinitionAst</a></span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="FunctionDefAst.html" title="Idl::FunctionDefAst (class)">FunctionDefAst</a></span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="GlobalAst.html" title="Idl::GlobalAst (class)">GlobalAst</a></span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="GlobalWithInitializationAst.html" title="Idl::GlobalWithInitializationAst (class)">GlobalWithInitializationAst</a></span></span><span class='rparen'>)</span>

    <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>level = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_levels'>levels</span> <span class='op'>==</span> <span class='int'>1</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:45 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>