All components of the RVCP share the following naming scheme:

  Format: <name>[v<version>]

Where:

* Left & right square braces denote optional.
* Less-than & greater-than signs just separate fields (i.e., they aren't present in the CRD name).
* <name> identifies the type of RISC-V standard (processor, non-processor system IP, or platform) along with
  any other information required to identify the variant of that standard.
* <version> identifies a particular release
** Format is <major>[.<minor>[.<patch>]]
** Inspired by semantic versioning scheme (https://semver.org/) but doesn't follow it exactly

The rules for updating <major>, <minor>, and <patch> are defined by the type of RVCP component.
However, the follow these general guidelines:

* A <major> release of 0 is used for pre-release versions and release versions start with 1.
* The <major> release number is updated when mandatory changes are made.
* The <minor> release number is updated when optional changes are made.
* The <patch> release number is updated for documentation fixes/improvements that don't affect
  certificates already obtained for a particular implementation.
