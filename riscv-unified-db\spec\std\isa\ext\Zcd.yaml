# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zcd
long_name: Compressed instructions for double precision floating point
description: |
  Zcd is the existing set of compressed double precision floating point loads and stores:
  `c.fld`, `c.fldsp`, `c.fsd`, `c.fsdsp`.

type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-04
    repositories:
      - url: https://github.com/riscv/riscv-code-size-reduction
        branch: main
    contributors:
      - name: <PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON> Ness
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
      - name: sinan
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON><PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
    requires:
      allOf:
        - anyOf:
          - { name: <PERSON><PERSON>, version: "= 1.0.0" }
          - { name: C, version: "~> 2.0.0" }
        - { name: D, version: "~> 2.2.0" }
