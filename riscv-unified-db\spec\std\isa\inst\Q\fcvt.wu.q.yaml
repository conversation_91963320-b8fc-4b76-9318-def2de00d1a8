# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fcvt.wu.q
long_name: Floating-point Convert Unsigned Quad-precision to Word
description:
  - id: inst-fcvt.wu.q-behaviour
    normative: false
    text: |
      `fcvt.wu.q` converts a quad-precision floating-point number to a 32-bit unsigned integer.
definedBy: Q
assembly: xd, fs1, rm
encoding:
  match: 110001100001-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
