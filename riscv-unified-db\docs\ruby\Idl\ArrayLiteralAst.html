<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::ArrayLiteralAst
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::ArrayLiteralAst";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (A)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">ArrayLiteralAst</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::ArrayLiteralAst
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></li>
          
            <li class="next">Idl::ArrayLiteralAst</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="Rvalue.html" title="Idl::Rvalue (module)">Rvalue</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb</dd>
  </dl>
  
</div>








  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#element_nodes-instance_method" title="#element_nodes (instance method)">#<strong>element_nodes</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_idl-instance_method" title="#to_idl (instance method)">#<strong>to_idl</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type-instance_method" title="#type (instance method)">#<strong>type</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check this node and all children.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#value-instance_method" title="#value (instance method)">#<strong>value</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#values-instance_method" title="#values (instance method)">#<strong>values</strong>(symtab)  &#x21d2; Array&lt;Integer&gt;, ... </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="Rvalue.html#values-instance_method" title="Idl::Rvalue#values (method)">Rvalue</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return a complete list of possible compile-time-known values of the node, or raise a ValueError if the full list cannot be determined.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  
  
  
  
  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="element_nodes-instance_method">
  
    #<strong>element_nodes</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1948
1949
1950</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1948</span>

<span class='kw'>def</span> <span class='id identifier rubyid_element_nodes'>element_nodes</span>
  <span class='lbracket'>[</span><span class='id identifier rubyid_first'>first</span><span class='rbracket'>]</span> <span class='op'>+</span> <span class='id identifier rubyid_rest'>rest</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:expression</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_idl-instance_method">
  
    #<strong>to_idl</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1971</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1971</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_idl'>to_idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>[</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:to_idl</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_join'>join</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>,</span><span class='tstring_end'>&#39;</span></span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>]</span><span class='tstring_end'>&quot;</span></span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type-instance_method">
  
    #<strong>type</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1963
1964
1965</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1963</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:array</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='comma'>,</span> <span class='label'>sub_type:</span> <span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>type check this node and all children</p>

<p>Calls to <span class='object_link'><a href="#type-instance_method" title="Idl::ArrayLiteralAst#type (method)">#type</a></span> and/or <span class='object_link'><a href="#value-instance_method" title="Idl::ArrayLiteralAst#value (method)">#value</a></span> may depend on type_check being called first with the same symtab. If not, those functions may raise an AstNode::InternalError</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is a type error</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is an internal compiler error during type check</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1953
1954
1955
1956
1957
1958
1959
1960
1961</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1953</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_node'>node</span><span class='op'>|</span>
    <span class='id identifier rubyid_node'>node</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='kw'>unless</span> <span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='period'>.</span><span class='id identifier rubyid_all?'>all?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_equal_to?'>equal_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
    <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Array elements must be identical</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="value-instance_method">
  
    #<strong>value</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1967
1968
1969</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1967</span>

<span class='kw'>def</span> <span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_element_nodes'>element_nodes</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="values-instance_method">
  
    #<strong>values</strong>(symtab)  &#x21d2; <tt>Array&lt;Integer&gt;</tt>, ... 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="Rvalue.html#values-instance_method" title="Idl::Rvalue#values (method)">Rvalue</a></span>
    </span>
  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return a complete list of possible compile-time-known values of the node, or raise a ValueError if the full list cannot be determined</p>

<p>For most AstNodes, this will just be a single-entry array</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The context for the evaulation</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;Integer&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The complete list of compile-time-known values, when they are integral</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;Boolean&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The complete list of compile-time-known values, when they are booleans</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">AstNode::ValueError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if the list of values is not knowable at compile time</p>
</div>
      
    </li>
  
</ul>

</div>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:45 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>