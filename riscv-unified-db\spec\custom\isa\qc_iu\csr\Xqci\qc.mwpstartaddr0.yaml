# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mwpstartaddr0
long_name: Watchpoint start address for region 0
address: 0x7d0
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Watchpoint start address for region 0
fields:
  ADDR:
    type: RW
    reset_value: 0
    location: 31-0
    description: Watchpoint start address
