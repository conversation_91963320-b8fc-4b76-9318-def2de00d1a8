// THIS FILE IS AUTOGENERATED

#include <string>

#include "udb/cpp_exceptions.hpp"
#include "udb/enum.hxx"

<%- enums = cfg_arch.global_ast.enums -%>
<%- symtab = cfg_arch.symtab -%>

namespace udb {
  <%- enums.each do |enum| -%>
  <%- element_names = enum.type(symtab).element_names -%>
  <%- element_values = enum.type(symtab).element_values -%>
  const std::string to_s(const <%= enum.name %>& value) {
    switch(value.value()) {
      <%- seen = [] -%>
      <%- element_values.each_index do |idx| -%>
      <%- next if seen.include?(element_values[idx]) -%>
      case <%= element_values[idx] %>: return "<%= element_names[idx] %>";
      <%- seen << element_values[idx] -%>
      <%- end -%>
      default: return "Unknown";
    }
  }

  const <%= enum.name %> <%= enum.name %>::from_s(const std::string& value) {
    <%- element_values.each_index do |idx| -%>
    <%= idx.zero? ? "" : "else" %> if (value == "<%= element_names[idx] %>") {
      return <%= element_values[idx] %>;
    }
    <%- end -%>
    else {
      throw DbError("Bad enum value");
    }
  }

  <%- end -%>
}
