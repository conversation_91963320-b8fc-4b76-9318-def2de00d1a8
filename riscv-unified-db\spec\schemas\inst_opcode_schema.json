{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Instruction Opcode Schema", "description": "Schema for instruction opcode definitions", "type": "object", "required": ["$schema", "kind", "data"], "properties": {"$schema": {"const": "inst_opcode_schema.json#"}, "kind": {"const": "instruction_opcode"}, "name": {"type": "string"}, "data": {"type": "object", "additionalProperties": false, "properties": {"location": {"$ref": "schema_defs.json#/$defs/field_location"}, "value": {"$ref": "schema_defs.json#/$defs/integer"}, "display_name": {"type": "string"}, "$parent_of": {"$ref": "schema_defs.json#/$defs/ref_url_list"}}}}}