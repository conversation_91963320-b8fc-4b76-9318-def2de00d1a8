# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/proc_cert_model_schema.json

$schema: proc_cert_model_schema.json#
kind: processor certificate model
name: MC100-32
long_name: Basic 32-bit Microcontroller Certificate
class:
  $ref: proc_cert_class/MC.yaml#

# Semantic versions within the model
versions:
  - version: "1.0.0"

# MXLEN used by rakefile
base: 32

in_scope_priv_modes:
  - M

# History of this certificate.
revision_history:
  - revision: "0.9.0"
    date: "2025-02-06"
    changes:
      - |
        Provided to CSC Microcontroller WG for review given the following list of known issues:
          ** abc
          ** def
  - revision: "0.8.0"
    date: "2025-01-19"
    changes:
      - Updated so that content can apply equally to all certificate-related documents
        such as CRDs (Certification Requirement Documents) and CTPs (Certification Test Plans).
  - revision: "0.7.0"
    date: "2024-07-29"
    changes:
      - First version after moving non-microcontroller content in this document to a new document
        called "RISC-V CRDs (Certification Requirement Documents)"
      - Change MC100 Unpriv ISA spec from
        "https://riscv.org/wp-content/uploads/2016/06/riscv-spec-v2.1.pdf[riscv-spec-v2.1], May 31,
        2016" to https://github.com/riscv/riscv-isa-manual/releases/tag/Ratified-IMAFDQC since the
        former isn't ratified by the latter is the oldest ratified version.
      - Added requirements for WFI instruction
      - Added requirements related to msip memory-mapped register
  - revision: "0.6.0"
    date: "2024-07-11"
    changes:
      - Supporting multiple MC versions to support customers wanting to certify existing microcontrollers not using the latest version of ratified standards.
      - Changed versioning scheme to use major.minor.patch instead of 3-digit major & minor.
      - Added a table showing the mapping from MC version to ISA manuals.
      - Reluctantly made interrupts OUT OF SCOPE for MC100 since only the CLINT interrupt controller
        was ratified at that time and isn't anticipated to be the interrupt controller used by MC100 implementations.
      - Clarified MANDATORY behaviors for mie and mip CSRs
      - Removed canonical discovery recipe because the OPT-* options directly inform the certification
        tests and certification reference model of the status of the various options. Also, canonical
        discovery recipes (e.g., probing for CLIC) violate the certification approach of avoiding writing
        potentially illegal values to CSR fields.
      - Added more options for interrupts
      - Moved non-microcontroller content in this document to a new document called "RISC-V Certification Plans"
  - revision: "0.5.0"
    date: "2024-06-03"
    changes:
      - Renamed to "RISC-V Microcontroller Certification Plan" based on Jason's recommendation
      - Added mvendorid, marchid, mimpid, and mhardid read-only priv CSRs because Allen pointed out
        these are mandatory in M-mode v1.13 (probably older versions too, haven't looked yet).
      - Added table showing mapping of MC versions to associated RISC-V specifications
  - revision: "0.4.0"
    date: "2024-06-03"
    changes:
      - Added M-mode instruction requirements
      - Made Zicntr MANDATORY due to very low cost for implementations to support (in the spirit of minimizing options).
      - Removed OPT-CNTR-PREC since minstret and mcycle must be a full 64 bits to be standard-compliant.
  - revision: "0.3.0"
    date: "2024-05-25"
    changes:
      - Includes Zicntr as OPTIONAL and then has only 32-bit counters for instret and cycle.
  - revision: "0.2.0"
    date: "2024-05-20"
    changes:
      - Very early draft
  - revision: "0.1.0"
    date: "2024-05-16"
    changes:
      - Initial version

introduction: |
  The MC100 Processor Certificate targets basic RISC-V microcontrollers.
  It supports either a 32-bit (MC100-32) or 64-bit (MC100-64) base ISA.
  MC100 is not intended for the smallest possible microcontrollers but rather for applications
  benefiting from a minimal but standardized microcontroller. It consists of:

  * Unprivileged ISA: RV32I for MC100-32 and RV64I for MC100-64 with a few extensions suitable
  for a basic microcontroller.
  * Privileged ISA: Only the M-mode features listed as mandatory in the RISC-V Privileged ISA manual

# Specification versions
tsc_profile_release: null # None for MC100
unpriv_isa_manual_revision: "20191213"
priv_isa_manual_revision: "20190608-Priv-MSU-Ratified"
debug_manual_revision: "0.13.2"

extensions:
  I:
    presence: mandatory
  C:
    presence: mandatory
  M:
    presence: optional
  Zicsr:
    presence: mandatory
  Zicntr:
    presence: mandatory
    param_constraints:
      TIME_CSR_IMPLEMENTED: {} # Unconstrained
  Sm:
    version: "~> 1.12.0"
    presence: mandatory
    param_constraints:
      MTVEC_BASE_ALIGNMENT_DIRECT: {} # Unconstrained
      MTVEC_BASE_ALIGNMENT_VECTORED: {} # Unconstrained
      ARCH_ID: {} # Unconstrained
      IMP_ID: {} # Unconstrained
      VENDOR_ID_BANK: {} # Unconstrained
      VENDOR_ID_OFFSET: {} # Unconstrained
      MISA_CSR_IMPLEMENTED: {} # Unconstrained
      MTVAL_WIDTH: {} # Unconstrained
      MTVEC_MODES: {} # Unconstrained
      PHYS_ADDR_WIDTH: {} # Unconstrained
      MISALIGNED_LDST: {} # Unconstrained
      MISALIGNED_LDST_EXCEPTION_PRIORITY: {} # Unconstrained
      MISALIGNED_MAX_ATOMICITY_GRANULE_SIZE: {} # Unconstrained
      MISALIGNED_SPLIT_STRATEGY:
        schema:
          const: by_byte
      PRECISE_SYNCHRONOUS_EXCEPTIONS:
        schema:
          const: true
      TRAP_ON_ECALL_FROM_M:
        schema:
          const: true
      TRAP_ON_EBREAK:
        schema:
          const: true
      M_MODE_ENDIANNESS:
        schema:
          const: little
      MXLEN:
        schema:
          const: 32
