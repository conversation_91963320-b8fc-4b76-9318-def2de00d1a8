<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: TestExpressions
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "TestExpressions";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (T)</a> &raquo;
    
    
    <span class="title">TestExpressions</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: TestExpressions
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Minitest::Test</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">Minitest::Test</li>
          
            <li class="next">TestExpressions</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="TestMixin.html" title="TestMixin (module)">TestMixin</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/tests/test_expressions.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Test IDL expressions</p>


  </div>
</div>
<div class="tags">
  

</div>






  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#setup-instance_method" title="#setup (instance method)">#<strong>setup</strong>  &#x21d2; Object </a>
    

    
  </span>
  
    <span class="note title not_defined_here">
      included
      from <span class='object_link'><a href="TestMixin.html#setup-instance_method" title="TestMixin#setup (method)">TestMixin</a></span>
    </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#test_that_integer_literals_give_correct_values-instance_method" title="#test_that_integer_literals_give_correct_values (instance method)">#<strong>test_that_integer_literals_give_correct_values</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#test_that_multplication_is_higher_precedence_than_addition-instance_method" title="#test_that_multplication_is_higher_precedence_than_addition (instance method)">#<strong>test_that_multplication_is_higher_precedence_than_addition</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#test_that_operators_are_left_recusrive-instance_method" title="#test_that_operators_are_left_recusrive (instance method)">#<strong>test_that_operators_are_left_recusrive</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#test_that_values_are_tuncated-instance_method" title="#test_that_values_are_tuncated (instance method)">#<strong>test_that_values_are_tuncated</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="setup-instance_method">
  
    #<strong>setup</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
    <span class="not_defined_here">
      Originally defined in module
        <span class='object_link'><a href="TestMixin.html#setup-instance_method" title="TestMixin#setup (method)">TestMixin</a></span>
    </span>
  
</h3>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="test_that_integer_literals_give_correct_values-instance_method">
  
    #<strong>test_that_integer_literals_give_correct_values</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/test_expressions.rb', line 48</span>

<span class='kw'>def</span> <span class='id identifier rubyid_test_that_integer_literals_give_correct_values'>test_that_integer_literals_give_correct_values</span>
  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>8&#39;d13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>16&#39;hd</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>12&#39;o15</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>4&#39;b1101</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-8&#39;sd13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span><span class='lparen'>(</span><span class='op'>-</span><span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-16&#39;shd</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span><span class='lparen'>(</span><span class='op'>-</span><span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-12&#39;so15</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span><span class='lparen'>(</span><span class='op'>-</span><span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-4&#39;sb1101</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>3</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>4&#39;sb1101</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span><span class='lparen'>(</span><span class='op'>-</span><span class='int'>3</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>32&#39;h80000000</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>0x80000000</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>32&#39;h8000_0000</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>0x80000000</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>8&#39;13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='comment'># 13 decimal, unsigned XLEN-bit wide
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&#39;13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='comment'># 13 decimal, signed XLEN-bit wide
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&#39;s13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>13</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='comment'># compilation error: 300 does not fit in 8 bits
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&#39;h1_0000_0000</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_assert_raises'>assert_raises</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='comma'>,</span> <span class='label'>pass_error:</span> <span class='kw'>true</span><span class='rparen'>)</span> <span class='rbrace'>}</span>

  <span class='comment'># 3 decimal: the literal is 13, unsigned, in 4-bits. when negated, the sign bit is lost
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>-4&#39;d13</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>3</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='comment'># compilation error: 300 does not fit in 8 bits
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>8&#39;sd300</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_assert_raises'>assert_raises</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='comma'>,</span> <span class='label'>pass_error:</span> <span class='kw'>true</span><span class='rparen'>)</span> <span class='rbrace'>}</span>

  <span class='comment'># compilation error: bit width must be positive
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>0&#39;15</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_assert_raises'>assert_raises</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='comma'>,</span> <span class='label'>pass_error:</span> <span class='kw'>true</span><span class='rparen'>)</span> <span class='rbrace'>}</span>

  <span class='comment'># compilation error: value does not fit in four bita
</span>  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>4&#39;hff</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_assert_raises'>assert_raises</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">TypeError</a></span></span><span class='rparen'>)</span> <span class='lbrace'>{</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='comma'>,</span> <span class='label'>pass_error:</span> <span class='kw'>true</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="test_that_multplication_is_higher_precedence_than_addition-instance_method">
  
    #<strong>test_that_multplication_is_higher_precedence_than_addition</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


38
39
40
41
42
43
44
45
46</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/test_expressions.rb', line 38</span>

<span class='kw'>def</span> <span class='id identifier rubyid_test_that_multplication_is_higher_precedence_than_addition'>test_that_multplication_is_higher_precedence_than_addition</span>
  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='heredoc_beg'>&lt;&lt;~IDL</span><span class='period'>.</span><span class='id identifier rubyid_strip'>strip</span>
<span class='tstring_content'>    4 + 5&#39;d3 * 2
</span><span class='heredoc_end'>  IDL
</span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_refute_equal'>refute_equal</span> <span class='int'>14</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>10</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="test_that_operators_are_left_recusrive-instance_method">
  
    #<strong>test_that_operators_are_left_recusrive</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


12
13
14
15
16
17
18
19
20</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/test_expressions.rb', line 12</span>

<span class='kw'>def</span> <span class='id identifier rubyid_test_that_operators_are_left_recusrive'>test_that_operators_are_left_recusrive</span>
  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='heredoc_beg'>&lt;&lt;~IDL</span><span class='period'>.</span><span class='id identifier rubyid_strip'>strip</span>
<span class='tstring_content'>    4 - 3 - 1
</span><span class='heredoc_end'>  IDL
</span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>0</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>((4 - 3) - 1)</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="test_that_values_are_tuncated-instance_method">
  
    #<strong>test_that_values_are_tuncated</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


22
23
24
25
26
27
28
29
30
31
32
33
34
35
36</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/tests/test_expressions.rb', line 22</span>

<span class='kw'>def</span> <span class='id identifier rubyid_test_that_values_are_tuncated'>test_that_values_are_tuncated</span>
  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='heredoc_beg'>&lt;&lt;~IDL</span><span class='period'>.</span><span class='id identifier rubyid_strip'>strip</span>
<span class='tstring_content'>    4&#39;hf + 4&#39;h1
</span><span class='heredoc_end'>  IDL
</span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>0</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_idl'>idl</span> <span class='op'>=</span> <span class='heredoc_beg'>&lt;&lt;~IDL</span><span class='period'>.</span><span class='id identifier rubyid_strip'>strip</span>
<span class='tstring_content'>    4&#39;hf + 5&#39;h1
</span><span class='heredoc_end'>  IDL
</span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='ivar'>@compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_expression'>compile_expression</span><span class='lparen'>(</span><span class='id identifier rubyid_idl'>idl</span><span class='comma'>,</span> <span class='ivar'>@symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_assert_equal'>assert_equal</span> <span class='int'>16</span><span class='comma'>,</span> <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='ivar'>@symtab</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>