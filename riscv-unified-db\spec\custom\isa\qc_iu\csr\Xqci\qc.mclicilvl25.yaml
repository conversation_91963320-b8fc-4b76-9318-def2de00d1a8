# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl25
long_name: IRQ Level 25
address: 0xbd9
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 200-207
fields:
  IRQ200:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ200 level
  IRQ201:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ201 level
  IRQ202:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ202 level
  IRQ203:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ203 level
  IRQ204:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ204 level
  IRQ205:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ205 level
  IRQ206:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ206 level
  IRQ207:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ207 level
