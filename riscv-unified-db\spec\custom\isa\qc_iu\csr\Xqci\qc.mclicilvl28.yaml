# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl28
long_name: IRQ Level 28
address: 0xbdc
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 224-231
fields:
  IRQ224:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ224 level
  IRQ225:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ225 level
  IRQ226:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ226 level
  IRQ227:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ227 level
  IRQ228:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ228 level
  IRQ229:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ229 level
  IRQ230:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ230 level
  IRQ231:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ231 level
