# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl19
long_name: IRQ Level 19
address: 0xbd3
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 152-159
fields:
  IRQ152:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ152 level
  IRQ153:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ153 level
  IRQ154:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ154 level
  IRQ155:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ155 level
  IRQ156:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ156 level
  IRQ157:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ157 level
  IRQ158:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ158 level
  IRQ159:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ159 level
