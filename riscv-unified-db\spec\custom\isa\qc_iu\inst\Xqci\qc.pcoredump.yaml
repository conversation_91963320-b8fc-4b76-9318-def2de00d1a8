# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.pcoredump
long_name: Print core dump pseudo-instruction (hint) working only in simulation environment
description: |
  The print core dump instruction calls simulation environment with no explicit arguments.
  Implicit arguments are all general purpose registers and CSRs.
  Simulation environment expected to print the core dump on its console or standard output.
  The core dump format and content are defined by simulation environment.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: ""
base: 32
encoding:
  match: "01100000000000000010000000010011"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 8;
  XReg arg = 0;
  iss_syscall(func,arg);
