<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::VariableDeclarationWithInitializationAst
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::VariableDeclarationWithInitializationAst";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (V)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">VariableDeclarationWithInitializationAst</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::VariableDeclarationWithInitializationAst
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next"><span class='object_link'><a href="AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></li>
          
            <li class="next">Idl::VariableDeclarationWithInitializationAst</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  <dl>
      <dt>Includes:</dt>
      <dd><span class='object_link'><a href="Declaration.html" title="Idl::Declaration (module)">Declaration</a></span>, <span class='object_link'><a href="Executable.html" title="Idl::Executable (module)">Executable</a></span></dd>
  </dl>
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb<span class="defines">,<br />
  lib/idl/passes/gen_adoc.rb</span>
</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>reprents a single variable declaration with initialization</p>

<p>for example:</p>

<pre class="code ruby"><code class="ruby">Bits&lt;64&gt; doubleword = 64&#39;hdeadbeef
Boolean has_property = true
</code></pre>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#lhs-instance_method" title="#lhs (instance method)">#<strong>lhs</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute lhs.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#rhs-instance_method" title="#rhs (instance method)">#<strong>rhs</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute rhs.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_name-instance_method" title="#type_name (instance method)">#<strong>type_name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns the value of attribute type_name.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#add_symbol-instance_method" title="#add_symbol (instance method)">#<strong>add_symbol</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Add symbol(s) at the outermost scope of the symbol table.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#execute-instance_method" title="#execute (instance method)">#<strong>execute</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>“execute” the statement by updating the variables in the symbol table.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#gen_adoc-instance_method" title="#gen_adoc (instance method)">#<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(input, interval, type_name_ast, var_write_ast, ary_size, rval_ast)  &#x21d2; VariableDeclarationWithInitializationAst </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of VariableDeclarationWithInitializationAst.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#lhs_type-instance_method" title="#lhs_type (instance method)">#<strong>lhs_type</strong>(symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_idl-instance_method" title="#to_idl (instance method)">#<strong>to_idl</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Return valid IDL representation of the node (and its subtree).</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check-instance_method" title="#type_check (instance method)">#<strong>type_check</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check this node and all children.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  
  
  
  
  
  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(input, interval, type_name_ast, var_write_ast, ary_size, rval_ast)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::VariableDeclarationWithInitializationAst (class)">VariableDeclarationWithInitializationAst</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of VariableDeclarationWithInitializationAst.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1447
1448
1449
1450
1451
1452
1453
1454
1455
1456
1457</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1447</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='id identifier rubyid_type_name_ast'>type_name_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_var_write_ast'>var_write_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_ary_size'>ary_size</span><span class='comma'>,</span> <span class='id identifier rubyid_rval_ast'>rval_ast</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_ary_size'>ary_size</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='lbracket'>[</span><span class='id identifier rubyid_type_name_ast'>type_name_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_var_write_ast'>var_write_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_rval_ast'>rval_ast</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>else</span>
    <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_input'>input</span><span class='comma'>,</span> <span class='id identifier rubyid_interval'>interval</span><span class='comma'>,</span> <span class='lbracket'>[</span><span class='id identifier rubyid_type_name_ast'>type_name_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_var_write_ast'>var_write_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_ary_size'>ary_size</span><span class='comma'>,</span> <span class='id identifier rubyid_rval_ast'>rval_ast</span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@type_name</span> <span class='op'>=</span> <span class='id identifier rubyid_type_name_ast'>type_name_ast</span>
  <span class='ivar'>@lhs</span> <span class='op'>=</span> <span class='id identifier rubyid_var_write_ast'>var_write_ast</span>
  <span class='ivar'>@ary_size</span> <span class='op'>=</span> <span class='id identifier rubyid_ary_size'>ary_size</span>
  <span class='ivar'>@rhs</span> <span class='op'>=</span> <span class='id identifier rubyid_rval_ast'>rval_ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="lhs-instance_method">
  
    #<strong>lhs</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute lhs.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1445
1446
1447</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1445</span>

<span class='kw'>def</span> <span class='id identifier rubyid_lhs'>lhs</span>
  <span class='ivar'>@lhs</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="rhs-instance_method">
  
    #<strong>rhs</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute rhs.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1445
1446
1447</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1445</span>

<span class='kw'>def</span> <span class='id identifier rubyid_rhs'>rhs</span>
  <span class='ivar'>@rhs</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="type_name-instance_method">
  
    #<strong>type_name</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the value of attribute type_name.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1445
1446
1447</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1445</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_name'>type_name</span>
  <span class='ivar'>@type_name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="add_symbol-instance_method">
  
    #<strong>add_symbol</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Add symbol(s) at the outermost scope of the symbol table</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table at the scope that the symbol(s) will be inserted</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1516
1517
1518
1519
1520</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1516</span>

<span class='kw'>def</span> <span class='id identifier rubyid_add_symbol'>add_symbol</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='id identifier rubyid_lhs_type'>lhs_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span>
<span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span>
  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='id identifier rubyid_lhs_type'>lhs_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="execute-instance_method">
  
    #<strong>execute</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>“execute” the statement by updating the variables in the symbol table</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The symbol table for the context</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>ValueError if some part of the statement cannot be executed at compile time</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1523
1524
1525
1526</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1523</span>

<span class='kw'>def</span> <span class='id identifier rubyid_execute'>execute</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_value_error'>value_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>TODO: Array declaration</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
  <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='id identifier rubyid_lhs_type'>lhs_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="gen_adoc-instance_method">
  
    #<strong>gen_adoc</strong>(indent = 0, indent_spaces: 2)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


112
113
114
115
116
117
118</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/passes/gen_adoc.rb', line 112</span>

<span class='kw'>def</span> <span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='id identifier rubyid_indent'>indent</span> <span class='op'>=</span> <span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span> <span class='int'>2</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'> </span><span class='tstring_end'>&#39;</span></span> <span class='op'>*</span> <span class='id identifier rubyid_indent'>indent</span><span class='embexpr_end'>}</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>else</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'> </span><span class='tstring_end'>&#39;</span></span> <span class='op'>*</span> <span class='id identifier rubyid_indent'>indent</span><span class='embexpr_end'>}</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>[</span><span class='embexpr_beg'>#{</span><span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>] = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_gen_adoc'>gen_adoc</span><span class='lparen'>(</span><span class='int'>0</span><span class='comma'>,</span> <span class='label'>indent_spaces:</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="lhs_type-instance_method">
  
    #<strong>lhs_type</strong>(symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1459
1460
1461
1462
1463
1464
1465
1466
1467
1468
1469
1470
1471
1472
1473
1474
1475
1476
1477
1478
1479
1480
1481
1482
1483</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1459</span>

<span class='kw'>def</span> <span class='id identifier rubyid_lhs_type'>lhs_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_decl_type'>decl_type</span> <span class='op'>=</span> <span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span>
  <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No type &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39; on line </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lineno'>lineno</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_decl_type'>decl_type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:enum_ref</span><span class='comma'>,</span> <span class='label'>enum_class:</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='rparen'>)</span> <span class='kw'>if</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_kind'>kind</span> <span class='op'>==</span> <span class='symbol'>:enum</span>

  <span class='comment'># decl_type = decl_type.clone.qualify(q.text_value.to_sym) unless q.empty?
</span>
  <span class='kw'>if</span> <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_upcase'>upcase</span> <span class='op'>==</span> <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span>
    <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_make_const'>make_const</span>
  <span class='kw'>end</span>

  <span class='kw'>unless</span> <span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='kw'>begin</span>
      <span class='id identifier rubyid_decl_type'>decl_type</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Type.html" title="Idl::Type (class)">Type</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Type.html#initialize-instance_method" title="Idl::Type#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='symbol'>:array</span><span class='comma'>,</span> <span class='label'>sub_type:</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='comma'>,</span> <span class='label'>width:</span> <span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span>
      <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Array size must be known at compile time</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
    <span class='kw'>if</span> <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_upcase'>upcase</span> <span class='op'>==</span> <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_make_const'>make_const</span>
    <span class='kw'>end</span>  
  <span class='kw'>end</span>

  <span class='id identifier rubyid_decl_type'>decl_type</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="to_idl-instance_method">
  
    #<strong>to_idl</strong>  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Return valid IDL representation of the node (and its subtree)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>IDL code for the node</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1529
1530
1531
1532
1533
1534
1535</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1529</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_idl'>to_idl</span>
  <span class='kw'>if</span> <span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'> = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>else</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_type_name'>type_name</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'> </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_lhs'>lhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'>[</span><span class='embexpr_beg'>#{</span><span class='ivar'>@ary_size</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_content'>] = </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_rhs'>rhs</span><span class='period'>.</span><span class='id identifier rubyid_to_idl'>to_idl</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check-instance_method">
  
    #<strong>type_check</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>type check this node and all children</p>

<p>Calls to #type and/or #value may depend on type_check being called first with the same symtab. If not, those functions may raise an AstNode::InternalError</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table for lookup</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/TypeError.html" title="Idl::AstNode::TypeError (class)">AstNode::TypeError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is a type error</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="AstNode/InternalError.html" title="Idl::AstNode::InternalError (class)">AstNode::InternalError</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if there is an internal compiler error during type check</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1486
1487
1488
1489
1490
1491
1492
1493
1494
1495
1496
1497
1498
1499
1500
1501
1502
1503
1504
1505
1506
1507
1508
1509
1510
1511
1512
1513</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 1486</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>
  <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>

  <span class='ivar'>@type_name</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>

  <span class='ivar'>@ary_size</span><span class='op'>&amp;.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_decl_type'>decl_type</span> <span class='op'>=</span> <span class='id identifier rubyid_lhs_type'>lhs_type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>


  <span class='kw'>if</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_const?'>const?</span>
    <span class='comment'># this is a constant; ensure we are assigning a constant value
</span>    <span class='kw'>begin</span>
      <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span><span class='comma'>,</span> <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_value'>value</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span>
    <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="AstNode/ValueError.html" title="Idl::AstNode::ValueError (class)">ValueError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
      <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Declaring constant with a non-constant value (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_e'>e</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>end</span>
  <span class='kw'>else</span>
    <span class='id identifier rubyid_symtab'>symtab</span><span class='period'>.</span><span class='id identifier rubyid_add'>add</span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='const'><span class='object_link'><a href="Var.html" title="Idl::Var (class)">Var</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Var.html#initialize-instance_method" title="Idl::Var#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_text_value'>text_value</span><span class='comma'>,</span> <span class='id identifier rubyid_decl_type'>decl_type</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span><span class='rparen'>)</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@lhs</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span>

  <span class='comment'># now check that the assignment is compatible
</span>  <span class='kw'>return</span> <span class='kw'>if</span> <span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_convertable_to?'>convertable_to?</span><span class='lparen'>(</span><span class='id identifier rubyid_decl_type'>decl_type</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_type_error'>type_error</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Incompatible type (</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_decl_type'>decl_type</span><span class='embexpr_end'>}</span><span class='tstring_content'>, </span><span class='embexpr_beg'>#{</span><span class='ivar'>@rhs</span><span class='period'>.</span><span class='id identifier rubyid_type'>type</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span><span class='embexpr_end'>}</span><span class='tstring_content'>) in assignment</span><span class='tstring_end'>&quot;</span></span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:45 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>