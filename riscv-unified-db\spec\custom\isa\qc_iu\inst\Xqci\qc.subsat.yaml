# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.subsat
long_name: Saturating signed subtraction
description: |
  Subtract signed values `rs1` and `rs2`, saturate the signed result, and write to `rd`.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcia
base: 32
encoding:
  match: 0010000----------011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  Bits<xlen()+1> result = X[rs1] `- X[rs2];
  Bits<xlen()+1> most_negative_number = {2'b11,{(xlen()-1){1'b0}}};
  Bits<xlen()+1> most_positive_number = {2'b00,{(xlen()-1){1'b1}}};

  # overflow occurs if the operands have different signs and the result is a different sign than the first operand
  if (X[rs1][xlen()-1] != X[rs2][xlen()-1]) {
    if (result[xlen()-1] != X[rs1][xlen()-1]) {
      if ($signed(X[rs1]) < 0) {
        result = most_negative_number;
      } else {
        result = most_positive_number;
      }
    }
  }

  # otherwise, overflow did not occur
  X[rd] = result[(xlen() - 1):0];
