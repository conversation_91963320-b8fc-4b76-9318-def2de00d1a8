<% unless db_obj.cert_normative_rules.empty? -%>
<% nice_name = db_obj.is_a?(Udb::CsrField) ? "#{db_obj.parent.name}.#{db_obj.name}" : db_obj.name -%>
<% if defined?(use_description_list) && use_description_list -%>
Normative Rules for `<%= nice_name %>`: ::
<% else -%>
==== Normative Rules for `<%= nice_name %>`
<% end -%>

[%autowidth]
|===
| ID | Description | Documentation Links

<% db_obj.cert_normative_rules.each do |cp| -%>
| <%= defined?(org) ? anchor_for_udb_doc_cov_pt(org, cp.id) : "" %><%= cp.id %>
| <%= cp.description %>
a| <% cp.doc_links.each do |link| -%>
* <%= link.to_adoc %>
<% end # each link -%>
<% end # each normative rule -%>
|===

<% end # unless no normative rules -%>
