# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: cycle
long_name: Cycle counter for RDCYCLE Instruction
address: 0xC00
writable: false
description: |
  Alias for M-mode CSR `mcycle`.

  Privilege mode access is controlled with `mcounteren.CY`, `scounteren.CY`, and `hcounteren.CY` as follows:

  [%autowidth,cols="1,1,1,1,1,1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.CY`# .2+h! [.rotate]#`scounteren.CY`# .2+h! [.rotate]#`hcounteren.CY`#
  4+^.>h! `cycle` behavior
  .^h! S-mode .^h! U-mode .^h! VS-mode .^h! VU-mode

  ! 0 ! - ! - ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction`
  ! 1 ! 0 ! 0 ! read-only ! `IllegalInstruction` ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 1 ! 0 ! read-only ! read-only ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 0 ! 1 ! read-only ! `IllegalInstruction` ! read-only ! `VirtualInstruction`
  ! 1 ! 1 ! 1 ! read-only ! read-only ! read-only ! read-only
  !===
priv_mode: U
length: 64
definedBy: Zicntr
fields:
  COUNT:
    location: 63-0
    alias: mcycle.COUNT
    description: Alias of `mcycle.COUNT`.
    type: RO-H
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  # access is determined by *counteren CSRs
  if (mode() == PrivilegeMode::S) {
    # S-mode is present ->
    #   mcounteren determines access in S-mode
    if (CSR[mcounteren].CY == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::U) {
    if (CSR[misa].S == 1'b1) {
      # S-mode is present ->
      #   mcounteren and scounteren together determine access in U-mode
      if ((CSR[mcounteren].CY & CSR[scounteren].CY) == 1'b0) {
        raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
    } else if (CSR[mcounteren].CY == 1'b0) {
      # S-mode is not present ->
      #   mcounteren determines access in U-mode
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VS) {
    # access in VS mode
    if (CSR[hcounteren].CY == 1'b0 && CSR[mcounteren].CY == 1'b1) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].CY == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VU) {
    # access in VU mode
    if (((CSR[hcounteren].CY & CSR[scounteren].CY) == 1'b0) && (CSR[mcounteren].CY == 1'b1)) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].CY == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  }

  # since the counter may be shared among harts, reads must be handled
  # as a builtin function
  return read_mcycle();
