# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.andi
long_name: Add immediate
description: |
  And a sign-extended 26-bit immediate `imm` to the value in `rs1`,
  and store the result in `rd`.
  Instruction encoded in QC.EI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilia
base: 32
encoding:
  match: ----------------11---------------011-----0011111
  variables:
    - name: imm
      location: 47-32|29-20
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, imm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = X[rs1] & sext(imm,26);
