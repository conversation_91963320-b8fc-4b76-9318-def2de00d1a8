# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl13
long_name: IRQ Level 13
address: 0xbcd
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 104-111
fields:
  IRQ104:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ104 level
  IRQ105:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ105 level
  IRQ106:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ106 level
  IRQ107:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ107 level
  IRQ108:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ108 level
  IRQ109:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ109 level
  IRQ110:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ110 level
  IRQ111:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ111 level
