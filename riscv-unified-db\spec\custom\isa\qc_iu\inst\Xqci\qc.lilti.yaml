# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.lilti
long_name: Conditional load immediate if less than (Immediate)
description: |
  Move `simm` to `rd` if the value in `rs1` is less than value `imm`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcicli
base: 32
encoding:
  match: -----11----------100-----1011011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: imm
      location: 24-20
    - name: simm
      location: 31-27
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, imm, simm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if ($signed(X[rs1]) < $signed(imm)) {
    X[rd] = sext(simm, 5);
  }
