# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicie2
long_name: IRQ Enable 2
address: 0x7fa
length: 32
base: 32
priv_mode: M
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Enable bits for IRQs 64-95
fields:
  IRQ64:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ64 enabled
  IRQ65:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ65 enabled
  IRQ66:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ66 enabled
  IRQ67:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ67 enabled
  IRQ68:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ68 enabled
  IRQ69:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ69 enabled
  IRQ70:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ70 enabled
  IRQ71:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ71 enabled
  IRQ72:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ72 enabled
  IRQ73:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ73 enabled
  IRQ74:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ74 enabled
  IRQ75:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ75 enabled
  IRQ76:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ76 enabled
  IRQ77:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ77 enabled
  IRQ78:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ78 enabled
  IRQ79:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ79 enabled
  IRQ80:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ80 enabled
  IRQ81:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ81 enabled
  IRQ82:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ82 enabled
  IRQ83:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ83 enabled
  IRQ84:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ84 enabled
  IRQ85:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ85 enabled
  IRQ86:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ86 enabled
  IRQ87:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ87 enabled
  IRQ88:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ88 enabled
  IRQ89:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ89 enabled
  IRQ90:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ90 enabled
  IRQ91:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ91 enabled
  IRQ92:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ92 enabled
  IRQ93:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ93 enabled
  IRQ94:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ94 enabled
  IRQ95:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ95 enabled
