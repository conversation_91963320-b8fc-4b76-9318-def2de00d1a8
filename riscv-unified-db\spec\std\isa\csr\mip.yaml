# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mip
long_name: Machine Interrupt Pending
address: 0x344
writable: true
priv_mode: M

# Description is shared with mie CSR (it copies it from here).
description: |
  The `mie` and `mip` CSRs are MXLEN-bit read/write registers used when
  the CLINT or PLIC interrupt controllers are present.
  Note that the CLINT refers to an interrupt controller
  used by some RISC-V implementations but isn't a ratified
  RISC-V International standard.

  The `mip` CSR contains information on pending interrupts, while `mie` is the corresponding
  CSR containing interrupt enable bits.
  Interrupt cause number _i_ (as reported in the `mcause` CSR)
  corresponds to bit _i_ in both `mip` and `mie`.
  Bits 15:0 are allocated to standard interrupt causes only, while
  bits 16 and above are designated for platform use.

  NOTE: Interrupts designated for platform use may be designated for custom use
  at the platform's discretion.

  An interrupt _i_ will trap to M-mode (causing the privilege mode to
  change to M-mode) if all of the following are true:

    * either the current privilege mode is M and the MIE bit in the `mstatus` register is
    set, or the current privilege mode has less privilege than M-mode;
    * bit _i_ is set in both `mip` and `mie`
    * if register `mideleg` exists, bit _i_ is not set in `mideleg`.

  These conditions for an interrupt trap to occur must be evaluated in a
  bounded amount of time from when an interrupt becomes, or ceases to be,
  pending in `mip`, and must also be evaluated immediately following the
  execution of an __x__RET instruction or an explicit write to a CSR on
  which these interrupt trap conditions expressly depend (including `mip`,
  `mie`, `mstatus`, and `mideleg`).

  Interrupts to M-mode take priority over any interrupts to lower
  privilege modes.

  Each individual bit in register `mip` may be writable or may be
  read-only. When bit _i_ in `mip` is writable, a pending interrupt _i_
  can be cleared by writing 0 to this bit. If interrupt _i_ can become
  pending but bit _i_ in `mip` is read-only, the implementation must
  provide some other mechanism for clearing the pending interrupt.

  A bit in `mie` must be writable if the corresponding interrupt can ever
  become pending. Bits of `mie` that are not writable must be read-only
  zero.

  [NOTE]
  ====
  The machine-level interrupt registers handle a few root interrupt
  sources which are assigned a fixed service priority for simplicity,
  while separate external interrupt controllers can implement a more
  complex prioritization scheme over a much larger set of interrupts that
  are then muxed into the machine-level interrupt sources.

  '''

  The non-maskable interrupt is not made visible via the `mip` register as
  its presence is implicitly known when executing the NMI trap handler.
  ====

  If supervisor mode is implemented, bits `mip`.SEIP and `mie`.SEIE are
  the interrupt-pending and interrupt-enable bits for supervisor-level
  external interrupts. SEIP is writable in `mip`, and may be written by
  M-mode software to indicate to S-mode that an external interrupt is
  pending. Additionally, the platform-level interrupt controller may
  generate supervisor-level external interrupts. Supervisor-level external
  interrupts are made pending based on the logical-OR of the
  software-writable SEIP bit and the signal from the external interrupt
  controller. When `mip` is read with a CSR instruction, the value of the
  SEIP bit returned in the `rd` destination register is the logical-OR of
  the software-writable bit and the interrupt signal from the interrupt
  controller, but the signal from the interrupt controller is not used to
  calculate the value written to SEIP. Only the software-writable SEIP bit
  participates in the read-modify-write sequence of a CSRRS or CSRRC
  instruction.

  [NOTE]
  ====
  For example, if we name the software-writable SEIP bit `B` and the
  signal from the external interrupt controller `E`, then if
  `csrrs t0, mip, t1` is executed, `t0[9]` is written with `B || E`, then
  `B` is written with `B || t1[9]`. If `csrrw t0, mip, t1` is executed,
  then `t0[9]` is written with `B || E`, and `B` is simply written with
  `t1[9]`. In neither case does `B` depend upon `E`.

  The SEIP field behavior is designed to allow a higher privilege layer to
  mimic external interrupts cleanly, without losing any real external
  interrupts. The behavior of the CSR instructions is slightly modified
  from regular CSR accesses as a result.
  ====

  If supervisor mode is implemented, bits `mip`.STIP and `mie`.STIE are
  the interrupt-pending and interrupt-enable bits for supervisor-level
  timer interrupts. STIP is writable in `mip`, and may be written by
  M-mode software to deliver timer interrupts to S-mode.

  If supervisor mode is implemented, bits `mip`.SSIP and `mie`.SSIE are
  the interrupt-pending and interrupt-enable bits for supervisor-level
  software interrupts. SSIP is writable in `mip` and may also be set to 1
  by a platform-specific interrupt controller.

  <% if ext?(:Sscofpmf) -%>
  bits `mip`.LCOFIP and `mie`.LCOFIE
  are the interrupt-pending and interrupt-enable bits for local counter-overflow
  interrupts.
  LCOFIP is read-write in `mip` and reflects the occurrence of a local
  counter-overflow overflow interrupt request resulting from any of the
  `mhpmevent__n__`.OF bits being set.
  <% end -%>

  Multiple simultaneous interrupts destined for M-mode are handled in the
  following decreasing priority order: MEI, MSI, MTI, SEI, SSI, STI, LCOFI.

  [NOTE]
  ====
  The machine-level interrupt fixed-priority ordering rules were developed
  with the following rationale.

  Interrupts for higher privilege modes must be serviced before interrupts
  for lower privilege modes to support preemption.

  The platform-specific machine-level interrupt sources in bits 16 and
  above have platform-specific priority, but are typically chosen to have
  the highest service priority to support very fast local vectored
  interrupts.

  External interrupts are handled before internal (timer/software)
  interrupts as external interrupts are usually generated by devices that
  might require low interrupt service times.

  Software interrupts are handled before internal timer interrupts,
  because internal timer interrupts are usually intended for time slicing,
  where time precision is less important, whereas software interrupts are
  used for inter-processor messaging. Software interrupts can be avoided
  when high-precision timing is required, or high-precision timer
  interrupts can be routed via a different interrupt path. Software
  interrupts are located in the lowest four bits of `mip` as these are
  often written by software, and this position allows the use of a single
  CSR instruction with a five-bit immediate.
  ====

  Restricted views of the `mip` and `mie` registers appear as the `sip`
  and `sie` registers for supervisor level. If an interrupt is delegated
  to S-mode by setting a bit in the `mideleg` register, it becomes visible
  in the `sip` register and is maskable using the `sie` register.
  Otherwise, the corresponding bits in `sip` and `sie` are read-only zero.
length: MXLEN
definedBy: Sm
fields:
  SSIP:
    location: 1
    alias:
      - sip.SSIP
      - mvip.SSIP
    description: |
      *Supervisor Software Interrupt Pending*

      Reports the current pending state of an (H)S-mode software interrupt, which is generated by writing to this field.

      <%- if ext?(:Smaia) -%>
      When using AIA/IMSIC, IPIs are expected to be delivered as external interrupts
      and SSIP is not backed by any hardware update (aside from any aliasing effects).
      However, SSIP is still writable by M-mode software and, when written, can be used to
      generate an S-mode Software Interrupt.
      <%- end -%>

      <% if ext?(:Smaia) %>_Aliases_<% else %>_Alias_<% end %>:

      * `sip.SSIP` when `mideleg.SSI` is set
      <%- if ext?(:Smaia) -%>
      * `mvip.SSIP`
      <%- end -%>
    type: RW
    reset_value: 0
    definedBy: S
    affectedBy: Smaia
  VSSIP:
    location: 2
    alias:
      - hip.VSSIP
      - hvip.VSSIP
      - vsip.SSIP
      - sip.SSIP
    description: |
      *Virtual Supervisor Software Interrupt Pending*

      Reports the current pending state of a VS-mode software interrupt, which is generated by writing to this field.

      <%- if ext?(:Smaia) -%>
      When using AIA/IMSIC, IPIs are expected to be delivered as external interrupts and VSSIP is not backed by any hardware update (aside from any aliased writes).
      However, VSSIP is still writable by M-mode software and, when written, can be used to
      generate a VS-mode Software Interrupt.
      <%- end -%>

      _Aliases_:

      * `hip.VSSIP`
      * `hvip.VSSIP`
      * `vsip.SSIP` when `hideleg.VSSI` is set
    type: RW
    reset_value: 0
    definedBy: H
    affectedBy: Smaia
  MSIP:
    location: 3
    description: |
      *Machine Software Interrupt Pending*

      Unused field.

      <%- if ext?(:Smaia) -%>
      With AIA/IMSIC, IPIs are delivered as external interrupts. As a result, this bit is
      unused and hardwired to 0.
      <%- end -%>
    type: RO
    reset_value: 0
  STIP:
    location: 5
    alias:
      - sip.STIP
      - mvip.STIP
    description: |
      *Supervisor Timer Interrupt Pending*

      Reports the current pending state of an (H)S-mode timer interrupt
      <%- if ext?(:Sstc) -%>
      , which is normally controlled by the `stimecmp` CSR.
      <%- else -%>
      , which is generated by software by writing to `mip.STIP`<% if ext?(:Smaia) %>or its alias `mvip.STIP`<% end %>.
      <%- end -%>

      <%-if ext?(:Sstc) -%>
      When `menvcfg.STCE` is set, `mip.STIP` is RO-H, and is completely controlled by the timer interrupt device (using `stimecmp`).

      When `menvcfg.STCE` is clear, `mip.STIP` is RW, and M-mode software may write the bit to inject a Supervisor Timer Interrupt.
      <%- end -%>

      <% if ext?(:Smaia) %>_Aliases_<% else %>_Alias_<% end %>:

      * `sip.STIP` when `mideleg.STI` is set (though `sip.STIP` is a read-only view)
      <%- if ext?(:Smaia) -%>
      * `mvip.STIP` when when `menvcfg.STCE` is clear
      <%- end -%>

    type: RW
    reset_value: 0
    definedBy: S
    affectedBy: Sstc
  VSTIP:
    location: 6
    alias:
      - hip.VSTIP
    description: |
      *Virtual Supervisor Timer Interrupt Pending*

      Reports the current pending state of a VS-mode timer interrupt
      <%- if ext?(:Sstc) -%>
      , which is normally controlled by the `vstimecmp` CSR, but can also be injected by the hypervisor through `hvip.VSTIP`.
      <%- else -%>
      , which is generated by M-mode and/or HS-mode software by writing to `hvip.VSTIP`.
      <%- end -%>

      <%-if ext?(:Sstc) -%>
      When `menvcfg.STCE` is set (enabling the `Sstc` extension), `mip.VSTIP` is the logical OR of `hvip.VSTIP` and the VS-level interrupt signal generated by the timer device (controlled by the value of `vstimecmp`).

      When `menvcfg.STCE` is clear (disabling the `Sstc` extension), `mip.VSTIP` is exactly the value of `hvip.VSTIP`.
      <%- end -%>

      `mip.VSTIP` is never writable. If VS-mode software wants to clear the bit, it must do so
      <%- if ext?(:Sstc) -%>
      by writing the `vstimecmp` register or
      <%- end -%>
      by calling into the hypervisor (which can then clear `hvip.VSTIP`).

      _Aliases_:

      * `hip.VSTIP`
      * `vsip.STIP` when `hideleg.VSTI` is set
      * `hvip.VSTIP` <% if ext?(:Sstc) %>when `menvcfg.STCE` is clear<% end %> (though `hvip.VSTIP` is writable)
    type: RO-H
    reset_value: 0
    definedBy: H
    affectedBy: Sstc
  MTIP:
    location: 7
    description: |
      *Machine Timer Interrupt Pending*

      Reports the current pending state of an M-mode timer interrupt.

      Bit is controlled by the timer device (using `mtimecmp`), and is not writable.
    type: RO-H
    reset_value: 0
  SEIP:
    location: 9
    alias:
      - sip.SEIP
    description: |
      *Supervisor External Interrupt Pending*

      Reports the current pending state of an (H)S-mode external interrupt.

      This field has two parts: a software-writable shadow value and a wire from the interrupt controller.
      The value presented to software in the bit on a CSR read is the logical OR of the software-writable value and the interrupt controller value.
      When software writes this bit, only the shadow value is updated (the interrupt controller is not notified of the write).

      <%- if ext?(:Smaia) -%>
      The software-writable shadow value is aliased in `mvip.SEIP` (`Smaia` extension).
      <%- end -%>

      _Alias_:

      * `sip.SEIP` when `mideleg.SEI` is set (though `sip.SEIP` is read-only)
    type: RW-H
    definedBy: S
    affectedBy: Smaia
    reset_value: 0
  VSEIP:
    location: 10
    alias:
      - hip.VSEIP
      - vsip.SEIP
      - sip.SEIP
    description: |
      *Virtual Supervisor External Interrupt Pending*

      Reports the current pending state of a VS-mode external interrupt.

      This field is the logical OR of `hvip.VSEIP` and the wire coming from the interrupt controller.

      The field is not writable by software
      <%- if ext?(:Smaia) -%>
      (_i.e._, unlike the behavior of `mip.SEIP`/`mvip.SEIP`, attempted writes to `mip.VSEIP` do not propagate to `hvip.VSEIP`)
      <%- end -%>
      .

      _Aliases_:

      * `hip.VSEIP`
      * `vsip.SEIP` when `hideleg.VSEI` is set
    type: RO-H
    reset_value: 0
    definedBy: H
    affectedBy: Smaia
  MEIP:
    location: 11
    description: |
      *Machine External Interrupt Pending*

      Reports the current pending state of an M-mode external interrupt.

      MEIP is controlled by the external interrupt controller <% if ext?(:Smaia) %>(AIA) <% end %>.
      It is not writable by software.
    type: RO-H
    reset_value: 0
  SGEIP:
    location: 12
    alias: hip.SGEIP
    description: |
      *Supervisor Guest External Interrupt Pending*

      Read-only summary of any pending Supervisor Guest External Interrupt Pending, i.e.:
      the logical-OR reduction of the `hgeip` register.

      _Alias_:

      * `hip.SGEIP`
    type: RO-H
    reset_value: 0
    definedBy: H
  LCOFIP:
    location: 13
    alias:
      - sip.LCOFIP
      - vsip.LCOFIP
    description: |
      *Local Counter Overflow Interrupt pending*

      <%- if ext?(:H) -%>
      When `hideleg.LCOFI` is set,
      `vsip.LCOFIP`, `sip.LCOFIP`, and `mip.LCOFIP` are all aliases.
      <%- end -%>

      When a counter overflow interrupt occurs, a hidden sticky bit is set.

      Software writes 0 to `mip.LCOFIP` to clear the pending interrupt.

      <% if ext?(:H) %>_Aliases_<% else %>_Alias_<% end %>:

      * `sip.LCOFIP` when `mideleg.LCOFI` is set
      <%- if ext?(:H) -%>
      * `vsip.LCOFIP` when `hideleg.LCOFI` is set
      <%- end -%>
    type: RW-H
    reset_value: 0
    definedBy: Sscofpmf
sw_read(): |
  # OR in the hidden smode external interrupt
  return
    $bits(CSR[mip])
    | ((CSR[misa].S == 1'b1 && pending_smode_external_interrupt)
         ? 10'h200
         : 0);
