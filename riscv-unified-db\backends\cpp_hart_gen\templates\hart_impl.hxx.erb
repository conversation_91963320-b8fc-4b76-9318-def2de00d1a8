#pragma once

#include <memory>

#include <fmt/core.h>

#include "udb/inst.hpp"
#include "udb/cfgs/<%= cfg_arch.name %>/inst.hxx"
#include "udb/hart.hpp"

using namespace std::literals;

namespace udb {

  <%- cfg_arch.globals.each do |global| -%>
  <%- next if global.type(cfg_arch.symtab).const? -%>
  template <SocModel SocType>
  <%= global.type(cfg_arch.symtab).to_cxx %> <%= name_of(:hart, cfg_arch) %><SocType>::<%= global.id %>;
  <%- end -%>

  template <SocModel SocType>
  void udb::<%= name_of(:hart, cfg_arch) %><SocType>::printState(FILE* out) const {
    fmt::print(out, "Hart %u:\n", this->m_hart_id);
    if constexpr (sizeof(XReg) == 8) {
      fmt::print(out, "PC: {:#18x}\n", m_pc);
      for (int i=0; i<16; i++) {
        fmt::print(out, "x{:2}: {:#18x}\tx{:2}: {:#18x}\n", i, m_xregs[i], i + 16, m_xregs[16 + 1]);
      }
    } else if constexpr (sizeof(XReg) == 4) {
      fmt::print(out, "PC: {:#10x}\n", m_pc);
      for (int i=0; i<16; i++) {
        fmt::print(out, "x{:2}: {:#10x}\tx{:2}: {:#10x}\n", i, m_xregs[i], i + 16, m_xregs[16 + 1]);
      }
    } else {
      udb_assert(false, "unsupported xlen");
    }
  }

  template <SocModel SocType>
  void udb::<%= name_of(:hart, cfg_arch) %><SocType>::init_csr_map()
  {
    <%- cfg_arch.not_prohibited_csrs.each do |csr| -%>
    <%- unless csr.address.nil? -%>
    m_csr_addr_map[<%= csr.address %>] = &m_csrs.<%= csr.name %>;
    <%- end -%>
    <%- unless csr.indirect_address.nil? -%>
    m_csr_indirect_addr_map[std::make_pair(<%= csr.indirect_address %>, <%= csr.indirect_slot %>)] = &m_csrs.<%= csr.name %>;
    <%_ end -%>
    m_csr_name_map["<%= csr.name %>"] = &m_csrs.<%= csr.name %>;
    <%- end -%>
  }

#define __UDB_CONST_GLOBAL(global_name) <%= name_of(:hart, cfg_arch) %><SocType>::global_name
#define __UDB_MUTABLE_GLOBAL(global_name) this->global_name
#define __UDB_FUNC_CALL this->
#define __UDB_PC this->m_pc


  template <SocModel SocType>
  Bits<<%= name_of(:hart, cfg_arch) %><SocType>::INSTR_ENC_SIZE> <%= name_of(:hart, cfg_arch) %><SocType>::_fetch()
  {
    <%- symtab = cfg_arch.symtab.global_clone -%>
    <%- symtab.push(cfg_arch.fetch) -%>
    <%= cfg_arch.fetch.body.prune(symtab).gen_cpp(symtab, 4) %>
    <%- symtab.release -%>
  }

  template <SocModel SocType>
  bool <%= name_of(:hart, cfg_arch) %><SocType>::_decode(
    const XReg& pc,
    const Bits<<%= cfg_arch.largest_encoding %>>& encoding,
    InstBase* inst
  ) {
    <%- if cfg_arch.multi_xlen? -%>
    if (xlen() == 32) {
      <%= DecodeGen.new(cfg_arch).generate(cfg_arch.possible_instructions.select { |i| i.rv32? }, 32, indent: 4) %>
    } else {
      <%= DecodeGen.new(cfg_arch).generate(cfg_arch.possible_instructions.select { |i| i.rv64? }, 64, indent: 4) %>
    }
    <%- else # just need a single decode -%>
    <%= DecodeGen.new(cfg_arch).generate(cfg_arch.possible_instructions, cfg_arch.possible_xlens[0], indent: 2) %>
    <%- end -%>

    // fall through: no instruction found
    return false;
  }

  template <SocModel SocType>
  int <%= name_of(:hart, cfg_arch) %><SocType>::_run_one()
  {
    Bits<INSTR_ENC_SIZE> enc;
    try {
       enc = _fetch();
    } catch(const AbortInstruction& e) {
      return StopReason::Exception;
    }
    InstBase* inst = reinterpret_cast<InstBase*>(m_run_one_inst_storage.data());
    if (_decode(m_pc, enc, inst) == false) {
      try {
        raise(ExceptionCode::IllegalInstruction, mode(), m_params.REPORT_ENCODING_IN_MTVAL_ON_ILLEGAL_INSTRUCTION ? static_cast<uint64_t>(enc) : 0ull);
      } catch (const AbortInstruction& e) {
        return StopReason::Exception;
     }
    }

    // set the fall-through next pc
    m_next_pc = m_pc + inst->enc_len();

    try {
      inst->execute();
    } catch (const udb::WfiException& e) {
      std::destroy_at(inst);
      advance_pc();
      return StopReason::Wfi;
    } catch (const udb::PauseException& e) {
      std::destroy_at(inst);
      advance_pc();
      return StopReason::Pause;
    } catch (const udb::UnpredictableBehaviorException& e) {
      std::destroy_at(inst);
      advance_pc();
      return StopReason::UnpredictableBehavior;
    } catch (const udb::ExitEvent& e) {
      this->m_exit_code = e.code();
      this->m_exit_reason = e.what();
      advance_pc();
      std::destroy_at(inst);
      if (e.code() == 0) {
        return StopReason::ExitSuccess;
      } else {
        return StopReason::ExitFailure;
      }
    }
    advance_pc();

    return StopReason::InstLimitReached;
  }

  template <SocModel SocType>
  int <%= name_of(:hart, cfg_arch) %><SocType>::_run_bb()
  {
    auto current_bb = m_bb_cache.get(m_pc);
    InstBase* inst;

    try {
      if (current_bb->start_pc() == m_pc) {
        current_bb->reset(); // hit, reset to the top of the basic block

        // now execute the entire bb
        const auto bb_size = current_bb->size();

        for (unsigned b = 0; b < bb_size; b++) {
          inst = current_bb->pop();

          // set the fall-through next pc
          m_next_pc = m_pc + inst->enc_len();

          inst->execute();
          if (this->m_exit_requested) {
            this->m_exit_requested = false; // reset the request
            break;
          }
          advance_pc();
        }
      } else {
        // miss, need to create the bb
        current_bb->recycle(m_pc);
        do {
          Bits<INSTR_ENC_SIZE> enc;
          enc = _fetch();

          inst = current_bb->alloc_inst();
          if (_decode(m_pc, enc, inst) == false) {
            raise(ExceptionCode::IllegalInstruction, mode(), m_params.REPORT_ENCODING_IN_MTVAL_ON_ILLEGAL_INSTRUCTION ? static_cast<uint64_t>(enc) : 0ull);
          }

          fmt::print("PC {:x} {}\n", m_pc, inst->disassemble());
          for (auto r : inst->srcRegs()) {
            fmt::print("R {} {:x}\n", r.to_string(), m_xregs[r.get_num()]);
          }

          // set the fall-through next pc
          m_next_pc = m_pc + inst->enc_len();

          inst->execute();

          for (auto r : inst->dstRegs()) {
            fmt::print("R= {} {:x}\n", r.to_string(), m_xregs[r.get_num()]);
          }

          if (this->m_exit_requested) {
            this->m_exit_requested = false; // reset the request
            break;
          }

          advance_pc();
        } while (!(current_bb->full() || inst->control_flow()));
      }
    } catch (const AbortInstruction& e) {
      current_bb->invalidate();
      advance_pc();
      return StopReason::Exception;
    } catch (const udb::WfiException& e) {
      current_bb->invalidate();
      advance_pc();
      return StopReason::Wfi;
    } catch (const udb::PauseException& e) {
      current_bb->invalidate();
      advance_pc();
      return StopReason::Pause;
    } catch (const udb::UnpredictableBehaviorException& e) {
      current_bb->invalidate();
      advance_pc();
      return StopReason::UnpredictableBehavior;
    } catch (const udb::ExitEvent& e) {
      current_bb->invalidate();
      advance_pc();
      this->m_exit_code = e.code();
      this->m_exit_reason = e.what();
      if (e.code() == 0) {
        return StopReason::ExitSuccess;
      } else {
        return StopReason::ExitFailure;
      }
    }

    return StopReason::InstLimitReached;
  }


  template <SocModel SocType>
  int <%= name_of(:hart, cfg_arch) %><SocType>::_run_n(uint64_t n)
  {
    while (n > 0) {
      if (n >= decltype(m_bb_cache)::MAX_BASIC_BLOCK_SIZE) {
        // safe to execute a bb
        auto before = this->m_num_inst_exec;
        auto reason = _run_bb();
        if (reason != StopReason::InstLimitReached) {
          return reason;
        }
        n -= (this->m_num_inst_exec - before);
      } else {
        auto reason = _run_one();
        if (reason != StopReason::InstLimitReached) {
          return reason;
        }
        n--;
      }
    }

    return StopReason::InstLimitReached;
  }


#undef __UDB_CONST_GLOBAL
#undef __UDB_MUTABLE_GLOBAL
#undef __UDB_FUNC_CALL
#undef __UDB_PC
}
