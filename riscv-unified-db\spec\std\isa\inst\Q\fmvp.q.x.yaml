# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fmvp.q.x
long_name: No synopsis available
description: |
  No description available.
definedBy:
  allOf: [Q, Zfa]
base: 64
assembly: fd, xs1, xs2
encoding:
  match: 1011011----------000-----1010011
  variables:
    - name: xs2
      location: 24-20
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
