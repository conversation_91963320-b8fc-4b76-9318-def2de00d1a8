# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zbc
long_name: Carry-less multiplication scalar instructions
description: |
  Carry-less multiplication is the multiplication in the polynomial ring over GF(2).
type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2021-06
    repositories:
      - url: https://github.com/riscv/riscv-bitmanip
        branch: main
    contributors:
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON><PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON><PERSON>
      - name: <PERSON>
      - name: <PERSON> <PERSON>
      - name: <PERSON><PERSON> <PERSON>
      - name: <PERSON>
      - name: <PERSON> <PERSON><PERSON>
      - name: <PERSON>
