<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Idl::IfSyntaxNode
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::IfSyntaxNode";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (I)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">IfSyntaxNode</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Idl::IfSyntaxNode
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="../Treetop/Runtime/SyntaxNode.html" title="Treetop::Runtime::SyntaxNode (class)">Treetop::Runtime::SyntaxNode</a></span></li>
          
            <li class="next">Idl::IfSyntaxNode</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb</dd>
  </dl>
  
</div>








  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#to_ast-instance_method" title="#to_ast (instance method)">#<strong>to_ast</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
    </ul>
  


  
  

  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="to_ast-instance_method">
  
    #<strong>to_ast</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


3915
3916
3917
3918
3919
3920
3921
3922
3923
3924
3925
3926
3927
3928
3929
3930
3931
3932
3933
3934
3935
3936
3937
3938
3939
3940
3941
3942
3943
3944</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 3915</span>

<span class='kw'>def</span> <span class='id identifier rubyid_to_ast'>to_ast</span>
  <span class='id identifier rubyid_if_body_stmts'>if_body_stmts</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='id identifier rubyid_if_body'>if_body</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='id identifier rubyid_if_body_stmts'>if_body_stmts</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_eifs'>eifs</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='kw'>unless</span> <span class='id identifier rubyid_elseifs'>elseifs</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
    <span class='id identifier rubyid_elseifs'>elseifs</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_eif'>eif</span><span class='op'>|</span>
      <span class='id identifier rubyid_stmts'>stmts</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
      <span class='id identifier rubyid_eif'>eif</span><span class='period'>.</span><span class='id identifier rubyid_body'>body</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
        <span class='id identifier rubyid_stmts'>stmts</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
      <span class='kw'>end</span>
      <span class='id identifier rubyid_eifs'>eifs</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="ElseIfAst.html" title="Idl::ElseIfAst (class)">ElseIfAst</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="ElseIfAst.html#initialize-instance_method" title="Idl::ElseIfAst#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_eif'>eif</span><span class='period'>.</span><span class='id identifier rubyid_expression'>expression</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_stmts'>stmts</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_final_else_stmts'>final_else_stmts</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='kw'>unless</span> <span class='id identifier rubyid_final_else'>final_else</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
    <span class='id identifier rubyid_final_else'>final_else</span><span class='period'>.</span><span class='id identifier rubyid_body'>body</span><span class='period'>.</span><span class='id identifier rubyid_elements'>elements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
      <span class='id identifier rubyid_final_else_stmts'>final_else_stmts</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_if_body_ast'>if_body_ast</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="IfBodyAst.html" title="Idl::IfBodyAst (class)">IfBodyAst</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="IfBodyAst.html#initialize-instance_method" title="Idl::IfBodyAst#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_if_body_stmts'>if_body_stmts</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_final_else_ast'>final_else_ast</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="IfBodyAst.html" title="Idl::IfBodyAst (class)">IfBodyAst</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="IfBodyAst.html#initialize-instance_method" title="Idl::IfBodyAst#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_final_else_stmts'>final_else_stmts</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_ast'>ast</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="IfAst.html" title="Idl::IfAst (class)">IfAst</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="IfAst.html#initialize-instance_method" title="Idl::IfAst#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_if_cond'>if_cond</span><span class='period'>.</span><span class='id identifier rubyid_to_ast'>to_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_if_body_ast'>if_body_ast</span><span class='comma'>,</span> <span class='id identifier rubyid_eifs'>eifs</span><span class='comma'>,</span> <span class='id identifier rubyid_final_else_ast'>final_else_ast</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_ast'>ast</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='id identifier rubyid_parent'>parent</span>
  <span class='id identifier rubyid_if_body_ast'>if_body_ast</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='id identifier rubyid_ast'>ast</span>
  <span class='id identifier rubyid_eifs'>eifs</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_eif'>eif</span><span class='op'>|</span> <span class='id identifier rubyid_eif'>eif</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='id identifier rubyid_ast'>ast</span> <span class='rbrace'>}</span>
  <span class='id identifier rubyid_final_else_ast'>final_else_ast</span><span class='period'>.</span><span class='id identifier rubyid_parent'>parent</span> <span class='op'>=</span> <span class='id identifier rubyid_ast'>ast</span>
  <span class='id identifier rubyid_ast'>ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>