# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.li
long_name: Load immediate large
description: |
  Loads the 32-bit immediate `imm` into `rd`.
  Instruction encoded in QC.EAI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcili
base: 32
encoding:
  match: --------------------------------0000-----0011111
  variables:
    - name: imm
      location: 47-16
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, imm"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = imm;
