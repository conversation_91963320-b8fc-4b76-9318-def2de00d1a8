# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mcycleh
long_name: High-half machine Cycle Counter
definedBy: Zicntr
address: 0xB80
writable: true
description: |
  High-half alias of `mcycle`.
priv_mode: M
length: 32
base: 32
fields:
  COUNT:
    location: 31-0
    type: RW-RH
    sw_write(csr_value): |
      # since writes to this register may not be hart-local, it must be handled
      # as a special case
      if (xlen() == 32) {
        return sw_write_mcycle({csr_value.COUNT[31:0], read_mcycle()[31:0]});
      } else {
        return sw_write_mcycle(csr_value.COUNT);
      }
    description: |
      Alias of upper half of `mcycle.COUNT`.
    reset_value: UNDEFINED_LEGAL
    affectedBy: [<PERSON><PERSON><PERSON><PERSON>, <PERSON>mcntrpmf, <PERSON>mcdeleg, Ssccfg]
sw_read(): |
  # since the counter may be shared among harts, reads must be handled
  # as a builtin function
  return read_mcycle()[63:32];
