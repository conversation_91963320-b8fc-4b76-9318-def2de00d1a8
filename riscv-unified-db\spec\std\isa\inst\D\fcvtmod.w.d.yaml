# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: fcvtmod.w.d
long_name: Floating-point Convert Double-precision to Word with Mo<PERSON>lo
description:
  - id: inst-fcvtmod.w.d-behaviour
    normative: false
    text: |
      `fcvtmod.w.d` always rounds towards zero. Bits 31:0 are taken from the rounded, unbounded two's
      complement result, then sign-extended to XLEN bits and written to integer register `xd`.±∞ and
      NaN are converted to zero.

      Floating-point exception flags are raised the same as they would be for FCVT.W.D with the same input
      operand.

      This instruction is only provided if the D extension is implemented. It is encoded like FCVT.W.D, but
      with the `xs2` field set to 8 and the `rm` field set to 1 (RTZ). Other `rm` values are reserved.
definedBy:
  allOf: [D, Zfa]
assembly: xd, fs1, rm
encoding:
  match: 110000101000-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
      not: [0, 2, 3, 4, 5, 6, 7]
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
