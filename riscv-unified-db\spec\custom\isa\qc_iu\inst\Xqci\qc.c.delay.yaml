# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.c.delay
long_name: Delay execution for immediate amount of cycles
description: |
  Delay execution for amount of cycles provided as immediate argument
  Instruction encoded in CI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisync
assembly: ""
base: 32
encoding:
  match: "000000000-----10"
  variables:
    - name: imm
      location: 6-2
      not: 0
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  delay(imm);
