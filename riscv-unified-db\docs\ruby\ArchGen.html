<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: ArchGen
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "ArchGen";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (A)</a> &raquo;
    
    
    <span class="title">ArchGen</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: ArchGen
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">ArchGen</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_gen.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Class to help parse parameterized arch definitions and generate a unified configuration</p>

<p>ArchGen is initialized with a config name, which <strong>must</strong> be the name of a directory under cfgs/</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#gen_dir-instance_method" title="#gen_dir (instance method)">#<strong>gen_dir</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>path where the result will be written.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>configuration name.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#all_known_exts-instance_method" title="#all_known_exts (instance method)">#<strong>all_known_exts</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>return list of all known extension names, even those not part of this config Includes both extensions defined in arch/ and those added through an overlay of the config.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#check_extension_dependencies-instance_method" title="#check_extension_dependencies (instance method)">#<strong>check_extension_dependencies</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#exception_codes-instance_method" title="#exception_codes (instance method)">#<strong>exception_codes</strong>  &#x21d2; Hash&lt;Integer, String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns mapping of exception codes to text name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#generate-instance_method" title="#generate (instance method)">#<strong>generate</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>generate the architecture definition into the gen directory.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_csrs-instance_method" title="#implemented_csrs (instance method)">#<strong>implemented_csrs</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all implemented CSRs.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_extensions-instance_method" title="#implemented_extensions (instance method)">#<strong>implemented_extensions</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all implemented extensions.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_instructions-instance_method" title="#implemented_instructions (instance method)">#<strong>implemented_instructions</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all implemented instructions.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(config_name)  &#x21d2; ArchGen </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Initialize an Architecture Generator.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#interrupt_codes-instance_method" title="#interrupt_codes (instance method)">#<strong>interrupt_codes</strong>  &#x21d2; Hash&lt;Integer, String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Returns mapping of interrupt codes to text name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#params-instance_method" title="#params (instance method)">#<strong>params</strong>  &#x21d2; Hash&lt;String, Object&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Hash of parameters for the config.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#validate_config-instance_method" title="#validate_config (instance method)">#<strong>validate_config</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>validates the configuration using cfgs/config_validation.rb.</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(config_name)  &#x21d2; <tt><span class='object_link'><a href="" title="ArchGen (class)">ArchGen</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Initialize an Architecture Generator</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>config_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The name of config located in the cfgs/ directory,</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 37</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_config_name'>config_name</span><span class='rparen'>)</span>
  <span class='ivar'>@validator</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Validator.html" title="Validator (class)">Validator</a></span></span><span class='period'>.</span><span class='id identifier rubyid_instance'>instance</span>

  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_config_name'>config_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
  <span class='ivar'>@cfg_dir</span> <span class='op'>=</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>cfgs</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='ivar'>@name</span>
  <span class='ivar'>@gen_dir</span> <span class='op'>=</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>gen</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='ivar'>@name</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>No config named &#39;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_exist?'>exist?</span><span class='lparen'>(</span><span class='ivar'>@cfg_dir</span><span class='rparen'>)</span>

  <span class='id identifier rubyid_cfg_params_path'>cfg_params_path</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='ivar'>@cfg_dir</span><span class='embexpr_end'>}</span><span class='tstring_content'>/params.yaml</span><span class='tstring_end'>&quot;</span></span>
  <span class='ivar'>@cfg</span> <span class='op'>=</span> <span class='ivar'>@validator</span><span class='period'>.</span><span class='id identifier rubyid_validate_str'>validate_str</span><span class='lparen'>(</span><span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='lparen'>(</span><span class='id identifier rubyid_cfg_params_path'>cfg_params_path</span><span class='rparen'>)</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='symbol'>:config</span><span class='rparen'>)</span>
  <span class='ivar'>@params</span> <span class='op'>=</span> <span class='ivar'>@cfg</span><span class='period'>.</span><span class='id identifier rubyid_fetch'>fetch</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>params</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>

  <span class='kw'>unless</span> <span class='ivar'>@params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>NAME</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='ivar'>@name</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>AbstractConfig name (</span><span class='embexpr_beg'>#{</span><span class='ivar'>@params</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&#39;</span><span class='tstring_content'>NAME</span><span class='tstring_end'>&#39;</span></span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>) in params.yaml does not match directory path (</span><span class='embexpr_beg'>#{</span><span class='ivar'>@name</span><span class='embexpr_end'>}</span><span class='tstring_content'>)</span><span class='tstring_end'>&quot;</span></span>
  <span class='kw'>end</span>

  <span class='ivar'>@opcode_data</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='gvar'>$root</span><span class='embexpr_end'>}</span><span class='tstring_content'>/ext/riscv-opcodes/instr_dict.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='ivar'>@ext_gen_complete</span> <span class='op'>=</span> <span class='kw'>false</span>

  <span class='id identifier rubyid_validate_config'>validate_config</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="gen_dir-instance_method">
  
    #<strong>gen_dir</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>path where the result will be written</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


26
27
28</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 26</span>

<span class='kw'>def</span> <span class='id identifier rubyid_gen_dir'>gen_dir</span>
  <span class='ivar'>@gen_dir</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>Object</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>configuration name</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


23
24
25</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 23</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="all_known_exts-instance_method">
  
    #<strong>all_known_exts</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>return list of all known extension names, even those not part of this config Includes both extensions defined in arch/ and those added through an overlay of the config</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all known extension names</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


587
588
589
590
591
592
593
594</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 587</span>

<span class='kw'>def</span> <span class='id identifier rubyid_all_known_exts'>all_known_exts</span>
  <span class='lparen'>(</span>
    <span class='const'>Dir</span><span class='period'>.</span><span class='id identifier rubyid_glob'>glob</span><span class='lparen'>(</span><span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ext</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>**</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>*.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>+</span>          <span class='comment'># exts in arch/
</span>    <span class='const'>Dir</span><span class='period'>.</span><span class='id identifier rubyid_glob'>glob</span><span class='lparen'>(</span><span class='ivar'>@cfg_dir</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch_overlay</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ext</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>**</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>*.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='comment'># exts in cfg/arch_overlay/
</span>  <span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_f'>f</span><span class='op'>|</span>
    <span class='const'>File</span><span class='period'>.</span><span class='id identifier rubyid_basename'>basename</span><span class='lparen'>(</span><span class='id identifier rubyid_f'>f</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="check_extension_dependencies-instance_method">
  
    #<strong>check_extension_dependencies</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


111
112
113
114
115
116
117
118
119
120
121
122
123
124
125
126
127
128</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 111</span>

<span class='kw'>def</span> <span class='id identifier rubyid_check_extension_dependencies'>check_extension_dependencies</span>
  <span class='ivar'>@implemented_extensions</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext'>ext</span><span class='op'>|</span>
    <span class='id identifier rubyid_requirements'>requirements</span> <span class='op'>=</span> <span class='ivar'>@required_ext_map</span><span class='lbracket'>[</span><span class='lbracket'>[</span><span class='id identifier rubyid_ext'>ext</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_ext'>ext</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>version</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rbracket'>]</span><span class='rbracket'>]</span>
    <span class='kw'>next</span> <span class='kw'>if</span> <span class='id identifier rubyid_requirements'>requirements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='id identifier rubyid_requirements'>requirements</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

    <span class='comment'># turn into an array, if needed
</span>    <span class='id identifier rubyid_requirements'>requirements</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='id identifier rubyid_requirements'>requirements</span><span class='rbracket'>]</span> <span class='kw'>unless</span> <span class='id identifier rubyid_requirements'>requirements</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_requirements'>requirements</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
      <span class='kw'>next</span> <span class='kw'>if</span> <span class='ivar'>@implemented_extensions</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
        <span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='id identifier rubyid_r'>r</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span> <span class='op'>&amp;&amp;</span>
        <span class='const'>Gem</span><span class='op'>::</span><span class='const'>Requirement</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='id identifier rubyid_r'>r</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='const'>Gem</span><span class='op'>::</span><span class='const'>Version</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>version</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rparen'>)</span>
      <span class='kw'>end</span>

      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Extension &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ext'>ext</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39; requires extension &#39;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_r'>r</span><span class='embexpr_end'>}</span><span class='tstring_content'>&#39;; it must also be implemented</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="exception_codes-instance_method">
  
    #<strong>exception_codes</strong>  &#x21d2; <tt>Hash&lt;Integer, String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns mapping of exception codes to text name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;Integer, String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Mapping of exception code number to text name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 668</span>

<span class='kw'>def</span> <span class='id identifier rubyid_exception_codes'>exception_codes</span>
  <span class='kw'>return</span> <span class='ivar'>@exception_codes</span> <span class='kw'>unless</span> <span class='ivar'>@exception_codes</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_gen_ext_def'>gen_ext_def</span> <span class='kw'>unless</span> <span class='ivar'>@ext_gen_complete</span>

  <span class='ivar'>@exception_codes</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='const'>Dir</span><span class='period'>.</span><span class='id identifier rubyid_glob'>glob</span><span class='lparen'>(</span><span class='ivar'>@gen_dir</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ext</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>*.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext_path'>ext_path</span><span class='op'>|</span>
    <span class='id identifier rubyid_ext_obj'>ext_obj</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_path'>ext_path</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_ext_obj'>ext_obj</span> <span class='op'>=</span> <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='lbracket'>[</span><span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='period'>.</span><span class='id identifier rubyid_keys'>keys</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rbracket'>]</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>exception_codes</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
      <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>exception_codes</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_exception_code'>exception_code</span><span class='op'>|</span>
        <span class='ivar'>@exception_codes</span><span class='lbracket'>[</span><span class='id identifier rubyid_exception_code'>exception_code</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>num</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_exception_code'>exception_code</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='ivar'>@exception_codes</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="generate-instance_method">
  
    #<strong>generate</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>generate the architecture definition into the gen directory</p>

<p>After calling this, gen/CFG_NAME/arch will be populated with up-to-date parsed (with ERB) and merged (with overlay) architecture files.</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


96
97
98
99
100
101
102
103
104
105
106
107
108
109</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 96</span>

<span class='kw'>def</span> <span class='id identifier rubyid_generate'>generate</span>
  <span class='comment'># extensions need to be parsed first since we pull, e.g., exception codes from them
</span>  <span class='id identifier rubyid_gen_ext_def'>gen_ext_def</span>
  <span class='id identifier rubyid_add_implied_extensions'>add_implied_extensions</span>
  <span class='id identifier rubyid_check_extension_dependencies'>check_extension_dependencies</span>

  <span class='id identifier rubyid_gen_csr_def'>gen_csr_def</span>

  <span class='id identifier rubyid_gen_inst_def'>gen_inst_def</span>

  <span class='id identifier rubyid_gen_arch_def'>gen_arch_def</span>

  <span class='ivar'>@generate_done</span> <span class='op'>=</span> <span class='kw'>true</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_csrs-instance_method">
  
    #<strong>implemented_csrs</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all implemented CSRs.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all implemented CSRs</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


158
159
160
161</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 158</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_csrs'>implemented_csrs</span>
  <span class='id identifier rubyid_generate'>generate</span> <span class='kw'>unless</span> <span class='ivar'>@generate_done</span>
  <span class='ivar'>@implemented_csrs</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_extensions-instance_method">
  
    #<strong>implemented_extensions</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all implemented extensions.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all implemented extensions</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


170
171
172
173</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 170</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_extensions'>implemented_extensions</span>
  <span class='id identifier rubyid_generate'>generate</span> <span class='kw'>unless</span> <span class='ivar'>@generate_done</span>
  <span class='ivar'>@implemented_extensions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_instructions-instance_method">
  
    #<strong>implemented_instructions</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all implemented instructions.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all implemented instructions</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


164
165
166
167</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 164</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_instructions'>implemented_instructions</span>
  <span class='id identifier rubyid_generate'>generate</span> <span class='kw'>unless</span> <span class='ivar'>@generate_done</span>
  <span class='ivar'>@implemented_instructions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="interrupt_codes-instance_method">
  
    #<strong>interrupt_codes</strong>  &#x21d2; <tt>Hash&lt;Integer, String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns mapping of interrupt codes to text name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;Integer, String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Mapping of interrupt code number to text name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


689
690
691
692
693
694
695
696
697
698
699
700
701
702
703
704
705</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 689</span>

<span class='kw'>def</span> <span class='id identifier rubyid_interrupt_codes'>interrupt_codes</span>
  <span class='kw'>return</span> <span class='ivar'>@interrupt_codes</span> <span class='kw'>unless</span> <span class='ivar'>@interrupt_codes</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_gen_ext_def'>gen_ext_def</span> <span class='kw'>unless</span> <span class='ivar'>@ext_gen_complete</span>

  <span class='ivar'>@interrupt_codes</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='const'>Dir</span><span class='period'>.</span><span class='id identifier rubyid_glob'>glob</span><span class='lparen'>(</span><span class='ivar'>@gen_dir</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>ext</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>*.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext_path'>ext_path</span><span class='op'>|</span>
    <span class='id identifier rubyid_ext_obj'>ext_obj</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_path'>ext_path</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_ext_obj'>ext_obj</span> <span class='op'>=</span> <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='lbracket'>[</span><span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='period'>.</span><span class='id identifier rubyid_keys'>keys</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rbracket'>]</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>interrupt_codes</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
      <span class='id identifier rubyid_ext_obj'>ext_obj</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>interrupt_codes</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_interrupt_code'>interrupt_code</span><span class='op'>|</span>
        <span class='ivar'>@interrupt_codes</span><span class='lbracket'>[</span><span class='id identifier rubyid_interrupt_code'>interrupt_code</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>num</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_interrupt_code'>interrupt_code</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='ivar'>@interrupt_codes</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="params-instance_method">
  
    #<strong>params</strong>  &#x21d2; <tt>Hash&lt;String, Object&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Hash of parameters for the config.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;String, Object&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Hash of parameters for the config</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


794</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 794</span>

<span class='kw'>def</span> <span class='id identifier rubyid_params'>params</span> <span class='op'>=</span> <span class='ivar'>@params</span><span class='period'>.</span><span class='id identifier rubyid_select'>select</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_k'>k</span><span class='comma'>,</span> <span class='id identifier rubyid__v'>_v</span><span class='op'>|</span> <span class='id identifier rubyid_k'>k</span><span class='period'>.</span><span class='id identifier rubyid_upcase'>upcase</span> <span class='op'>==</span> <span class='id identifier rubyid_k'>k</span> <span class='rbrace'>}</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="validate_config-instance_method">
  
    #<strong>validate_config</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>validates the configuration using cfgs/config_validation.rb</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_gen.rb', line 61</span>

<span class='kw'>def</span> <span class='id identifier rubyid_validate_config'>validate_config</span>
  <span class='id identifier rubyid_fork'>fork</span> <span class='kw'>do</span>
    <span class='id identifier rubyid_validation_file'>validation_file</span> <span class='op'>=</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>cfgs</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>config_validation.rb</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_validation_env'>validation_env</span> <span class='op'>=</span> <span class='id identifier rubyid_env'>env</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span>
    <span class='id identifier rubyid_validation_env'>validation_env</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_define_method'>define_method</span><span class='lparen'>(</span><span class='symbol'>:require_param</span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_param_name'>param_name</span><span class='op'>|</span>
      <span class='kw'>return</span> <span class='kw'>if</span> <span class='id identifier rubyid_constants'>constants</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_param_name'>param_name</span><span class='period'>.</span><span class='id identifier rubyid_to_sym'>to_sym</span><span class='rparen'>)</span>

      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>At </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_caller'>caller</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Configuration is missing required parameter </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_param_name'>param_name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
      <span class='const'>Kernel</span><span class='period'>.</span><span class='id identifier rubyid_exit!'>exit!</span> <span class='int'>1</span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_validation_env'>validation_env</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_define_method'>define_method</span><span class='lparen'>(</span><span class='symbol'>:require_ext</span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='op'>|</span>
      <span class='kw'>return</span> <span class='kw'>if</span> <span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='period'>.</span><span class='id identifier rubyid_to_sym'>to_sym</span><span class='rparen'>)</span>

      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>At </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_caller'>caller</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Configuration is missing required extension </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
      <span class='const'>Kernel</span><span class='period'>.</span><span class='id identifier rubyid_exit!'>exit!</span> <span class='int'>1</span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_validation_env'>validation_env</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_define_method'>define_method</span><span class='lparen'>(</span><span class='symbol'>:assert</span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_condition'>condition</span><span class='op'>|</span>
      <span class='kw'>return</span> <span class='kw'>if</span> <span class='id identifier rubyid_condition'>condition</span>

      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>At </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_caller'>caller</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='embexpr_end'>}</span><span class='tstring_content'>:</span><span class='tstring_end'>&quot;</span></span>
      <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Configuration check failed</span><span class='tstring_end'>&quot;</span></span>
      <span class='const'>Kernel</span><span class='period'>.</span><span class='id identifier rubyid_exit!'>exit!</span> <span class='int'>1</span>
    <span class='kw'>end</span>
    <span class='id identifier rubyid_env'>env</span><span class='period'>.</span><span class='id identifier rubyid_clone'>clone</span><span class='period'>.</span><span class='id identifier rubyid_class_eval'>class_eval</span> <span class='id identifier rubyid_validation_file'>validation_file</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='comma'>,</span> <span class='id identifier rubyid_validation_file'>validation_file</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='comma'>,</span> <span class='int'>1</span>
  <span class='kw'>end</span>
  <span class='const'>Process</span><span class='period'>.</span><span class='id identifier rubyid_wait'>wait</span>
  <span class='id identifier rubyid_exit'>exit</span> <span class='int'>1</span> <span class='kw'>unless</span> <span class='gvar'>$CHILD_STATUS</span><span class='period'>.</span><span class='id identifier rubyid_success?'>success?</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>