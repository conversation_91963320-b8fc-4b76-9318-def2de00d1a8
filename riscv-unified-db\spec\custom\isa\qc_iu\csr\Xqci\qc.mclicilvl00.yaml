# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl00
long_name: IRQ Level 0
address: 0xbc0
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 0-7
fields:
  IRQ0:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ0 level
  IRQ1:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ1 level
  IRQ2:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ2 level
  IRQ3:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ3 level
  IRQ4:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ4 level
  IRQ5:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ5 level
  IRQ6:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ6 level
  IRQ7:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ7 level
