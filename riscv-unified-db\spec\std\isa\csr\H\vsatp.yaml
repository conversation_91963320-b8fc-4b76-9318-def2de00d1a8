# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: vsatp
address: 0x280
writable: true
virtual_address: 0x180
long_name: Virtual Supervisor Address Translation and Protection
description: |
  The `vsatp` register is a VSXLEN-bit read/write register that is VS-mode's version of supervisor
  register `satp`.
  When V=1, `vsatp` substitutes for the usual `satp`, so instructions that normally read or modify
  `satp` actually access `vsatp` instead.
  `vsatp` controls VS-stage address translation, the first stage of two-stage translation for
  guest virtual addresses.

  The `vsatp` register is considered active for the purposes of the address-translation algorithm
  unless the effective privilege mode is U and `hstatus.HU`=0.
  However, even when `vsatp` is active, VS-stage page-table entries' A bits must not be set as a
  result of speculative execution, unless the effective privilege mode is VS or VU.

  [NOTE]
  In particular, virtual-machine load/store (`hlv`, 'hlvx', or 'hsv') instructions that are
  misspeculatively executed must not cause VS-stage A bits to be set.

  When V=0, a write to `vsatp` with an unsupported MODE value is either ignored as it is for
  `satp`, or the fields of `vsatp` are treated as WARL in the normal way.
  However, when V=1, a write to `satp` with an unsupported MODE value is ignored and no write to
  `vsatp` is effected.
priv_mode: VS
length: VSXLEN
definedBy: H
fields:
  MODE:
    location_rv64: 63-60
    location_rv32: 31
    type: RW-R
    description: |
      *Translation Mode*

      Controls the current translation mode in VS-mode according to the table below.

      [separator="!",%autowidth]
      !===
      ! Value ! Name ! Description

      ! 0 ! Bare a! No translation -> virtual address == physical address
      <%- if ext?(:Sv39) -%>
      ! 8 ! Sv39 ! 39-bit virtual address translation
      <%- end -%>
      <%- if ext?(:Sv48) -%>
      ! 9 ! Sv48 ! 48-bit virtual address translation
      <%- end -%>
      <%- if ext?(:Sv57) -%>
      ! 10 ! Sv57 ! 57-bit virtual address translation
      <%- end -%>
      !===

      Any other value shall be ignored on a write.
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (csr_value.MODE == 0) {
        if (virtual_mode?() || IGNORE_INVALID_VSATP_MODE_WRITES_WHEN_V_EQ_ZERO) {
          # In Bare, ASID and PPN must be zero, else the entire write is ignored
          if (csr_value.ASID == 0 && csr_value.PPN == 0) {
            return csr_value.MODE;
          } else {
            return UNDEFINED_LEGAL_DETERMINISTIC;
          }
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      }
      else if (implemented?(ExtensionName::Sv39) && csr_value.MODE == 8) { return csr_value.MODE; }
      else if (implemented?(ExtensionName::Sv48) && csr_value.MODE == 9) { return csr_value.MODE; }
      else if (implemented?(ExtensionName::Sv57) && csr_value.MODE == 10) { return csr_value.MODE; }
      else {
        return UNDEFINED_LEGAL_DETERMINISTIC;
      }
  ASID:
    location_rv32: 30-22
    location_rv64: 59-44
    description: |
      *Address Space ID*
    type: RW-R
    sw_write(csr_value): |
      if (csr_value.MODE == 0) {
        if (virtual_mode?() || IGNORE_INVALID_VSATP_MODE_WRITES_WHEN_V_EQ_ZERO) {
          # when MODE == Bare, PPN and ASID must be zero
          if (csr_value.ASID == 0 && csr_value.PPN == 0) {
            return csr_value.ASID;
          } else {
            return UNDEFINED_LEGAL_DETERMINISTIC;
          }
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      } else {
        XReg shamt = ((xlen() == 32) || (CSR[mstatus].SXL == $bits(XRegWidth::XLEN32))) ? 9 : 16;
        XReg all_ones = ((MXLEN'1 << shamt) - 1);
        XReg largest_allowed_asid = (MXLEN'1 << shamt) - 1;

        if (csr_value.ASID == all_ones) {
          # the specification states that if all 1's are written to the ASID field, then
          # you must return the largest asid
          return largest_allowed_asid;
        } else if (csr_value.ASID > largest_allowed_asid) {
          # ... but is silent on what happens on any other illegal value
          return UNDEFINED_LEGAL_DETERMINISTIC;
        } else {
          # unrestricted
          return csr_value.ASID;
        }
      }
    reset_value: UNDEFINED_LEGAL
  PPN:
    location_rv32: 21-0
    location_rv64: 43-0
    description: |
      *Physical Page Number*

      The physical address of the active root page table is PPN << 12.

      Can only hold values that correspond to a valid page table base, which
      will be implementation-dependent.
    type: RW-R
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (csr_value.MODE == 0) {
        if (virtual_mode?() || IGNORE_INVALID_VSATP_MODE_WRITES_WHEN_V_EQ_ZERO) {
          # when MODE == Bare, PPN and ASID must be zero
          if (csr_value.ASID == 0 && csr_value.PPN == 0) {
            return csr_value.PPN;
          } else {
            return UNDEFINED_LEGAL_DETERMINISTIC;
          }
        } else {
          return UNDEFINED_LEGAL_DETERMINISTIC;
        }
      } else {
        # unrestricted
        return csr_value.PPN;
      }
