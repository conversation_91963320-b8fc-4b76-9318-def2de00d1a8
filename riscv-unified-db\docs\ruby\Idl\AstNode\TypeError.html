<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Exception: Idl::AstNode::TypeError
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::AstNode::TypeError";
  relpath = '../../';
</script>


  <script type="text/javascript" charset="utf-8" src="../../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../../_index.html">Index (T)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../../Idl.html" title="Idl (module)">Idl</a></span></span> &raquo; <span class='title'><span class='object_link'><a href="../AstNode.html" title="Idl::AstNode (class)">AstNode</a></span></span>
     &raquo; 
    <span class="title">TypeError</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Exception: Idl::AstNode::TypeError
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">StandardError</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">StandardError</li>
          
            <li class="next">Idl::AstNode::TypeError</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>error that is thrown when compilation reveals a type error</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#bt-instance_method" title="#bt (instance method)">#<strong>bt</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The backtrace starting from the ‘type_error’ call site.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#what-instance_method" title="#what (instance method)">#<strong>what</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The error message.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(what)  &#x21d2; TypeError </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of TypeError.</p>
</div></span>
  
</li>

      
    </ul>
  


  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(what)  &#x21d2; <tt><span class='object_link'><a href="" title="Idl::AstNode::TypeError (class)">TypeError</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of TypeError.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>what</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Error message</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


122
123
124
125
126
127
128
129
130
131</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 122</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_what'>what</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_what'>what</span><span class='rparen'>)</span>

  <span class='ivar'>@what</span> <span class='op'>=</span> <span class='id identifier rubyid_what'>what</span>
  <span class='ivar'>@bt</span> <span class='op'>=</span> <span class='const'>Kernel</span><span class='period'>.</span><span class='id identifier rubyid_caller'>caller</span>

  <span class='comment'># shift twice to get to the call site of &#39;type_error&#39;
</span>  <span class='ivar'>@bt</span><span class='period'>.</span><span class='id identifier rubyid_shift'>shift</span>
  <span class='ivar'>@bt</span><span class='period'>.</span><span class='id identifier rubyid_shift'>shift</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="bt-instance_method">
  
    #<strong>bt</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>The backtrace starting from the ‘type_error’ call site</p>

<p>Note, this will be different (truncated) from #backtrace</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The compiler backtrace at the error point</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


119
120
121</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 119</span>

<span class='kw'>def</span> <span class='id identifier rubyid_bt'>bt</span>
  <span class='ivar'>@bt</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="what-instance_method">
  
    #<strong>what</strong>  &#x21d2; <tt>String</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The error message.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The error message</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


112
113
114</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 112</span>

<span class='kw'>def</span> <span class='id identifier rubyid_what'>what</span>
  <span class='ivar'>@what</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>