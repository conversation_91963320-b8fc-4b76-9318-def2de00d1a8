<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: ArchDef
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "ArchDef";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (A)</a> &raquo;
    
    
    <span class="title">ArchDef</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: ArchDef
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName">Object</span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next">ArchDef</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Object model for a configured architecture definition</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#config_params-instance_method" title="#config_params (instance method)">#<strong>config_params</strong>  &#x21d2; Hash </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The configuration parameters.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#global_ast-instance_method" title="#global_ast (instance method)">#<strong>global_ast</strong>  &#x21d2; Idl::AstNode </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Abstract syntax tree of global scope.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#idl_compiler-instance_method" title="#idl_compiler (instance method)">#<strong>idl_compiler</strong>  &#x21d2; Idl::Compiler </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The IDL compiler.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#name-instance_method" title="#name (instance method)">#<strong>name</strong>  &#x21d2; String </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Name of the architecture configuration.</p>
</div></span>
  
</li>

    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#sym_table-instance_method" title="#sym_table (instance method)">#<strong>sym_table</strong>  &#x21d2; SymbolTable </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The symbol table containing global definitions.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#all_known_csr_names-instance_method" title="#all_known_csr_names (instance method)">#<strong>all_known_csr_names</strong>  &#x21d2; Array&lt;String&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all known CSRs, even those not implemented by this config.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#csr-instance_method" title="#csr (instance method)">#<strong>csr</strong>(csr_name)  &#x21d2; Csr<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A specific csr, or nil if it doesn’t exist.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#csr_hash-instance_method" title="#csr_hash (instance method)">#<strong>csr_hash</strong>  &#x21d2; Hash&lt;String, Csr&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>All csrs, even unimplemented ones, indexed by CSR name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#csrs-instance_method" title="#csrs (instance method)">#<strong>csrs</strong>  &#x21d2; Array&lt;Csr&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all CSRs defined by RISC-V, whether or not they are implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#data-instance_method" title="#data (instance method)">#<strong>data</strong>  &#x21d2; Hash </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The raw architecture defintion data structure.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#ext%3F-instance_method" title="#ext? (instance method)">#<strong>ext?</strong>(ext_name, *ext_version_requirements)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extension-instance_method" title="#extension (instance method)">#<strong>extension</strong>(name)  &#x21d2; Extension<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extension_hash-instance_method" title="#extension_hash (instance method)">#<strong>extension_hash</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extensions-instance_method" title="#extensions (instance method)">#<strong>extensions</strong>  &#x21d2; Array&lt;Extesion&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all extensions, even those that are’t implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#convert_monospace_to_links-instance_method" title="#convert_monospace_to_links (instance method)">#<strong>convert_monospace_to_links</strong>(adoc)  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>given an adoc string, find names of CSR/Instruction/Extension enclosed in ‘monospace` and replace them with links to the relevant object page.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_csr-instance_method" title="#implemented_csr (instance method)">#<strong>implemented_csr</strong>(csr_name)  &#x21d2; Csr<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A specific csr, or nil if it doesn’t exist or isn’t implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_csr_hash-instance_method" title="#implemented_csr_hash (instance method)">#<strong>implemented_csr_hash</strong>  &#x21d2; Hash&lt;String, Csr&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Implemented csrs, indexed by CSR name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_csrs-instance_method" title="#implemented_csrs (instance method)">#<strong>implemented_csrs</strong>  &#x21d2; Array&lt;Csr&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all implemented CSRs.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_extensions-instance_method" title="#implemented_extensions (instance method)">#<strong>implemented_extensions</strong>  &#x21d2; Array&lt;ExtensionVersion&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all extensions, with specific versions, that are implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implemented_instructions-instance_method" title="#implemented_instructions (instance method)">#<strong>implemented_instructions</strong>  &#x21d2; Array&lt;Instruction&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all implemented instructions.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(config_name)  &#x21d2; ArchDef </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Initialize a new configured architecture defintiion.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#inst-instance_method" title="#inst (instance method)">#<strong>inst</strong>(inst_name)  &#x21d2; Instruction<sup>?</sup> </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>An instruction named ‘inst_name’, or nil if it doesn’t exist.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#instruction_hash-instance_method" title="#instruction_hash (instance method)">#<strong>instruction_hash</strong>  &#x21d2; Hash&lt;String, Instruction&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>All instructions, indexed by name.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#instructions-instance_method" title="#instructions (instance method)">#<strong>instructions</strong>  &#x21d2; Array&lt;Instruction&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>List of all instructions, whether or not they are implemented.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#multi_xlen%3F-instance_method" title="#multi_xlen? (instance method)">#<strong>multi_xlen?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>(i.e., that in some mode the effective xlen can be either 32 or 64, depending on CSR values).</p>
</div></span>
  
</li>

      
    </ul>
  

<div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(config_name)  &#x21d2; <tt><span class='object_link'><a href="" title="ArchDef (class)">ArchDef</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Initialize a new configured architecture defintiion</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1345
1346
1347
1348
1349
1350
1351
1352
1353
1354
1355
1356
1357
1358
1359
1360
1361
1362
1363
1364
1365
1366
1367
1368
1369</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1345</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_config_name'>config_name</span><span class='rparen'>)</span>
  <span class='ivar'>@name</span> <span class='op'>=</span> <span class='id identifier rubyid_config_name'>config_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
  <span class='id identifier rubyid_arch_def_file'>arch_def_file</span> <span class='op'>=</span> <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>gen</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='ivar'>@name</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch_def.yaml</span><span class='tstring_end'>&quot;</span></span>

  <span class='id identifier rubyid_validator'>validator</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Validator.html" title="Validator (class)">Validator</a></span></span><span class='period'>.</span><span class='id identifier rubyid_instance'>instance</span>
  <span class='kw'>begin</span>
    <span class='id identifier rubyid_validator'>validator</span><span class='period'>.</span><span class='id identifier rubyid_validate_str'>validate_str</span><span class='lparen'>(</span><span class='id identifier rubyid_arch_def_file'>arch_def_file</span><span class='period'>.</span><span class='id identifier rubyid_read'>read</span><span class='comma'>,</span> <span class='label'>type:</span> <span class='symbol'>:arch</span><span class='rparen'>)</span>
  <span class='kw'>rescue</span> <span class='const'><span class='object_link'><a href="Validator.html" title="Validator (class)">Validator</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Validator/ValidationError.html" title="Validator::ValidationError (class)">ValidationError</a></span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span>
    <span class='id identifier rubyid_warn'>warn</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>While parsing unified architecture definition at </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_arch_def_file'>arch_def_file</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='id identifier rubyid_e'>e</span>
  <span class='kw'>end</span>

  <span class='ivar'>@arch_def</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='id identifier rubyid_arch_def_file'>arch_def_file</span><span class='rparen'>)</span>

  <span class='ivar'>@config_params</span> <span class='op'>=</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>params</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>

  <span class='ivar'>@sym_table</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/SymbolTable.html#initialize-instance_method" title="Idl::SymbolTable#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='rparen'>)</span>
  <span class='ivar'>@idl_compiler</span> <span class='op'>=</span> <span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/Compiler.html" title="Idl::Compiler (class)">Compiler</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Idl/Compiler.html#initialize-instance_method" title="Idl::Compiler#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='kw'>self</span><span class='rparen'>)</span>

  <span class='comment'># load the globals into the symbol table
</span>  <span class='ivar'>@global_ast</span> <span class='op'>=</span> <span class='ivar'>@idl_compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_file'>compile_file</span><span class='lparen'>(</span>
    <span class='gvar'>$root</span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>arch</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>isa</span><span class='tstring_end'>&quot;</span></span> <span class='op'>/</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>globals.isa</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span>
    <span class='ivar'>@sym_table</span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="config_params-instance_method">
  
    #<strong>config_params</strong>  &#x21d2; <tt>Hash</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The configuration parameters.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The configuration parameters</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1333
1334
1335</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1333</span>

<span class='kw'>def</span> <span class='id identifier rubyid_config_params'>config_params</span>
  <span class='ivar'>@config_params</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="global_ast-instance_method">
  
    #<strong>global_ast</strong>  &#x21d2; <tt><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">Idl::AstNode</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Abstract syntax tree of global scope.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">Idl::AstNode</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Abstract syntax tree of global scope</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1339
1340
1341</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1339</span>

<span class='kw'>def</span> <span class='id identifier rubyid_global_ast'>global_ast</span>
  <span class='ivar'>@global_ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="idl_compiler-instance_method">
  
    #<strong>idl_compiler</strong>  &#x21d2; <tt><span class='object_link'><a href="Idl/Compiler.html" title="Idl::Compiler (class)">Idl::Compiler</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The IDL compiler.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/Compiler.html" title="Idl::Compiler (class)">Idl::Compiler</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The IDL compiler</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1336
1337
1338</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1336</span>

<span class='kw'>def</span> <span class='id identifier rubyid_idl_compiler'>idl_compiler</span>
  <span class='ivar'>@idl_compiler</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="name-instance_method">
  
    #<strong>name</strong>  &#x21d2; <tt>String</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Name of the architecture configuration.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Name of the architecture configuration</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1327
1328
1329</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1327</span>

<span class='kw'>def</span> <span class='id identifier rubyid_name'>name</span>
  <span class='ivar'>@name</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      
      <span id=""></span>
      <div class="method_details ">
  <h3 class="signature " id="sym_table-instance_method">
  
    #<strong>sym_table</strong>  &#x21d2; <tt>SymbolTable</tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The symbol table containing global definitions.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>SymbolTable</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The symbol table containing global definitions</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1330
1331
1332</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1330</span>

<span class='kw'>def</span> <span class='id identifier rubyid_sym_table'>sym_table</span>
  <span class='ivar'>@sym_table</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="all_known_csr_names-instance_method">
  
    #<strong>all_known_csr_names</strong>  &#x21d2; <tt>Array&lt;String&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all known CSRs, even those not implemented by this config.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;String&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all known CSRs, even those not implemented by this config</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1468
1469
1470</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1468</span>

<span class='kw'>def</span> <span class='id identifier rubyid_all_known_csr_names'>all_known_csr_names</span>
  <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>csrs</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_csr'>csr</span><span class='op'>|</span> <span class='id identifier rubyid_csr'>csr</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="csr-instance_method">
  
    #<strong>csr</strong>(csr_name)  &#x21d2; <tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a specific csr, or nil if it doesn’t exist.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>csr_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>CSR name</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>a specific csr, or nil if it doesn’t exist</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1502
1503
1504</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1502</span>

<span class='kw'>def</span> <span class='id identifier rubyid_csr'>csr</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_csr_hash'>csr_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="csr_hash-instance_method">
  
    #<strong>csr_hash</strong>  &#x21d2; <tt>Hash&lt;String, <span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns All csrs, even unimplemented ones, indexed by CSR name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;String, <span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>All csrs, even unimplemented ones, indexed by CSR name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1484
1485
1486
1487
1488
1489
1490
1491
1492</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1484</span>

<span class='kw'>def</span> <span class='id identifier rubyid_csr_hash'>csr_hash</span>
  <span class='kw'>return</span> <span class='ivar'>@csr_hash</span> <span class='kw'>unless</span> <span class='ivar'>@csr_hash</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@csr_hash</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='id identifier rubyid_csrs'>csrs</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_csr'>csr</span><span class='op'>|</span>
    <span class='ivar'>@csr_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_csr'>csr</span>
  <span class='kw'>end</span>
  <span class='ivar'>@csr_hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="csrs-instance_method">
  
    #<strong>csrs</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all CSRs defined by RISC-V, whether or not they are implemented.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all CSRs defined by RISC-V, whether or not they are implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1458
1459
1460
1461
1462
1463
1464</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1458</span>

<span class='kw'>def</span> <span class='id identifier rubyid_csrs'>csrs</span>
  <span class='kw'>return</span> <span class='ivar'>@csrs</span> <span class='kw'>unless</span> <span class='ivar'>@csrs</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@csrs</span> <span class='op'>=</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>csrs</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid__csr_name'>_csr_name</span><span class='comma'>,</span> <span class='id identifier rubyid_csr_data'>csr_data</span><span class='op'>|</span>
    <span class='const'><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Csr.html#initialize-instance_method" title="Csr#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_csr_data'>csr_data</span><span class='comma'>,</span> <span class='ivar'>@sym_table</span><span class='comma'>,</span> <span class='kw'>self</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="data-instance_method">
  
    #<strong>data</strong>  &#x21d2; <tt>Hash</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The raw architecture defintion data structure.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The raw architecture defintion data structure</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1444
1445
1446</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1444</span>

<span class='kw'>def</span> <span class='id identifier rubyid_data'>data</span>
  <span class='ivar'>@arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="ext?-instance_method">
  
    
      <span class="overload">#<strong>ext?</strong>(ext_name)  &#x21d2; <tt>Boolean</tt> </span>
    
      <span class="overload">#<strong>ext?</strong>(ext_name, ext_version_requirements)  &#x21d2; <tt>Boolean</tt> </span>
    
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
  <p class="tag_title">Overloads:</p>
  <ul class="overload">
    
      
      <li class="overload_item">
        <span class="signature">#<strong>ext?</strong>(ext_name)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns True if the extension ‘name` is implemented.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension name (case sensitive)</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>True if the extension ‘name` is implemented</p>
</div>
      
    </li>
  
</ul>

</div>
      </li>
    
      
      <li class="overload_item">
        <span class="signature">#<strong>ext?</strong>(ext_name, ext_version_requirements)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns True if the extension ‘name` meeting `ext_version_requirements` is implemented.</p>


  </div>
</div>
<div class="tags">
  
  <div class="examples">
    <p class="tag_title">Examples:</p>
    
      
        <p class="example_title"><div class='inline'>
<p>Checking extension presence with a version requirement</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='symbol'>:S</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&gt;= 1.12</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span></code></pre>
    
      
        <p class="example_title"><div class='inline'>
<p>Checking extension presence with multiple version requirements</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='symbol'>:S</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&gt;= 1.12</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&lt; 1.15</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span></code></pre>
    
      
        <p class="example_title"><div class='inline'>
<p>Checking extension precsence with a precise version requirement</p>
</div></p>
      
      <pre class="example code"><code><span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='symbol'>:S</span><span class='comma'>,</span> <span class='float'>1.12</span><span class='rparen'>)</span></code></pre>
    
  </div>
<p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension name (case sensitive)</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>ext_version_requirements</span>
      
      
        <span class='type'>(<tt>Number</tt>, <tt>String</tt>, <tt>Array</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension version requirements, taking the same inputs as Gem::Requirement</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>True if the extension ‘name` meeting `ext_version_requirements` is implemented</p>
</div>
      
    </li>
  
</ul>

  <p class="tag_title">See Also:</p>
  <ul class="see">
    
      <li><a href="https://docs.ruby-lang.org/en/3.0/Gem/Requirement.html#method-c-new" target="_parent" title="Gem::Requirement#new">Gem::Requirement#new</a></li>
    
  </ul>

</div>
      </li>
    
  </ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1432
1433
1434
1435
1436
1437
1438
1439
1440
1441</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1432</span>

<span class='kw'>def</span> <span class='id identifier rubyid_ext?'>ext?</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_name'>ext_name</span><span class='comma'>,</span> <span class='op'>*</span><span class='id identifier rubyid_ext_version_requirements'>ext_version_requirements</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_implemented_extensions'>implemented_extensions</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='kw'>if</span> <span class='id identifier rubyid_ext_version_requirements'>ext_version_requirements</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>
      <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='id identifier rubyid_ext_name'>ext_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_requirement'>requirement</span> <span class='op'>=</span> <span class='const'>Gem</span><span class='op'>::</span><span class='const'>Requirement</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_version_requirements'>ext_version_requirements</span><span class='rparen'>)</span>
      <span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span> <span class='op'>==</span> <span class='id identifier rubyid_ext_name'>ext_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_requirement'>requirement</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_version'>version</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extension-instance_method">
  
    #<strong>extension</strong>(name)  &#x21d2; <tt><span class='object_link'><a href="Extension.html" title="Extension (class)">Extension</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension name</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Extension.html" title="Extension (class)">Extension</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension named ‘name`</p>
</div>
      
    </li>
  
    <li>
      
      
        <span class='type'>(<tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>if no extension ‘name` exists</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1414
1415
1416</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1414</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extension'>extension</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_extension_hash'>extension_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_name'>name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extension_hash-instance_method">
  
    #<strong>extension_hash</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1401
1402
1403
1404
1405
1406
1407
1408
1409</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1401</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extension_hash'>extension_hash</span>
  <span class='kw'>return</span> <span class='ivar'>@extension_hash</span> <span class='kw'>unless</span> <span class='ivar'>@extension_hash</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@extension_hash</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='id identifier rubyid_extensions'>extensions</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext'>ext</span><span class='op'>|</span>
    <span class='ivar'>@extension_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_ext'>ext</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_ext'>ext</span>
  <span class='kw'>end</span>
  <span class='ivar'>@extension_hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extensions-instance_method">
  
    #<strong>extensions</strong>  &#x21d2; <tt>Array&lt;Extesion&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all extensions, even those that are’t implemented.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;Extesion&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all extensions, even those that are’t implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1390
1391
1392
1393
1394
1395
1396
1397
1398</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1390</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extensions'>extensions</span>
  <span class='kw'>return</span> <span class='ivar'>@extensions</span> <span class='kw'>unless</span> <span class='ivar'>@extensions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@extensions</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>extensions</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each_value'>each_value</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_ext_data'>ext_data</span><span class='op'>|</span>
    <span class='ivar'>@extensions</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="Extension.html" title="Extension (class)">Extension</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Extension.html#initialize-instance_method" title="Extension#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_ext_data'>ext_data</span><span class='comma'>,</span> <span class='kw'>self</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
  <span class='ivar'>@extensions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="convert_monospace_to_links-instance_method">
  
    #<strong>convert_monospace_to_links</strong>(adoc)  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>given an adoc string, find names of CSR/Instruction/Extension enclosed in ‘monospace` and replace them with links to the relevant object page</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>adoc</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Asciidoc source</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Asciidoc source, with link placeholders</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1552
1553
1554
1555
1556
1557
1558
1559
1560
1561
1562
1563
1564
1565
1566
1567
1568
1569</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1552</span>

<span class='kw'>def</span> <span class='id identifier rubyid_convert_monospace_to_links'>convert_monospace_to_links</span><span class='lparen'>(</span><span class='id identifier rubyid_adoc'>adoc</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_adoc'>adoc</span><span class='period'>.</span><span class='id identifier rubyid_gsub'>gsub</span><span class='lparen'>(</span><span class='tstring'><span class='regexp_beg'>/</span><span class='tstring_content'>`([\w.]+)`</span><span class='regexp_end'>/</span></span><span class='rparen'>)</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_match'>match</span><span class='op'>|</span>
    <span class='id identifier rubyid_name'>name</span> <span class='op'>=</span> <span class='const'>Regexp</span><span class='period'>.</span><span class='id identifier rubyid_last_match'>last_match</span><span class='lparen'>(</span><span class='int'>1</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_csr_name'>csr_name</span><span class='comma'>,</span> <span class='id identifier rubyid_field_name'>field_name</span> <span class='op'>=</span> <span class='id identifier rubyid_name'>name</span><span class='period'>.</span><span class='id identifier rubyid_split'>split</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>.</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
    <span class='id identifier rubyid_csr'>csr</span> <span class='op'>=</span> <span class='id identifier rubyid_csr'>csr</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='op'>!</span><span class='id identifier rubyid_field_name'>field_name</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>&amp;&amp;</span> <span class='op'>!</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_field?'>field?</span><span class='lparen'>(</span><span class='id identifier rubyid_field_name'>field_name</span><span class='rparen'>)</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>%%LINK%csr_field;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_field_name'>field_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_field_name'>field_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>%%</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='op'>!</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>%%LINK%csr;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='embexpr_end'>}</span><span class='tstring_content'>%%</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_inst'>inst</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='period'>.</span><span class='id identifier rubyid_downcase'>downcase</span><span class='rparen'>)</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>%%LINK%inst;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>%%</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>elsif</span> <span class='id identifier rubyid_extension'>extension</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span>
      <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>%%LINK%ext;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>%%</span><span class='tstring_end'>&quot;</span></span>
    <span class='kw'>else</span>
      <span class='id identifier rubyid_match'>match</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_csr-instance_method">
  
    #<strong>implemented_csr</strong>(csr_name)  &#x21d2; <tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a specific csr, or nil if it doesn’t exist or isn’t implemented.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>csr_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>CSR name</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>a specific csr, or nil if it doesn’t exist or isn’t implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1496
1497
1498</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1496</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_csr'>implemented_csr</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_implemented_csr_hash'>implemented_csr_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_csr_hash-instance_method">
  
    #<strong>implemented_csr_hash</strong>  &#x21d2; <tt>Hash&lt;String, <span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Implemented csrs, indexed by CSR name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;String, <span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Implemented csrs, indexed by CSR name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1473
1474
1475
1476
1477
1478
1479
1480
1481</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1473</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_csr_hash'>implemented_csr_hash</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_csr_hash</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_csr_hash</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@implemented_csr_hash</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='id identifier rubyid_implemented_csrs'>implemented_csrs</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_csr'>csr</span><span class='op'>|</span>
    <span class='ivar'>@implemented_csr_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_csr'>csr</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_csr'>csr</span>
  <span class='kw'>end</span>
  <span class='ivar'>@implemented_csr_hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_csrs-instance_method">
  
    #<strong>implemented_csrs</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all implemented CSRs.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all implemented CSRs</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1449
1450
1451
1452
1453
1454
1455</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1449</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_csrs'>implemented_csrs</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_csrs</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_csrs</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@implemented_csrs</span> <span class='op'>=</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>csrs</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_select'>select</span><span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='comma'>,</span> <span class='id identifier rubyid__csr_data'>_csr_data</span><span class='op'>|</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implemented_csrs</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_csr_name'>csr_name</span><span class='rparen'>)</span><span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid__csr_name'>_csr_name</span><span class='comma'>,</span> <span class='id identifier rubyid_csr_data'>csr_data</span><span class='op'>|</span>
    <span class='const'><span class='object_link'><a href="Csr.html" title="Csr (class)">Csr</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="Csr.html#initialize-instance_method" title="Csr#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_csr_data'>csr_data</span><span class='comma'>,</span> <span class='ivar'>@sym_table</span><span class='comma'>,</span> <span class='kw'>self</span><span class='rparen'>)</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_extensions-instance_method">
  
    #<strong>implemented_extensions</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all extensions, with specific versions, that are implemented.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all extensions, with specific versions, that are implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1378
1379
1380
1381
1382
1383
1384
1385
1386
1387</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1378</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_extensions'>implemented_extensions</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_extensions</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_extensions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@implemented_extensions</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implemented_extensions</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='ivar'>@implemented_extensions</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="ExtensionVersion.html#initialize-instance_method" title="ExtensionVersion#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>version</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
<span class='int'>2</span>    <span class='kw'>end</span>

  <span class='ivar'>@implemented_extensions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="implemented_instructions-instance_method">
  
    #<strong>implemented_instructions</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all implemented instructions.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all implemented instructions</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1529
1530
1531
1532
1533
1534
1535
1536
1537
1538
1539</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1529</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implemented_instructions'>implemented_instructions</span>
  <span class='kw'>return</span> <span class='ivar'>@implemented_instructions</span> <span class='kw'>unless</span> <span class='ivar'>@implemented_instructions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='id identifier rubyid_opcode_data'>opcode_data</span> <span class='op'>=</span> <span class='const'>YAML</span><span class='period'>.</span><span class='id identifier rubyid_load_file'>load_file</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='gvar'>$root</span><span class='embexpr_end'>}</span><span class='tstring_content'>/ext/riscv-opcodes/instr_dict.yaml</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>

  <span class='ivar'>@implemented_instructions</span> <span class='op'>=</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implemented_instructions</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_inst_name'>inst_name</span><span class='op'>|</span>
    <span class='id identifier rubyid_instruction_hash'>instruction_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_inst_name'>inst_name</span><span class='rbracket'>]</span>
  <span class='kw'>end</span>

  <span class='ivar'>@implemented_instructions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="inst-instance_method">
  
    #<strong>inst</strong>(inst_name)  &#x21d2; <tt><span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span></tt><sup>?</sup> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns An instruction named ‘inst_name’, or nil if it doesn’t exist.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>inst_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Instruction name</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span></tt>, <tt>nil</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An instruction named ‘inst_name’, or nil if it doesn’t exist</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1543
1544
1545</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1543</span>

<span class='kw'>def</span> <span class='id identifier rubyid_inst'>inst</span><span class='lparen'>(</span><span class='id identifier rubyid_inst_name'>inst_name</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_instruction_hash'>instruction_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_inst_name'>inst_name</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="instruction_hash-instance_method">
  
    #<strong>instruction_hash</strong>  &#x21d2; <tt>Hash&lt;String, <span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns All instructions, indexed by name.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Hash&lt;String, <span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>All instructions, indexed by name</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1518
1519
1520
1521
1522
1523
1524
1525
1526</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1518</span>

<span class='kw'>def</span> <span class='id identifier rubyid_instruction_hash'>instruction_hash</span>
  <span class='kw'>return</span> <span class='ivar'>@instruction_hash</span> <span class='kw'>unless</span> <span class='ivar'>@instruction_hash</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@instruction_hash</span> <span class='op'>=</span> <span class='lbrace'>{</span><span class='rbrace'>}</span>
  <span class='id identifier rubyid_instructions'>instructions</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_inst'>inst</span><span class='op'>|</span>
    <span class='ivar'>@instruction_hash</span><span class='lbracket'>[</span><span class='id identifier rubyid_inst'>inst</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='rbracket'>]</span> <span class='op'>=</span> <span class='id identifier rubyid_inst'>inst</span>
  <span class='kw'>end</span>
  <span class='ivar'>@instruction_hash</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="instructions-instance_method">
  
    #<strong>instructions</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns List of all instructions, whether or not they are implemented.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of all instructions, whether or not they are implemented</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1507
1508
1509
1510
1511
1512
1513
1514
1515</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1507</span>

<span class='kw'>def</span> <span class='id identifier rubyid_instructions'>instructions</span>
  <span class='kw'>return</span> <span class='ivar'>@instructions</span> <span class='kw'>unless</span> <span class='ivar'>@instructions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@instructions</span> <span class='op'>=</span> <span class='ivar'>@arch_def</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>instructions</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid__inst_name'>_inst_name</span><span class='comma'>,</span> <span class='id identifier rubyid_inst_data'>inst_data</span><span class='op'>|</span>
    <span class='const'><span class='object_link'><a href="Instruction.html" title="Instruction (class)">Instruction</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="ArchDefObject.html#initialize-instance_method" title="ArchDefObject#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_inst_data'>inst_data</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='ivar'>@instructions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="multi_xlen?-instance_method">
  
    #<strong>multi_xlen?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>(i.e., that in some mode the effective xlen can be either 32 or 64, depending on CSR values)</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>true if this configuration can execute in multiple xlen environments</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1373
1374
1375</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1373</span>

<span class='kw'>def</span> <span class='id identifier rubyid_multi_xlen?'>multi_xlen?</span>
  <span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>SXLEN</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>UXLEN</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VSXLEN</span><span class='tstring_end'>&quot;</span></span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>VUXLEN</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_key'>key</span><span class='op'>|</span> <span class='ivar'>@config_params</span><span class='lbracket'>[</span><span class='id identifier rubyid_key'>key</span><span class='rbracket'>]</span> <span class='op'>==</span> <span class='int'>3264</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:48 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>