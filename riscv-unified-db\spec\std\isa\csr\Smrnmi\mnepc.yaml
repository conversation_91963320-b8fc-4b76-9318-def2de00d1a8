# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mnepc
long_name: Machine Exception Program Counter
address: 0x741
writable: true
priv_mode: M
length: MXLEN
description: |
  Written with the PC of an instruction on an exception or interrupt taken in M-mode.

  Also controls where the hart jumps on an exception return from M-mode.
definedBy: Sm
fields:
  PC:
    location_rv32: 31-0
    location_rv64: 63-0
    description: |
      When a NMI / double trap is taken into M-mode, `mnepc.PC` is written with the virtual address of the
      instruction that was interrupted or that encountered the exception.
      Otherwise, `mnepc.PC` is never written by the implementation, though it may be explicitly written
      by software.

      On an exception return from M-mode NMI / double trap (from the MNRET instruction),
      control transfers to the virtual address read out of `mnepc.PC`.

      [when,"ext?(:C)"]
      Because PCs are always halfword-aligned, bit 0 of `mnepc.PC` is always
      read-only 0.

      [when,"!ext?(:C)"]
      Because PCs are always word-aligned, bits 1:0 of `mnepc.PC` are always
      read-only 0.

      [when,"ext?(:C) && MUTABLE_MISA_C == true"]
      When `misa.C` is clear, bit 1 is masked to zero. Writes to bit 1 are still captured, and
      may be visible on the next read with `misa.C` is set.
    type: RW-RH
    sw_write(csr_value): |
      return csr_value.PC & ~64'b1;
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  if (implemented?(ExtensionName::C) && CSR[misa].C == 1'b1) {
    return CSR[mnepc].PC & ~64'b1;
  } else {
    return CSR[mnepc].PC & ~64'b11;
  }
