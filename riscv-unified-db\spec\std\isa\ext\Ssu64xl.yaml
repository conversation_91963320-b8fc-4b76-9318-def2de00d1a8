# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ssu64xl
long_name: 64-bit UXLEN
description: |
  `sstatus.UXL` must be capable of holding the value 2 (i.e., UXLEN=64 must be supported).

  [NOTE]
  This extension is defined by RVA22.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    param_constraints:
      UXLEN:
        schema:
          enum: [64, 3264]
