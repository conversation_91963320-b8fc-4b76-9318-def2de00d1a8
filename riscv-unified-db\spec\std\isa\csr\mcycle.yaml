# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mcycle
long_name: Machine Cycle Counter
definedBy: Zicntr
address: 0xB00
writable: true
description: |
  Counts the number of clock cycles executed by the processor core on which
  the hart is running.
  The counter has 64-bit precision on all RV32 and RV64 harts.

  The `mcycle` CSR may be shared between harts on the same core, in which case
  writes to `mcycle` will be visible to those harts. The platform should provide
  a mechanism to indicate which harts share an `mcycle` CSR.
priv_mode: M
length: 64
fields:
  COUNT:
    alias:
      - cycle.COUNT
    location: 63-0
    type: RW-RH
    sw_write(csr_value): |
      # since writes to this register may not be hart-local, it must be handled
      # as a special case
      if (xlen() == 32) {
        return sw_write_mcycle({read_mcycle()[63:31], csr_value.COUNT[31:0]});
      } else {
        return sw_write_mcycle(csr_value.COUNT);
      }
    description: |
      Cycle counter.

      <%- if ext?(:Zicntr) -%>
      Aliased as `cycle.CYCLE`.
      <%- end -%>

      Increments every cycle unless:

        * `mcountinhibit.CY` <%- if ext?(:Smcdeleg) -%>or its alias `scountinhibit.CY`<%- end -%> is set
        <%- if ext?(:Smcntrpmf) -%>
        * `mcyclecfg.MINH` is set and the current privilege level is M
        <%- if ext?(:S) -%>
        * `mcyclecfg.SINH` <%- if ext?(:Ssccfg) -%>or its alias `instretcfg.SINH`<%- end -%> is set and the current privilege level is (H)S
        <%- end -%>
        <%- if ext?(:U) -%>
        * `mcyclecfg.UINH` <%- if ext?(:Ssccfg) -%>or its alias `instretcfg.SINH`<%- end -%> is set and the current privilege level is U
        <%- end -%>
        <%- if ext?(:H) -%>
        * `mcyclecfg.VSINH` <%- if ext?(:Ssccfg) -%>or its alias `instretcfg.SINH`<%- end -%> is set and the current privilege level is VS
        * `mcyclecfg.VUINH` <%- if ext?(:Ssccfg) -%>or its alias `instretcfg.SINH`<%- end -%> is set and the current privilege level is VU
        <%- end -%>
        <%- end -%>
    reset_value: UNDEFINED_LEGAL
    affectedBy: [Zicntr, Smcntrpmf, Smcdeleg, Ssccfg]
sw_read(): |
  # since the counter may be shared among harts, reads must be handled
  # as a builtin function
  return read_mcycle();
