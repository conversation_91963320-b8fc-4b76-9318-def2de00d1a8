html {
  width: 100%;
  height: 100%;
}
body {
  font-family: "Lucida Sans", "Lucida Grande", Verdana, Arial, sans-serif;
  font-size: 13px;
  width: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  display: -webkit-flex;
  display: -ms-flexbox;
}

#nav {
  position: relative;
  width: 100%;
  height: 100%;
  border: 0;
  border-right: 1px dotted #eee;
  overflow: auto;
}
.nav_wrap {
  margin: 0;
  padding: 0;
  width: 20%;
  height: 100%;
  position: relative;
  display: flex;
  display: -webkit-flex;
  display: -ms-flexbox;
  flex-shrink: 0;
  -webkit-flex-shrink: 0;
  -ms-flex: 1 0;
}
#resizer {
  position: absolute;
  right: -5px;
  top: 0;
  width: 10px;
  height: 100%;
  cursor: col-resize;
  z-index: 9999;
}
#main {
  flex: 5 1;
  -webkit-flex: 5 1;
  -ms-flex: 5 1;
  outline: none;
  position: relative;
  background: #fff;
  padding: 1.2em;
  padding-top: 0.2em;
  box-sizing: border-box;
}

@media (max-width: 920px) {
  .nav_wrap { width: 100%; top: 0; right: 0; overflow: visible; position: absolute; }
  #resizer { display: none; }
  #nav {
    z-index: 9999;
    background: #fff;
    display: none;
    position: absolute;
    top: 40px;
    right: 12px;
    width: 500px;
    max-width: 80%;
    height: 80%;
    overflow-y: scroll;
    border: 1px solid #999;
    border-collapse: collapse;
    box-shadow: -7px 5px 25px #aaa;
    border-radius: 2px;
  }
}

@media (min-width: 920px) {
  body { height: 100%; overflow: hidden; }
  #main { height: 100%; overflow: auto; }
  #search { display: none; }
}

#main img { max-width: 100%; }
h1 { font-size: 25px; margin: 1em 0 0.5em; padding-top: 4px; border-top: 1px dotted #d5d5d5; }
h1.noborder { border-top: 0px; margin-top: 0; padding-top: 4px; }
h1.title { margin-bottom: 10px; }
h1.alphaindex { margin-top: 0; font-size: 22px; }
h2 {
  padding: 0;
  padding-bottom: 3px;
  border-bottom: 1px #aaa solid;
  font-size: 1.4em;
  margin: 1.8em 0 0.5em;
  position: relative;
}
h2 small { font-weight: normal; font-size: 0.7em; display: inline; position: absolute; right: 0; }
h2 small a {
  display: block;
  height: 20px;
  border: 1px solid #aaa;
  border-bottom: 0;
  border-top-left-radius: 5px;
  background: #f8f8f8;
  position: relative;
  padding: 2px 7px;
}
.clear { clear: both; }
.inline { display: inline; }
.inline p:first-child { display: inline; }
.docstring, .tags, #filecontents { font-size: 15px; line-height: 1.5145em; }
.docstring p > code, .docstring p > tt, .tags p > code, .tags p > tt {
  color: #c7254e; background: #f9f2f4; padding: 2px 4px; font-size: 1em;
  border-radius: 4px;
}
.docstring h1, .docstring h2, .docstring h3, .docstring h4 { padding: 0; border: 0; border-bottom: 1px dotted #bbb; }
.docstring h1 { font-size: 1.2em; }
.docstring h2 { font-size: 1.1em; }
.docstring h3, .docstring h4 { font-size: 1em; border-bottom: 0; padding-top: 10px; }
.summary_desc .object_link a, .docstring .object_link a {
  font-family: monospace; font-size: 1.05em;
  color: #05a; background: #EDF4FA; padding: 2px 4px; font-size: 1em;
  border-radius: 4px;
}
.rdoc-term { padding-right: 25px; font-weight: bold; }
.rdoc-list p { margin: 0; padding: 0; margin-bottom: 4px; }
.summary_desc pre.code .object_link a, .docstring pre.code .object_link a {
  padding: 0px; background: inherit; color: inherit; border-radius: inherit;
}

/* style for <table> */
#filecontents table, .docstring table { border-collapse: collapse; }
#filecontents table th, #filecontents table td,
.docstring table th, .docstring table td { border: 1px solid #ccc; padding: 8px; padding-right: 17px; }
#filecontents table tr:nth-child(odd),
.docstring table tr:nth-child(odd) { background: #eee; }
#filecontents table tr:nth-child(even),
.docstring table tr:nth-child(even) { background: #fff; }
#filecontents table th, .docstring table th { background: #fff; }

/* style for <ul> */
#filecontents li > p, .docstring li > p { margin: 0px; }
#filecontents ul, .docstring ul { padding-left: 20px; }
/* style for <dl> */
#filecontents dl, .docstring dl { border: 1px solid #ccc; }
#filecontents dt, .docstring dt { background: #ddd; font-weight: bold; padding: 3px 5px; }
#filecontents dd, .docstring dd { padding: 5px 0px; margin-left: 18px; }
#filecontents dd > p, .docstring dd > p { margin: 0px; }

.note {
  color: #222;
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 3px;
  display: block;
}
.docstring .note {
  border-left-color: #ccc;
  border-left-width: 5px;
}
.note.todo { background: #ffffc5; border-color: #ececaa; }
.note.returns_void { background: #efefef; }
.note.deprecated { background: #ffe5e5; border-color: #e9dada; }
.note.title.deprecated { background: #ffe5e5; border-color: #e9dada; }
.note.private { background: #ffffc5; border-color: #ececaa; }
.note.title { padding: 3px 6px; font-size: 0.9em; font-family: "Lucida Sans", "Lucida Grande", Verdana, Arial, sans-serif; display: inline; }
.summary_signature + .note.title { margin-left: 7px; }
h1 .note.title { font-size: 0.5em; font-weight: normal; padding: 3px 5px; position: relative; top: -3px; text-transform: capitalize; }
.note.title { background: #efefef; }
.note.title.constructor { color: #fff; background: #6a98d6; border-color: #6689d6; }
.note.title.writeonly { color: #fff; background: #45a638; border-color: #2da31d; }
.note.title.readonly { color: #fff; background: #6a98d6; border-color: #6689d6; }
.note.title.private { background: #d5d5d5; border-color: #c5c5c5; }
.note.title.not_defined_here { background: transparent; border: none; font-style: italic; }
.discussion .note { margin-top: 6px; }
.discussion .note:first-child { margin-top: 0; }

h3.inherited {
  font-style: italic;
  font-family: "Lucida Sans", "Lucida Grande", Verdana, Arial, sans-serif;
  font-weight: normal;
  padding: 0;
  margin: 0;
  margin-top: 12px;
  margin-bottom: 3px;
  font-size: 13px;
}
p.inherited {
  padding: 0;
  margin: 0;
  margin-left: 25px;
}

.box_info dl {
  margin: 0;
  border: 0;
  width: 100%;
  font-size: 1em;
  display: flex;
  display: -webkit-flex;
  display: -ms-flexbox;
}
.box_info dl dt {
  flex-shrink: 0;
  -webkit-flex-shrink: 1;
  -ms-flex-shrink: 1;
  width: 100px;
  text-align: right;
  font-weight: bold;
  border: 1px solid #aaa;
  border-width: 1px 0px 0px 1px;
  padding: 6px 0;
  padding-right: 10px;
}
.box_info dl dd {
  flex-grow: 1;
  -webkit-flex-grow: 1;
  -ms-flex: 1;
  max-width: 420px;
  padding: 6px 0;
  padding-right: 20px;
  border: 1px solid #aaa;
  border-width: 1px 1px 0 0;
  overflow: hidden;
  position: relative;
}
.box_info dl:last-child > * {
  border-bottom: 1px solid #aaa;
}
.box_info dl:nth-child(odd) > * { background: #eee; }
.box_info dl:nth-child(even) > * { background: #fff; }
.box_info dl > * { margin: 0; }

ul.toplevel { list-style: none; padding-left: 0; font-size: 1.1em; }
.index_inline_list { padding-left: 0; font-size: 1.1em; }

.index_inline_list li {
  list-style: none;
  display: inline-block;
  padding: 0 12px;
  line-height: 30px;
  margin-bottom: 5px;
}

dl.constants { margin-left: 10px; }
dl.constants dt { font-weight: bold; font-size: 1.1em; margin-bottom: 5px; }
dl.constants.compact dt { display: inline-block; font-weight: normal }
dl.constants dd { width: 75%; white-space: pre; font-family: monospace; margin-bottom: 18px; }
dl.constants .docstring .note:first-child { margin-top: 5px; }

.summary_desc {
  margin-left: 32px;
  display: block;
  font-family: sans-serif;
  font-size: 1.1em;
  margin-top: 8px;
  line-height: 1.5145em;
  margin-bottom: 0.8em;
}
.summary_desc tt { font-size: 0.9em; }
dl.constants .note { padding: 2px 6px; padding-right: 12px; margin-top: 6px; }
dl.constants .docstring { margin-left: 32px; font-size: 0.9em; font-weight: normal; }
dl.constants .tags { padding-left: 32px; font-size: 0.9em; line-height: 0.8em; }
dl.constants .discussion *:first-child { margin-top: 0; }
dl.constants .discussion *:last-child { margin-bottom: 0; }

.method_details { border-top: 1px dotted #ccc; margin-top: 25px; padding-top: 0; }
.method_details.first { border: 0; margin-top: 5px; }
.method_details.first h3.signature { margin-top: 1em; }
p.signature, h3.signature {
  font-size: 1.1em; font-weight: normal; font-family: Monaco, Consolas, Courier, monospace;
  padding: 6px 10px; margin-top: 1em;
  background: #E8F4FF; border: 1px solid #d8d8e5; border-radius: 5px;
}
p.signature tt,
h3.signature tt { font-family: Monaco, Consolas, Courier, monospace; }
p.signature .overload,
h3.signature .overload { display: block; }
p.signature .extras,
h3.signature .extras { font-weight: normal; font-family: sans-serif; color: #444; font-size: 1em; }
p.signature .not_defined_here,
h3.signature .not_defined_here,
p.signature .aliases,
h3.signature .aliases { display: block; font-weight: normal; font-size: 0.9em; font-family: sans-serif; margin-top: 0px; color: #555; }
p.signature .aliases .names,
h3.signature .aliases .names { font-family: Monaco, Consolas, Courier, monospace; font-weight: bold; color: #000; font-size: 1.2em; }

.tags .tag_title { font-size: 1.05em; margin-bottom: 0; font-weight: bold; }
.tags .tag_title tt { color: initial; padding: initial; background: initial; }
.tags ul { margin-top: 5px; padding-left: 30px; list-style: square; }
.tags ul li { margin-bottom: 3px; }
.tags ul .name { font-family: monospace; font-weight: bold; }
.tags ul .note { padding: 3px 6px; }
.tags { margin-bottom: 12px; }

.tags .examples .tag_title { margin-bottom: 10px; font-weight: bold; }
.tags .examples .inline p { padding: 0; margin: 0; font-weight: bold; font-size: 1em; }
.tags .examples .inline p:before { content: "▸"; font-size: 1em; margin-right: 5px; }

.tags .overload .overload_item { list-style: none; margin-bottom: 25px; }
.tags .overload .overload_item .signature {
  padding: 2px 8px;
  background: #F1F8FF; border: 1px solid #d8d8e5; border-radius: 3px;
}
.tags .overload .signature { margin-left: -15px; font-family: monospace; display: block; font-size: 1.1em; }
.tags .overload .docstring { margin-top: 15px; }

.defines { display: none; }

#method_missing_details .notice.this { position: relative; top: -8px; color: #888; padding: 0; margin: 0; }

.showSource { font-size: 0.9em; }
.showSource a, .showSource a:visited { text-decoration: none; color: #666; }

#content a, #content a:visited { text-decoration: none; color: #05a; }
#content a:hover { background: #ffffa5; }

ul.summary {
  list-style: none;
  font-family: monospace;
  font-size: 1em;
  line-height: 1.5em;
  padding-left: 0px;
}
ul.summary a, ul.summary a:visited {
  text-decoration: none; font-size: 1.1em;
}
ul.summary li { margin-bottom: 5px; }
.summary_signature { padding: 4px 8px; background: #f8f8f8; border: 1px solid #f0f0f0; border-radius: 5px; }
.summary_signature:hover { background: #CFEBFF; border-color: #A4CCDA; cursor: pointer; }
.summary_signature.deprecated { background: #ffe5e5; border-color: #e9dada; }
ul.summary.compact li { display: inline-block; margin: 0px 5px 0px 0px; line-height: 2.6em;}
ul.summary.compact .summary_signature { padding: 5px 7px; padding-right: 4px; }
#content .summary_signature:hover a,
#content .summary_signature:hover a:visited {
  background: transparent;
  color: #049;
}

p.inherited a { font-family: monospace; font-size: 0.9em; }
p.inherited { word-spacing: 5px; font-size: 1.2em; }

p.children { font-size: 1.2em; }
p.children a { font-size: 0.9em; }
p.children strong { font-size: 0.8em; }
p.children strong.modules { padding-left: 5px; }

ul.fullTree { display: none; padding-left: 0; list-style: none; margin-left: 0; margin-bottom: 10px; }
ul.fullTree ul { margin-left: 0; padding-left: 0; list-style: none; }
ul.fullTree li { text-align: center; padding-top: 18px; padding-bottom: 12px; background: url(data:image/gif;base64,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) no-repeat top center; }
ul.fullTree li:first-child { padding-top: 0; background: transparent; }
ul.fullTree li:last-child { padding-bottom: 0; }
.showAll ul.fullTree { display: block; }
.showAll .inheritName { display: none; }

#search { position: absolute; right: 12px; top: 0px; z-index: 9000; }
#search a {
  display: block; float: left;
  padding: 4px 8px; text-decoration: none; color: #05a; fill: #05a;
  border: 1px solid #d8d8e5;
  border-bottom-left-radius: 3px; border-bottom-right-radius: 3px;
  background: #F1F8FF;
  box-shadow: -1px 1px 3px #ddd;
}
#search a:hover { background: #f5faff; color: #06b; fill: #06b; }
#search a.active {
  background: #568; padding-bottom: 20px; color: #fff; fill: #fff;
  border: 1px solid #457;
  border-top-left-radius: 5px; border-top-right-radius: 5px;
}
#search a.inactive { color: #999; fill: #999; }
.inheritanceTree, .toggleDefines {
  float: right;
  border-left: 1px solid #aaa;
  position: absolute; top: 0; right: 0;
  height: 100%;
  background: #f6f6f6;
  padding: 5px;
  min-width: 55px;
  text-align: center;
}

#menu { font-size: 1.3em; color: #bbb; }
#menu .title, #menu a { font-size: 0.7em; }
#menu .title a { font-size: 1em; }
#menu .title { color: #555; }
#menu a, #menu a:visited { color: #333; text-decoration: none; border-bottom: 1px dotted #bbd; }
#menu a:hover { color: #05a; }

#footer { margin-top: 15px; border-top: 1px solid #ccc; text-align: center; padding: 7px 0; color: #999; }
#footer a, #footer a:visited { color: #444; text-decoration: none; border-bottom: 1px dotted #bbd; }
#footer a:hover { color: #05a; }

#listing ul.alpha { font-size: 1.1em; }
#listing ul.alpha { margin: 0; padding: 0; padding-bottom: 10px; list-style: none; }
#listing ul.alpha li.letter { font-size: 1.4em; padding-bottom: 10px; }
#listing ul.alpha ul { margin: 0; padding-left: 15px; }
#listing ul small { color: #666; font-size: 0.7em; }

li.r1 { background: #f0f0f0; }
li.r2 { background: #fafafa; }

#content ul.summary li.deprecated .summary_signature a,
#content ul.summary li.deprecated .summary_signature a:visited { text-decoration: line-through; font-style: italic; }

#toc {
  position: relative;
  float: right;
  overflow-x: auto;
  right: -3px;
  margin-left: 20px;
  margin-bottom: 20px;
  padding: 20px; padding-right: 30px;
  max-width: 300px;
  z-index: 5000;
  background: #fefefe;
  border: 1px solid #ddd;
  box-shadow: -2px 2px 6px #bbb;
}
#toc .title { margin: 0; }
#toc ol { padding-left: 1.8em; }
#toc li { font-size: 1.1em; line-height: 1.7em; }
#toc > ol > li { font-size: 1.1em; font-weight: bold; }
#toc ol > li > ol { font-size: 0.9em; }
#toc ol ol > li > ol { padding-left: 2.3em; }
#toc ol + li { margin-top: 0.3em; }
#toc.hidden { padding: 10px; background: #fefefe; box-shadow: none; }
#toc.hidden:hover { background: #fafafa; }
#filecontents h1 + #toc.nofloat { margin-top: 0; }
@media (max-width: 560px) {
  #toc {
    margin-left: 0;
    margin-top: 16px;
    float: none;
    max-width: none;
  }
}

/* syntax highlighting */
.source_code { display: none; padding: 3px 8px; border-left: 8px solid #ddd; margin-top: 5px; }
#filecontents pre.code, .docstring pre.code, .source_code pre { font-family: monospace; }
#filecontents pre.code, .docstring pre.code { display: block; }
.source_code .lines { padding-right: 12px; color: #555; text-align: right; }
#filecontents pre.code, .docstring pre.code,
.tags pre.example {
  padding: 9px 14px;
  margin-top: 4px;
  border: 1px solid #e1e1e8;
  background: #f7f7f9;
  border-radius: 4px;
  font-size: 1em;
  overflow-x: auto;
  line-height: 1.2em;
}
pre.code { color: #000; tab-size: 2; }
pre.code .info.file { color: #555; }
pre.code .val { color: #036A07; }
pre.code .tstring_content,
pre.code .heredoc_beg, pre.code .heredoc_end,
pre.code .qwords_beg, pre.code .qwords_end, pre.code .qwords_sep,
pre.code .words_beg, pre.code .words_end, pre.code .words_sep,
pre.code .qsymbols_beg, pre.code .qsymbols_end, pre.code .qsymbols_sep,
pre.code .symbols_beg, pre.code .symbols_end, pre.code .symbols_sep,
pre.code .tstring, pre.code .dstring { color: #036A07; }
pre.code .fid, pre.code .rubyid_new, pre.code .rubyid_to_s,
pre.code .rubyid_to_sym, pre.code .rubyid_to_f,
pre.code .dot + pre.code .id,
pre.code .rubyid_to_i pre.code .rubyid_each { color: #0085FF; }
pre.code .comment { color: #0066FF; }
pre.code .const, pre.code .constant { color: #585CF6; }
pre.code .label,
pre.code .symbol { color: #C5060B; }
pre.code .kw,
pre.code .rubyid_require,
pre.code .rubyid_extend,
pre.code .rubyid_include { color: #0000FF; }
pre.code .ivar { color: #318495; }
pre.code .gvar,
pre.code .rubyid_backref,
pre.code .rubyid_nth_ref { color: #6D79DE; }
pre.code .regexp, .dregexp { color: #036A07; }
pre.code a { border-bottom: 1px dotted #bbf; }
/* inline code */
*:not(pre) > code {
	padding: 1px 3px 1px 3px;
	border: 1px solid #E1E1E8;
	background: #F7F7F9;
	border-radius: 4px;
}

/* Color fix for links */
#content .summary_desc pre.code .id > .object_link a, /* identifier */
#content .docstring pre.code .id > .object_link a { color: #0085FF; }
#content .summary_desc pre.code .const > .object_link a, /* constant */
#content .docstring pre.code .const > .object_link a { color: #585CF6; }
