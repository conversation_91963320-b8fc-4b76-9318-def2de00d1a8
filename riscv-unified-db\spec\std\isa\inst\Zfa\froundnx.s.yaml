# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: froundnx.s
long_name: Floating-point Round Single-precision to Integer with Inexact
description: |
  No description available.
definedBy: Zfa
assembly: fd, fs1, rm
encoding:
  match: 010000000101-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: true
operation(): |

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let rs1_val_S = F_S(rs1);

    match (select_instr_or_fcsr_rm(rm)) {
      None() => { handle_illegal(); RETIRE_FAIL },
      Some(rm') => {
        let rm_3b =  encdec_rounding_mode(rm');
        let (fflags, rd_val_S) = riscv_f32roundToInt(rm_3b, rs1_val_S, true);

        accrue_fflags(fflags);
        F_S(rd) = rd_val_S;
        RETIRE_SUCCESS
      }
    }
  }

# SPDX-SnippetEnd
