# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: fmvh.x.d
long_name: No synopsis available
description: |
  No description available.
definedBy:
  allOf: [D, Zfa]
assembly: xd, fs1
encoding:
  match: 111000100001-----000-----1010011
  variables:
    - name: fs1
      location: 19-15
    - name: xd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
base: 32
operation(): |
