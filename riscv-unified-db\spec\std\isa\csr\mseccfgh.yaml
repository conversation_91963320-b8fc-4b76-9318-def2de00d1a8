# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mseccfgh
long_name: Most significant 32 bits of Machine Security Configuration
base: 32
address: 0x757
writable: true
priv_mode: M
length: 32
description: Machine Security Configuration
definedBy:
  name: Sm
  version: ">= 1.12"
fields: {}
