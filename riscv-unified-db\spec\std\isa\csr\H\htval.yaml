# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: htval
address: 0x643
writable: true
long_name: Hypervisor Trap Value Register
description: |
  When a trap is taken into HS-mode, htval is written with additional exception-specific information, alongside stval, to assist software in handling the trap.

  When a guest-page-fault trap is taken into HS-mode, htval is written with either zero or the guest physical address that faulted, shifted right by 2 bits. For other traps, htval is set to zero, but a future standard or extension may redefine htval's setting for other traps.

  A guest-page fault may arise due to an implicit memory access during first-stage (VS-stage) address translation, in which case a guest physical address written to htval is that of the implicit memory access that faulted-for example, the address of a VS-level page table entry that could not be read. (The guest physical address corresponding to the original virtual address is unknown when VS-stage translation fails to complete.) Additional information is provided in CSR htinst to disambiguate such situations.

  Otherwise, for misaligned loads and stores that cause guest-page faults, a nonzero guest physical address in htval corresponds to the faulting portion of the access as indicated by the virtual address in stval. For instruction guest-page faults on systems with variable-length instructions, a nonzero htval corresponds to the faulting portion of the instruction as indicated by the virtual address in stval.

  htval is a WARL register that must be able to hold zero and may be capable of holding only an arbitrary subset of other 2-bit-shifted guest physical addresses, if any.
priv_mode: M
length: MXLEN
definedBy: H
fields:
  VALUE:
    location_rv64: 63-0
    location_rv32: 31-0
    type(): |
      if (REPORT_GPA_IN_TVAL_ON_LOAD_GUEST_PAGE_FAULT
          || REPORT_GPA_IN_TVAL_ON_STORE_AMO_GUEST_PAGE_FAULT
          || REPORT_GPA_IN_TVAL_ON_INSTRUCTION_GUEST_PAGE_FAULT
          || REPORT_GPA_IN_TVAL_ON_INTERMEDIATE_GUEST_PAGE_FAULT) {
        return CsrFieldType::RWH;
      } else {
        return CsrFieldType::RO;
      }
    description: |
      Exception-specific information for a trap into M-mode.
    reset_value: UNDEFINED_LEGAL
