<%= portfolio_design.include_erb("beginning.adoc.erb") %>

// Book title. Need "":doctype: book" immediately after book title (no blank lines allowed).
= <%= proc_cert_model.name %> Processor CTP (Certification Test Plan)
:doctype: book

[preface]
= CTP Book Structure

A CTP book is composed of 3 book parts as follows:

* <<udb:doc,Part 1>> is the core CTP documentation including an appendix each for extension, instruction, and CSR
  that could be present in a certificate.
* <<manual:unpriv,Part 2>> is the RISC-V Unprivileged ISA manual, volume I (uses release specified by the certificate)
* <<manual:priv,Part 3>> is the RISC-V Privileged ISA manual, volume II (uses release specified by the certificate)

// Part 1 title
[#udb:doc]
= <%= proc_cert_model.name %> Processor Certification Test Plan: Part 1

[Preface]
== CTP Revision History

<%= proc_cert_design.include_erb("rev_history.adoc.erb") %>

[Preface]
== Typographic Conventions

<%= proc_cert_design.include_erb("typographic.adoc.erb") %>

[Preface]
== Glossary

<%= proc_cert_design.include_erb("glossary.adoc.erb") %>

== Introduction

<%= proc_cert_model.introduction %>

<%= proc_cert_class.introduction %>

=== What's a CTP?

Certification Test Plans (CTPs) list certification normative rules and how they will be tested via
certification test procedures (step by step descriptions of tests).
CTPs are developed by the RVI CSC (Certification Steering Committee) organization in collaboration
with the RVI TSC (Technical Steering Committee) organization who creates RISC-V standards including
the RISC-V Unprivileged and Privileged ISA manuals.

Each certificate has a corresponding CRD and CTP:

* The CRD defines the certification requirements an implementation must meet to obtain certification.
* The CTP defines the certification normative rules and certification test procedures followed by the certification
  tests that an implementation must pass to obtain certification.

The certification normative rules reference text in any or all of the following:

* RISC-V ISA manuals in parts 2 and 3
* Community-generated documentation located in part 1 (https://github.com/riscv-software-src/riscv-unified-db)
* IDL (ISA Description Language) executable psuedo-code descriptions located in part 1

The RISC-V ISA manuals are the preferred reference for certification normative rules since these manuals
represent the ratified RISC-V standards.
However, if the information in the ISA manuals isn't sufficiently clear or complete for certification purposes
the content in Part 1 is used.

=== Naming Scheme

==== CTP Naming

<%= proc_cert_design.include_erb("rvcp_naming_scheme.adoc.erb") %>

The specific rules for updating the version number for a CTP are as follows:

* The <major> release number is updated for changes that *could* cause a previously certified
   implementation to now fail. An example is increasing coverage.
* The <minor> release number is updated when the CRD referenced by a CTP adds support for new optional behaviors.
* The <patch> release number is updated for documentation fixes/improvements.
  These changes *cannot* cause a previously certified implementation to no longer pass certification.

==== Processor Naming Scheme

<%= proc_cert_design.include_erb("proc_naming_scheme.adoc.erb") %>

<%= proc_cert_design.include_erb("related_specs.adoc.erb") %>
<%= proc_cert_design.include_erb("priv_modes.adoc.erb") %>

== Normative Rule View

This section contains a view of the normative rule information organized by kind
(i.e., extension, instruction, or CSR).
This document is generated by a database backend so all views of the information are consistent.

=== Extension Normative Rules

<% proc_cert_model.in_scope_extensions.each do |ext| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => ext, "org" => "sep"}) %>
<% end -%>

=== Instruction Normative Rules

<% proc_cert_model.in_scope_instructions(portfolio_design).each do |inst| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => inst, "org" => "sep"}) %>
<% end -%>

=== CSR Normative Rules

<% proc_cert_model.in_scope_csrs(portfolio_design).each do |csr| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => csr, "org" => "sep" }) %>
<% csr.possible_fields.each do |field| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => field, "org" => "sep" }) %>
<% end -%>
<% end -%>

== Test Procedure View

This section contains just view of the test procedure information organized by kind
(i.e., extension, instruction, or CSR).
This document is generated by a database backend so all views of the information are consistent.

=== Extension Test Procedures

<% proc_cert_model.in_scope_extensions.each do |ext| -%>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => ext, "org" => "sep" }) %>
<% end -%>

=== Instruction Test Procedures

<% proc_cert_model.in_scope_instructions(portfolio_design).each do |inst| -%>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => inst, "org" => "sep" }) %>
<% end -%>

=== CSR Test Procedures

<% proc_cert_model.in_scope_csrs(portfolio_design).each do |csr| -%>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => csr, "org" => "sep" }) %>
<% csr.possible_fields.each do |field| -%>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => field, "org" => "sep" }) %>
<% end -%>
<% end -%>

== Combined View

This section contains a combined view of the normative rule and test procedure information organized
by kind (i.e., extension, instruction, or CSR).
This document is generated by a database backend so all views of the information are consistent.

=== Extension Normative Rules & Test Procedures

<% proc_cert_model.in_scope_extensions.each do |ext| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => ext, "org" => "combo"}) %>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => ext, "org" => "combo"}) %>
<% end -%>

=== Instruction Normative Rules & Test Procedures

<% proc_cert_model.in_scope_instructions(portfolio_design).each do |inst| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => inst, "org" => "combo"}) %>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => inst, "org" => "combo"}) %>
<% end -%>

=== CSR Normative Rules & Test Procedures

<% proc_cert_model.in_scope_csrs(portfolio_design).each do |csr| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => csr, "org" => "combo"}) %>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => csr, "org" => "combo"}) %>
<% csr.possible_fields.each do |field| -%>
<%= portfolio_design.include_erb("normative_rules.adoc.erb", { "db_obj" => field, "use_description_list" => true, "org" => "combo"}) %>
<%= portfolio_design.include_erb("test_procedures.adoc.erb", { "db_obj" => field, "use_description_list" => true, "org" => "combo"}) %>
<% end -%>
<% end -%>

// Appendices
<%= portfolio_design.include_erb("ext_appendix.adoc.erb", { "gen_ctp_content" => true }) %>
<%= portfolio_design.include_erb("inst_appendix.adoc.erb", { "gen_ctp_content" => true }) %>
<%= portfolio_design.include_erb("csr_appendix.adoc.erb", { "gen_ctp_content" => true }) %>
<%= portfolio_design.include_erb("idl_func_appendix.adoc.erb") %>

<<<
<% $logger.info "Including riscv-unprivileged.adoc" -%>
// Reset chapter numbering
:!chapter-number:
include::ext/riscv-isa-manual/src/riscv-unprivileged.adoc[]

<<<
<% $logger.info "Including riscv-privileged.adoc" -%>
// Reset chapter numbering
:!chapter-number:
include::ext/riscv-isa-manual/src/riscv-privileged.adoc[]
