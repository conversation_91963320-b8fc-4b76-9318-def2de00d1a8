# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: fcvt.d.l
long_name: Floating-point Convert Long to Double-precision
description:
  - id: inst-fcvt.d.l-behaviour
    normative: false
    text: |
      `fcvt.d.l` converts a 64-bit signed integer, in integer register `xs1` into a double-precision
      floating-point number in floating-point register `fd`.
definedBy: D
assembly: fd, xs1, rm
encoding:
  match: 110100100010-------------1010011
  variables:
    - name: xs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
base: 64
operation(): |
