# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zve32f
long_name: Vector Extension for Minimal Single-Precision Embedded Floating-Point
description: |
  TBD - See GitHub issue 616
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: F
