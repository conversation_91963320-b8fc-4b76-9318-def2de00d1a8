# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqcilsm
type: unprivileged
long_name: Qualcomm load/store multiple
versions:
- version: "0.1.0"
  state: development
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Add information about instruction formats of each instruction
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL><PERSON>.com
  changes:
    - Fix description of qc.swmi, qc.lwmi and qc.setwmi instructions
- version: "0.4.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix encoding of qc.swmi
- version: "0.5.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix IDL code and description of qc.setwm instruction to state that number of words written 0..31.
- version: "0.6.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: Albert Yosher
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: Derek Hower
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix encoding of qc.swm and qc.swmi instructions to state that rs3 cannot be x0
    - Fix description and IDL code of qc.swm and qc.lwm instructions to state that length is in rs2[4:0]
description: |
  The Xqcilsm extension includes six instructions that transfer multiple values
  between registers and memory.

doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
