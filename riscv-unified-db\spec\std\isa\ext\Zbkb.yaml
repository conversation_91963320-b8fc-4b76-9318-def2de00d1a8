# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zbkb
long_name: Bit-manipulation for Cryptography
description: This extension contains instructions essential for implementing common operations in cryptographic workloads.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
