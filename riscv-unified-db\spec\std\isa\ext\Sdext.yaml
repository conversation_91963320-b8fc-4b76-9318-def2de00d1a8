# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Sdext
long_name: Debug
description: |
  Hart-visible portion of the debug spec.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
