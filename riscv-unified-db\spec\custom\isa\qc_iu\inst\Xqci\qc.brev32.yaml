# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.brev32
long_name: Reverse bit order
description: |
  Reverses the bit order of `rs1` and writes the result to `rd`.
  Instruction encoded in I instruction format
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 000011000000-----011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg orig_val = X[rs1];
  XReg tmp;

  for (U32 i=0; i<xlen(); i++) {
    tmp[xlen() - 1 - i] = orig_val[i];
  }

  X[rd] = tmp;
