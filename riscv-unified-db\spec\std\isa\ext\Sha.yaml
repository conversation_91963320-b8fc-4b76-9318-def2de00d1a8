# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Sha
type: privileged
long_name: The augmented hypervisor extension
description: |
  *Sha* comprises the following extensions:

  ** *H* The hypervisor extension.

  ** *Ssstateen* Supervisor-mode view of the state-enable extension.  The
     supervisor-mode (`sstateen0-3`) and hypervisor-mode (`hstateen0-3`)
     state-enable registers must be provided.

  ** *Shcounterenw* For any `hpmcounter` that is not read-only zero, the corresponding bit in `hcounteren` must be writable.

  ** *Shvstvala* `vstval` must be written in all cases described above for `stval`.

  ** *Shtvala* `htval` must be written with the faulting guest physical
     address in all circumstances permitted by the ISA.

  ** *Shvstvecd* `vstvec.MODE` must be capable of holding the value 0 (Direct).
    When `vstvec.MODE`=Direct, `vstvec.BASE` must be capable of holding
    any valid four-byte-aligned address.

  ** *Shvsatpa* All translation modes supported in `satp` must be supported in `vsatp`.

  ** *Shgatpa* For each supported virtual memory scheme SvNN supported in
    `satp`, the corresponding hgatp SvNNx4 mode must be supported.  The
    `hgatp` mode Bare must also be supported.

  [NOTE]
  Sha is a new profile-defined extension that captures the
  full set of features that are mandated to be supported along with the
  H extension.  There is no change to the features added by including
  the hypervisor extension in a profile--the new name is solely to
  simplify the text of the profiles.  The definition has been added to
  the RVA22 profile text, where the hypervisor extension was first
  added, and will be added to the hypervisor section of the combined ISA
  manual.

versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    implies:
      - name: H
        version: "1.0.0"
      - name: Ssstateen
        version: "1.0.0"
      - name: Shcounterenw
        version: "1.0.0"
      - name: Shvstvala
        version: "1.0.0"
      - name: Shtvala
        version: "1.0.0"
      - name: Shvstvecd
        version: "1.0.0"
      - name: Shvsatpa
        version: "1.0.0"
      - name: Shgatpa
        version: "1.0.0"
