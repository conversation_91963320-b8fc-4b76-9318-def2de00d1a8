name: Publish container
on:
  push:
    branches:
      - main
  workflow_dispatch:
jobs:
  publish-singularity-container:
    runs-on: ubuntu-latest
    env:
      SINGULARITY: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4

      - name: Setup apptainer
        uses: eWaterCycle/setup-apptainer@v2.0.0
        with:
          apptainer-version: 1.4.0

      - name: Get container tag
        run: |
          TAG=$(cat bin/.container-tag)
          echo "TAG=$TAG" >> $GITHUB_ENV

      - name: Get container from cache
        id: cache-sif
        uses: actions/cache@v4
        with:
          path: .singularity/image.sif
          key: ${{ hashFiles('container.def', 'bin/.container-tag') }}

      - name: Get gems and node files from cache
        id: cache-bundle-npm
        uses: actions/cache@v4
        with:
          path: |
            .home/.gems
            node_modules
          key: ${{ hashFiles('Gemfile.lock') }}-${{ hashFiles('package-lock.json') }}

      - if: ${{ steps.cache-sif.outputs.cache-hit != 'true' }}
        name: Build container
        run: ./bin/build_container

      - name: Upload singularity container
        run: |
          apptainer registry login -u ${{ secrets.DOCKERHUB_USERNAME }} -p ${{ secrets.DOCKERHUB_TOKEN }} oras://docker.io
          apptainer push .singularity/image.sif oras://docker.io/riscvintl/spec-generator:${{ env.TAG }}

  publish-docker-container:
    runs-on: ubuntu-latest
    env:
      DOCKER: 1
    steps:
      - name: Clone Github Repo Action
        uses: actions/checkout@v4

      - name: Get container tag
        run: |
          TAG=$(cat bin/.container-tag)
          echo "TAG=$TAG" >> $GITHUB_ENV

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          file: ./.devcontainer/Dockerfile
          platforms: linux/amd64,linux/arm64
          tags: riscvintl/udb:${{ env.TAG }}
