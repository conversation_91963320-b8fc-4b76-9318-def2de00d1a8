# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: marchid
long_name: Machine Architecture ID
address: 0xf12
writable: false
priv_mode: M
length: MXLEN
description: |
  The `marchid` CSR is an MXLEN-bit read-only register encoding the base
  microarchitecture of the hart. This register must be readable in any
  implementation, but a value of 0 can be returned to indicate the field
  is not implemented. The combination of `mvendorid` and `marchid` should
  uniquely identify the type of hart microarchitecture that is
  implemented.

  Open-source project architecture IDs are allocated globally by RISC-V
  International, and have non-zero architecture IDs with a zero
  most-significant-bit (MSB). Commercial architecture IDs are allocated by
  each commercial vendor independently, but must have the MSB set and
  cannot contain zero in the remaining MXLEN-1 bits.

  [NOTE]
  ====
  The intent is for the architecture ID to represent the microarchitecture
  associated with the repo around which development occurs rather than a
  particular organization. Commercial fabrications of open-source designs
  should (and might be required by the license to) retain the original
  architecture ID. This will aid in reducing fragmentation and tool
  support costs, as well as provide attribution. Open-source architecture
  IDs are administered by RISC-V International and should only be
  allocated to released, functioning open-source projects. Commercial
  architecture IDs can be managed independently by any registered vendor
  but are required to have IDs disjoint from the open-source architecture
  IDs (MSB set) to prevent collisions if a vendor wishes to use both
  closed-source and open-source microarchitectures.

  The convention adopted within the following Implementation field can be
  used to segregate branches of the same architecture design, including by
  organization. The `misa` register also helps distinguish different
  variants of a design.
  ====

definedBy: Sm
fields:
  Architecture:
    location_rv32: 31-0
    location_rv64: 63-0
    type: RO
    description: Vendor-specific microarchitecture ID.
    reset_value(): return ARCH_ID;
