// THIS FILE IS AUTOGENERATED

#pragma once

#include <cstdint>
#include "udb/bits.hpp"
#include "udb/bitfield.hpp"

namespace udb {

  <%- cfg_arch.global_ast.bitfields.each do |bitfield| -%>
  class <%= bitfield.name %> : public Bitfield<<%= bitfield.size(cfg_arch.symtab) %>> {
    public:

    // constructors
    <%= bitfield.name %>()
      : <%= bitfield.element_names.map { |name| "#{name}(*this)" }.join(",\n#{' '*8}") %>
    {}
    <%= bitfield.name %>(const Bits<<%= bitfield.size(cfg_arch.symtab) %>>& value)
      : Bitfield<<%= bitfield.size(cfg_arch.symtab) %>>(value),
        <%= bitfield.element_names.map { |name| "#{name}(*this)" }.join(",\n#{' '*8}") %>
    {}

    <%= bitfield.name %>& operator=(const <%= bitfield.name %>& other)
    {
      <%- bitfield.element_names.each do |element_name| -%>
      <%= element_name %> = other.<%= element_name %>;
      <%- end -%>

      return *this;
    }

    <%- bitfield.element_names.size.times do |idx| -%>
    BitfieldMember<<%= bitfield.size(cfg_arch.symtab) %>, <%= bitfield.element_ranges(cfg_arch.symtab)[idx].begin %>, <%= bitfield.element_ranges(cfg_arch.symtab)[idx].size %>> <%= bitfield.element_names[idx] %>;
    <%- end -%>
  };
  <%- end -%>
}
