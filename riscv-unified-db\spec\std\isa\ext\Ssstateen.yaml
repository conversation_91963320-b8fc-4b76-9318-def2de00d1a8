# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ssstateen
long_name: Supervisor-mode view of the state-enable extension
description: |
  Supervisor-mode view of the state-enable extension.  The
  supervisor-mode (`sstateen0-3`) and hypervisor-mode (`hstateen0-3`)
  state-enable registers must be provided.

  NOTE: The Smstateen extension specification is an M-mode extension as
  it includes M-mode features, but the supervisor-mode visible
  components of the extension are named as the Ssstateen extension.  Only
  Ssstateen is mandated in the RVA22S64 profile when the hypervisor
  extension is implemented.  These registers are not mandated or
  supported options without the hypervisor extension, as there are no
  RVA22S64 supported options with relevant state to control in the
  absence of the hypervisor extension.

  [NOTE]
  This extension was ratified with the RVA22 profiles.
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    # TODO: add param_constraints
