# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ssstrict
long_name: Unimplemented reserved encodings trap and no no-conforming extensions
type: privileged
description: |
  No non-conforming extensions are present.  Attempts to
  execute unimplemented opcodes or access unimplemented CSRs in the
  standard or reserved encoding spaces raises an illegal instruction
  exception that results in a contained trap to the supervisor-mode
  trap handler.

  [NOTE]
  Ssstrict does not prescribe behavior for the custom encoding
  spaces or CSRs.

  [NOTE]
  Ssstrict definition applies to the execution environment
  claiming to be RVA23/RVB23-compatible.
  If the hypervisor extension is present, that execution environment will take a contained trap to
  supervisor-mode (however that trap is implemented, including, but not
  limited to, emulation/delegation in the outer execution
  environment). Ssstrict (and all the other RVA23/RVB23 mandates and options)
  do not apply to any guest VMs run by a hypervisor. An RVA23/RVB23 hypervisor
  can provide guest VMs that are also RVA23/RVB23-compatible but with an
  expanded set of emulated standard instructions. An RVA23/RVB23 hypervisor
  can also choose to implement guest VMs that are not RVA23/RVB23 compatible
  (e.g., lacking H, or only RVA20).

  [NOTE]
  Ssstrict is a new RVA23/RVB23 profile-defined extension that restricts the
  behavior of reserved encoding spaces.  The extension will be added to
  the supervisor chapter of the privileged architecture.
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
