rom: Memory.MappedMemory @ {
        sysbus 0x80000000;
        sysbus 0x0
    }
    size: 0x2000000

cpu: UdbCpu @ sysbus
    cpuType: "rv64imacfd_zicsr_zifencei"
    modelType: "_"
    configFile: "/local/mnt/workspace/dhower/riscv-unified-db/worktrees/cpp_hart/cfgs/mc100-32-full-example.yaml"
    sharedLibrary: "/local/mnt/workspace/dhower/riscv-unified-db/worktrees/cpp_hart/gen/cpp_hart_gen/__Debug/build/libhart_renode.so"
    bitness: CpuBitness.Bits64


uart: UART.SiFive_UART @ sysbus 0x38000000
    -> plic@33

clint: IRQControllers.CoreLevelInterruptor  @ sysbus 0x02000000
    [0,1] -> cpu@[3,7]
    frequency: 62000000
    numberOfTargets: 2

plic: IRQControllers.PlatformLevelInterruptController @ sysbus 0x0C000000
    [0,1] -> cpu@[11,9]
    numberOfSources: 65
    numberOfContexts: 4
