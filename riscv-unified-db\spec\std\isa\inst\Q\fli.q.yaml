# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fli.q
long_name: No synopsis available
description: |
  No description available.
definedBy:
  allOf: [Q, Zfa]
assembly: fd, xs1
encoding:
  match: 111101100001-----000-----1010011
  variables:
    - name: xs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
