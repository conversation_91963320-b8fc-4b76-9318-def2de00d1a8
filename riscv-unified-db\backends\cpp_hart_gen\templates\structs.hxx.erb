// THIS FILE IS AUTOGENERATED

#pragma once

#include <type_traits>
#include <cstdint>
#include "udb/bits.hpp"
#include "udb/bitfield.hpp"

namespace udb {

#define __UDB_STRUCT(name) <%= name_of(:cfg, cfg_arch) %>_ ## name ## _Struct

  <%- cfg_arch.global_ast.structs.each do |struct| -%>
    struct <%= name_of(:struct, cfg_arch, struct.name) %> {
      <%- struct.member_types.each_index do |idx| -%>
      <%= struct.member_types[idx].gen_cpp(cfg_arch.symtab, 0) %> <%= struct.member_names[idx] %>;
      <%- end -%>

      <%= name_of(:struct, cfg_arch, struct.name) %>() = default;
      ~<%= name_of(:struct, cfg_arch, struct.name) %>() = default;

      <%= name_of(:struct, cfg_arch, struct.name) %>& operator=(const <%= name_of(:struct, cfg_arch, struct.name) %>& other)
      {
        if (this == &other) { return *this; }

        <%- struct.member_types.each_index do |idx| -%>
        <%= struct.member_names[idx] %> = other.<%= struct.member_names[idx] %>;
        <%- end -%>

        return *this;
      }
    };
  <%- end -%>

#undef __UDB_STRUCT

}
