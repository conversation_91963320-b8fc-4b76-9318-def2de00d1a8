# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicip1
long_name: IRQ Pending 1
address: 0x7f1
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - Xqci
    - Xqciint
description: |
  Pending bits for IRQs 32-63
fields:
  IRQ32:
    type: RW
    reset_value: 0
    location: 0
    description: IRQ32 pending
  IRQ33:
    type: RW
    reset_value: 0
    location: 1
    description: IRQ33 pending
  IRQ34:
    type: RW
    reset_value: 0
    location: 2
    description: IRQ34 pending
  IRQ35:
    type: RW
    reset_value: 0
    location: 3
    description: IRQ35 pending
  IRQ36:
    type: RW
    reset_value: 0
    location: 4
    description: IRQ36 pending
  IRQ37:
    type: RW
    reset_value: 0
    location: 5
    description: IRQ37 pending
  IRQ38:
    type: RW
    reset_value: 0
    location: 6
    description: IRQ38 pending
  IRQ39:
    type: RW
    reset_value: 0
    location: 7
    description: IRQ39 pending
  IRQ40:
    type: RW
    reset_value: 0
    location: 8
    description: IRQ40 pending
  IRQ41:
    type: RW
    reset_value: 0
    location: 9
    description: IRQ41 pending
  IRQ42:
    type: RW
    reset_value: 0
    location: 10
    description: IRQ42 pending
  IRQ43:
    type: RW
    reset_value: 0
    location: 11
    description: IRQ43 pending
  IRQ44:
    type: RW
    reset_value: 0
    location: 12
    description: IRQ44 pending
  IRQ45:
    type: RW
    reset_value: 0
    location: 13
    description: IRQ45 pending
  IRQ46:
    type: RW
    reset_value: 0
    location: 14
    description: IRQ46 pending
  IRQ47:
    type: RW
    reset_value: 0
    location: 15
    description: IRQ47 pending
  IRQ48:
    type: RW
    reset_value: 0
    location: 16
    description: IRQ48 pending
  IRQ49:
    type: RW
    reset_value: 0
    location: 17
    description: IRQ49 pending
  IRQ50:
    type: RW
    reset_value: 0
    location: 18
    description: IRQ50 pending
  IRQ51:
    type: RW
    reset_value: 0
    location: 19
    description: IRQ51 pending
  IRQ52:
    type: RW
    reset_value: 0
    location: 20
    description: IRQ52 pending
  IRQ53:
    type: RW
    reset_value: 0
    location: 21
    description: IRQ53 pending
  IRQ54:
    type: RW
    reset_value: 0
    location: 22
    description: IRQ54 pending
  IRQ55:
    type: RW
    reset_value: 0
    location: 23
    description: IRQ55 pending
  IRQ56:
    type: RW
    reset_value: 0
    location: 24
    description: IRQ56 pending
  IRQ57:
    type: RW
    reset_value: 0
    location: 25
    description: IRQ57 pending
  IRQ58:
    type: RW
    reset_value: 0
    location: 26
    description: IRQ58 pending
  IRQ59:
    type: RW
    reset_value: 0
    location: 27
    description: IRQ59 pending
  IRQ60:
    type: RW
    reset_value: 0
    location: 28
    description: IRQ60 pending
  IRQ61:
    type: RW
    reset_value: 0
    location: 29
    description: IRQ61 pending
  IRQ62:
    type: RW
    reset_value: 0
    location: 30
    description: IRQ62 pending
  IRQ63:
    type: RW
    reset_value: 0
    location: 31
    description: IRQ63 pending
