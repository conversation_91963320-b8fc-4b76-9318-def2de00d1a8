# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

%version: 1.0

include "../../../../std/isa/isa/globals.isa"

builtin function delay {
  arguments XReg cycles
  description {
    Delay the processor by +cycles+ cycles.
  }
}

builtin function iss_syscall {
  arguments XReg id, XReg arg
  description {
    Instruction set simulator system call.
  }
}

builtin function read_device_32 {
  returns Bits<32>
  arguments XReg dev_addr
  description {
    Read 32 bits from non-memory-mapped device.
    Such devices have own addresses not related to memory map.
  }
}

builtin function write_device_32 {
  arguments XReg dev_addr, Bits<32> value
  description {
    Write 32 bits to non-memory-mapped device.
	Such devices have own addresses not related to memory map.
  }
}

builtin function sync_read_after_write_device {
  arguments <PERSON>olean completed, Bits<5> device_bitmask
  description {
    Synchronize non-memory-mapped device read-after-write.
	Such devices have own addresses not related to memory map.
	Devices specified by device bitmask (up to 5 devices).
	Stalls processor till condtions met.
	Conditions are that last device output started or completed (depends on argument).
  }
}

builtin function sync_write_after_read_device {
  arguments <PERSON><PERSON><PERSON> completed, Bits<5> device_bitmask
  description {
    Synchronize non-memory-mapped device write-after-read.
	Such devices have own addresses not related to memory map.
	Devices specified by device bitmask (up to 5 devices).
	Stalls processor till condtions met.
	Conditions are that last device input started or completed (depends on argument).
  }
}

function get_and_validate_stack_pointer {
  returns XReg
  arguments XReg sp, Bits<INSTR_ENC_SIZE> encoding
  description {
    Validate and return stack pointer.
	Stack pointer is validated to fit in range, defined by qc.mstktopaddr and qc.mstkbottomaddr CSRs.
	In case when stack pointer is out-of-range, raising exception SpOutOfRange.
	Stack pointer is also validated to be 16-byte aligned.
	In case when stack pointer is not 16-bytes aligned, raising exception IllegalStackPointer.
  }
  body {
     if (sp < $bits(CSR[qc.mstkbottomaddr])) {
       raise(ExceptionCode::SpOutOfRange, mode(), encoding);
     }
     else if (sp > $bits(CSR[qc.mstktopaddr])) {
       raise(ExceptionCode::SpOutOfRange, mode(), encoding);
     }
     else if ((sp & 0xf) != 0) {
       raise(ExceptionCode::IllegalStackPointer, mode(), encoding);
     }
     return sp;
   }
 }
