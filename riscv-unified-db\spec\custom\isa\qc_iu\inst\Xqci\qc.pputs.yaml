# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.pputs
long_name: Print string pseudo-instruction (hint) working only in simulation environment
description: |
  The print string instruction calls simulation environment with `rs1` explicit argument.
  The argument assumed to be pointer to the string in target simulated memory.
  Simulation environment expected to print the string on its console or standard output.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcisim
assembly: " xs1"
base: 32
encoding:
  match: 101000000000-----010000000010011
  variables:
    - name: rs1
      location: 19-15
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg func = 6;
  XReg arg = X[rs1];
  iss_syscall(func,arg);
