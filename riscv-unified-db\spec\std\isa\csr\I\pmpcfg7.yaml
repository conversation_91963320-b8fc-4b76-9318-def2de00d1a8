# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/I/pmpcfgN.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: pmpcfg7
base: 32 # odd numbered pmpcfg registers do not exist in RV64
long_name: PMP Configuration Register 7
address: 0x3A7
priv_mode: M
length: MXLEN
description: PMP entry configuration
definedBy: Smpmp
fields:
  pmp28cfg:
    location: 7-0
    description: |
      *PMP configuration for entry 28*

      The bits are as follows:

      [separator="!",%autowidth]
      !===
      ! Name ! Location ! Description

      h! L ! 7   ! Locks the entry from further modification. Additionally, when set, PMP checks also apply to M-mode for the entry.
      h! - ! 6:5 ! _Reserved_ Writes shall be ignored.
      h! A ! 4:3
      a! Address matching mode. One of:

          [when="PMP_GRANULARITY < 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NA4* (2) - Naturally aligned four-byte region
          * *NAPOT* (3) - Naturally aligned power of two

          [when="PMP_GRANULARITY >= 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NAPOT* (3) - Naturally aligned power of two

      [when="PMP_GRANULARITY >= 2"]
      Naturally aligned four-byte region, *NA4* (2), is not valid (not needed when the PMP granularity is larger than 4 bytes).

      h! X ! 2 ! When clear, instruction fetches cause an `Access Fault` for the matching region and privilege mode.
      h! W ! 1 ! When clear, stores and AMOs cause an `Access Fault` for the matching region and privilege mode.
      h! R ! 0 ! When clear, loads cause an `Access Fault` for the matching region and privilege mode.
      !===

      The combination of R = 0, W = 1 is reserved.
    type(): |
      if (NUM_PMP_ENTRIES > 28) {
        return CsrFieldType::RWR;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (NUM_PMP_ENTRIES > 28) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    sw_write(csr_value): |
      if ((CSR[pmpcfg7].pmp28cfg & 0x80) == 0) {
        # entry is not locked
        if (!(((csr_value.pmp28cfg & 0x1) == 0) && ((csr_value.pmp28cfg & 0x2) == 0x2))) {
          # not R = 0, W =1, which is reserved
          if ((PMP_GRANULARITY < 2) ||
              ((csr_value.pmp28cfg & 0x18) != 0x10)) {
            # NA4 is not allowed when PMP granularity is larger than 4 bytes
            return csr_value.pmp28cfg;
          }
        }
      }
      # fall through: keep old value
      return CSR[pmpcfg7].pmp28cfg;
  pmp29cfg:
    location: 15-8
    description: |
      *PMP configuration for entry 29*

      The bits are as follows:

      [separator="!",%autowidth]
      !===
      ! Name ! Location ! Description

      h! L ! 15   ! Locks the entry from further modification. Additionally, when set, PMP checks also apply to M-mode for the entry.
      h! - ! 14:13 ! _Reserved_ Writes shall be ignored.
      h! A ! 12:11
      a! Address matching mode. One of:

          [when="PMP_GRANULARITY < 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NA4* (2) - Naturally aligned four-byte region
          * *NAPOT* (3) - Naturally aligned power of two

          [when="PMP_GRANULARITY >= 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NAPOT* (3) - Naturally aligned power of two

      [when="PMP_GRANULARITY >= 2"]
      Naturally aligned four-byte region, *NA4* (2), is not valid (not needed when the PMP granularity is larger than 4 bytes).

      h! X ! 10 ! When clear, instruction fetches cause an `Access Fault` for the matching region and privilege mode.
      h! W ! 9 ! When clear, stores and AMOs cause an `Access Fault` for the matching region and privilege mode.
      h! R ! 8 ! When clear, loads cause an `Access Fault` for the matching region and privilege mode.
      !===

      The combination of R = 0, W = 1 is reserved.
    type(): |
      if (NUM_PMP_ENTRIES > 29) {
        return CsrFieldType::RWR;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (NUM_PMP_ENTRIES > 29) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    sw_write(csr_value): |
      if ((CSR[pmpcfg7].pmp29cfg & 0x80) == 0) {
        # entry is not locked
        if (!(((csr_value.pmp29cfg & 0x1) == 0) && ((csr_value.pmp29cfg & 0x2) == 0x2))) {
          # not R = 0, W =1, which is reserved
          if ((PMP_GRANULARITY < 2) ||
              ((csr_value.pmp29cfg & 0x18) != 0x10)) {
            # NA4 is not allowed when PMP granularity is larger than 4 bytes
            return csr_value.pmp29cfg;
          }
        }
      }
      # fall through: keep old value
      return CSR[pmpcfg7].pmp29cfg;
  pmp30cfg:
    location: 23-16
    description: |
      *PMP configuration for entry 30*

      The bits are as follows:

      [separator="!",%autowidth]
      !===
      ! Name ! Location ! Description

      h! L ! 23   ! Locks the entry from further modification. Additionally, when set, PMP checks also apply to M-mode for the entry.
      h! - ! 22:21 ! _Reserved_ Writes shall be ignored.
      h! A ! 20:19
      a! Address matching mode. One of:

          [when="PMP_GRANULARITY < 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NA4* (2) - Naturally aligned four-byte region
          * *NAPOT* (3) - Naturally aligned power of two

          [when="PMP_GRANULARITY >= 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NAPOT* (3) - Naturally aligned power of two

      [when="PMP_GRANULARITY >= 2"]
      Naturally aligned four-byte region, *NA4* (2), is not valid (not needed when the PMP granularity is larger than 4 bytes).

      h! X ! 18 ! When clear, instruction fetches cause an `Access Fault` for the matching region and privilege mode.
      h! W ! 17 ! When clear, stores and AMOs cause an `Access Fault` for the matching region and privilege mode.
      h! R ! 16 ! When clear, loads cause an `Access Fault` for the matching region and privilege mode.
      !===

      The combination of R = 0, W = 1 is reserved.
    type(): |
      if (NUM_PMP_ENTRIES > 30) {
        return CsrFieldType::RWR;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (NUM_PMP_ENTRIES > 30) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    sw_write(csr_value): |
      if ((CSR[pmpcfg7].pmp30cfg & 0x80) == 0) {
        # entry is not locked
        if (!(((csr_value.pmp30cfg & 0x1) == 0) && ((csr_value.pmp30cfg & 0x2) == 0x2))) {
          # not R = 0, W =1, which is reserved
          if ((PMP_GRANULARITY < 2) ||
              ((csr_value.pmp30cfg & 0x18) != 0x10)) {
            # NA4 is not allowed when PMP granularity is larger than 4 bytes
            return csr_value.pmp30cfg;
          }
        }
      }
      # fall through: keep old value
      return CSR[pmpcfg7].pmp30cfg;
  pmp31cfg:
    location: 31-24
    description: |
      *PMP configuration for entry 31*

      The bits are as follows:

      [separator="!",%autowidth]
      !===
      ! Name ! Location ! Description

      h! L ! 31   ! Locks the entry from further modification. Additionally, when set, PMP checks also apply to M-mode for the entry.
      h! - ! 30:29 ! _Reserved_ Writes shall be ignored.
      h! A ! 28:27
      a! Address matching mode. One of:

          [when="PMP_GRANULARITY < 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NA4* (2) - Naturally aligned four-byte region
          * *NAPOT* (3) - Naturally aligned power of two

          [when="PMP_GRANULARITY >= 2"]
          * *OFF* (0) - Null region (disabled)
          * *TOR* (1) - Top of range
          * *NAPOT* (3) - Naturally aligned power of two

      [when="PMP_GRANULARITY >= 2"]
      Naturally aligned four-byte region, *NA4* (2), is not valid (not needed when the PMP granularity is larger than 4 bytes).

      h! X ! 26 ! When clear, instruction fetches cause an `Access Fault` for the matching region and privilege mode.
      h! W ! 25 ! When clear, stores and AMOs cause an `Access Fault` for the matching region and privilege mode.
      h! R ! 24 ! When clear, loads cause an `Access Fault` for the matching region and privilege mode.
      !===

      The combination of R = 0, W = 1 is reserved.
    type(): |
      if (NUM_PMP_ENTRIES > 31) {
        return CsrFieldType::RWR;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (NUM_PMP_ENTRIES > 31) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
    sw_write(csr_value): |
      if ((CSR[pmpcfg7].pmp31cfg & 0x80) == 0) {
        # entry is not locked
        if (!(((csr_value.pmp31cfg & 0x1) == 0) && ((csr_value.pmp31cfg & 0x2) == 0x2))) {
          # not R = 0, W =1, which is reserved
          if ((PMP_GRANULARITY < 2) ||
              ((csr_value.pmp31cfg & 0x18) != 0x10)) {
            # NA4 is not allowed when PMP granularity is larger than 4 bytes
            return csr_value.pmp31cfg;
          }
        }
      }
      # fall through: keep old value
      return CSR[pmpcfg7].pmp31cfg;
