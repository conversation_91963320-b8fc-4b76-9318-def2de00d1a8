# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fcvt.d.q
long_name: Floating-point Convert Quad-precision to Double-precision
description:
  - id: inst-fcvt.d.q-behaviour
    normative: false
    text: |
      `fcvt.d.q` converts a quad-precision floating-point number to a double-precision floating-point number.
definedBy: Q
assembly: fd, fs1, rm
encoding:
  match: 010000100011-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
