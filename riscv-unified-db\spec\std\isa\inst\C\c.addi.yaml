# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.addi
long_name: Add a sign-extended non-zero immediate
description: |
  C.ADDI adds the non-zero sign-extended 6-bit immediate to the value in register xd then writes the result to xd.
  C.ADDI expands into `addi xd, xd, imm`.
  C.ADDI is only valid when xd &ne; x0 and imm &ne; 0.
  The code points with xd=x0 encode the C.NOP instruction; the remaining code points with imm=0 encode HINTs.
definedBy:
  anyOf:
    - C
    - Zca
assembly: xd, imm
encoding:
  match: 000-----------01
  variables:
    - name: imm
      location: 12|6-2
      not: 0
    - name: xd
      location: 11-7
      not: 0
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  X[xd] = X[xd] + $signed(imm);
