# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: <PERSON>c<PERSON>p
long_name: 16-bit May-be Operations
description: |
  The "Zcmop" extension, which defines eight 16-bit MOP
  instructions named C.MOP.__n__, where __n__ is an odd integer between 1 and
  15, inclusive.  C.MOP.__n__ is encoded in the reserved encoding space
  corresponding to C.LUI x__n__, 0, as shown in <<tab:c-mop>>.
  Unlike the MOPs defined in the Zimop extension, the C.MOP.__n__ instructions
  are defined to _not_ write any register.
  Their encoding allows future extensions to define them to read register
  `x[__n__]`.

  The Zcmop extension depends upon the Zca extension.

  NOTE: Very few suitable 16-bit encoding spaces exist.  This space was chosen
  because it already has unusual behavior with respect to the `rd`/`rs1`
  field--it encodes `c.addi16sp` when the field contains `x2`--and is
  therefore of lower value for most purposes.

  [[tab:c-mop]]
  .C.MOP.__n__ instruction encoding.

  |===
  |Mnemonic | Encoding           | Redefinable to read register

  |C.MOP.1  | `0110000010000001` | `x1`
  |C.MOP.3  | `0110000110000001` | `x3`
  |C.MOP.5  | `0110001010000001` | `x5`
  |C.MOP.7  | `0110001110000001` | `x7`
  |C.MOP.9  | `0110010010000001` | `x9`
  |C.MOP.11 | `0110010110000001` | `x11`
  |C.MOP.13 | `0110011010000001` | `x13`
  |C.MOP.15 | `0110011110000001` | `x15`
  |===

  NOTE: The recommended assembly syntax for C.MOP.__n__ is simply the nullary
  C.MOP.__n__.  The possibly accessed register is implicitly `x__n__`.

  NOTE: The expectation is that each Zcmop instruction is equivalent to some
  Zimop instruction, but the choice of expansion (if any) is left to the
  extension that redefines the MOP.
  Note, a Zcmop instruction that does not write a value can expand into a write
  to `x0`.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: C
