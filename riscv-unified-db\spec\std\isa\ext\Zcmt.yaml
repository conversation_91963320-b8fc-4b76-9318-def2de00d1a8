# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zcmt
long_name: 16-bit Table Jump
description: |
  Zcmt adds the table jump instructions and also adds the jvt CSR. The jvt CSR requires a
  state enable if Smstateen is implemented. See <<csrs-jvt>> for details.

  This extension reuses some encodings from _c.fsdsp_.  Therefore it is _incompatible_ with <<Zcd>>,
   which is included when C and D extensions are both present.

  NOTE: Zcmt is primarily targeted at embedded class CPUs due to implementation complexity. Additionally, it is not compatible with RVA profiles.

  The Zcmt extension depends on the <<Zca>> and Zicsr extensions.

  [%header,cols="^1,^1,4,8"]
  |===
  |RV32
  |RV64
  |Mnemonic
  |Instruction

  |yes
  |yes
  |cm.jt _index_
  |<<#insns-cm_jt>>

  |yes
  |yes
  |cm.jalt _index_
  |<<#insns-cm_jalt>>

  |===

type: unprivileged
company:
  name: RISC-V International
  url: https://riscv.org
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-04
    repositories:
      - url: https://github.com/riscv/riscv-code-size-reduction
        branch: main
    contributors:
      - name: Tariq Kurd
      - name: Ibrahim Abu Kharmeh
      - name: Torbjørn Viem Ness
      - name: Matteo Perotti
      - name: Nidal Faour
      - name: Bill Traynor
      - name: Rafael Sene
      - name: Xinlong Wu
      - name: sinan
      - name: Jeremy Bennett
      - name: Heda Chen
      - name: Alasdair Armstrong
      - name: Graeme Smecher
      - name: Nicolas Brunie
      - name: Jiawei
    implies:
      - name: Zca
        version: "1.0.0"
      - name: Zcb
        version: "1.0.0"
      - name: Zcmp
        version: "1.0.0"

      # TODO: this implication is conditional!!! (see description)
      # So it should look something like this:

      # if:
      #   allOf:
      #     param:
      #       XLEN: 32
      #     extensions:
      #       - F
      # then:
      #   - [Zca, "1.0.0"]
      #   - [Zcb, "1.0.0"]
      #   - [Zcmp, "1.0.0"]
      #   - [Zcmt, "1.0.0"]
      #   - [Zf, "1.0.0"]
      # else:
      #   # TODO: this implication is conditional!!! (see description)
      #   - [Zca, "1.0.0"]
      #   - [Zcb, "1.0.0"]
      #   - [Zcmp, "1.0.0"]
      #   - [Zcmt, "1.0.0"]

params:
  MSTATEEN_JVT_TYPE:
    when:
      name: Smstateen
      version: ~> 1.0
    schema:
      type: string
      enum: [rw, read-only-0, read-only-1]
    description: |
      Behavior of the mstateen0.JVT bit:

        * 'rw': read-write
        * 'read-only-0': read-only, fixed to 0
        * 'read-only-1': read-only, fixed to 1
  HSTATEEN_JVT_TYPE:
    when:
      allOf:
        - name: H
          version: ~> 1.0
        - name: Ssstateen
          version: ~> 1.0
    schema:
      type: string
      enum: [rw, read-only-0, read-only-1]
    description: |
      Behavior of the hstateen0.JVT bit:

        * 'rw': read-write
        * 'read-only-0': read-only, fixed to 0
        * 'read-only-1': read-only, fixed to 1
    extra_validation: |
      assert HSTATEEN_JVT_TYPE == 'read-only-0' if MSTATEEN_JVT_TYPE == 'read-only-0'
      assert HSTATEEN_JVT_TYPE == 'read-only-1' if MSTATEEN_JVT_TYPE == 'read-only-1'
  SSTATEEN_JVT_TYPE:
    when:
      allOf:
        - name: S
          version: ~> 1.0
        - name: Ssstateen
          version: ~> 1.0
    schema:
      type: string
      enum: [rw, read-only-0, read-only-1]
    description: |
      Behavior of the sstateen0.JVT bit:

        * 'rw': read-write
        * 'read-only-0': read-only, fixed to 0
        * 'read-only-1': read-only, fixed to 1
    extra_validation: |
      assert SSTATEEN_JVT_TYPE == 'read-only-0' if MSTATEEN_JVT_TYPE == 'read-only-0'
      assert SSTATEEN_JVT_TYPE == 'read-only-0' if HSTATEEN_JVT_TYPE == 'read-only-0'
      assert SSTATEEN_JVT_TYPE == 'read-only-1' if MSTATEEN_JVT_TYPE == 'read-only-1'
      assert SSTATEEN_JVT_TYPE == 'read-only-1' if HSTATEEN_JVT_TYPE == 'read-only-1'
