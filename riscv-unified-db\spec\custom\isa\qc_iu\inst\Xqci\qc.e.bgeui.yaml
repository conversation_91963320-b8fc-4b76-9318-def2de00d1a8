# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.bgeui
long_name: Branch on greater than or equal unsigned (immediate)
description: |
  Branches to `PC` + `offset` if the unsigned value in `rs1` is greater than or equal to the unsigned immediate `imm`.
  Instruction encoded in QC.EB instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibi
base: 32
encoding:
  match: -----------------------11111-----100-----0011111
  variables:
    - name: offset
      location: 31|7|30-25|11-8
      left_shift: 1
      sign_extend: true
    - name: imm
      location: 47-32
      not: 0
    - name: rs1
      location: 19-15
      not: 0
assembly: " xs1, imm, offset"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (X[rs1] >= imm) {
    jump_halfword($pc + $signed(offset));
  }
