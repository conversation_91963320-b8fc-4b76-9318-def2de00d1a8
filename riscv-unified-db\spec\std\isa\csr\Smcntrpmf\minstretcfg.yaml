# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
# yaml-language-server: $schema=../../../../schemas/csr_schema.json
kind: csr
name: minstretcfg
long_name: Machine Instructions-Retired Counter Configuration
address: 0x322
priv_mode: M
length: 64
definedBy: Smcntrpmf
description: |
  The `minstretcfg` CSR is a 64-bit machine-level register that configures privilege
  mode filtering for the `minstret` (Machine Instructions-Retired Counter). Each inhibit bit (xINH)
  disables counting of retired instructions in the associated privilege mode.

  | Field   | Description                                               |
  |---------|-----------------------------------------------------------|
  | MINH    | If set, then counting of events in M-mode is inhibited.  |
  | SINH    | If set, then counting of events in S/HS-mode is inhibited. |
  | UINH    | If set, then counting of events in U-mode is inhibited.  |
  | VSINH   | If set, then counting of events in VS-mode is inhibited. |
  | VUINH   | If set, then counting of events in VU-mode is inhibited. |

  When all inhibit bits are clear, instruction retirement is counted in all privilege modes.

  For each bit in 61:58, if the associated privilege mode is not implemented, the bit is read-only zero.

  Bit 63 (OF) always reads as zero, indicating that the counter does not generate overflow interrupts.

  Bits [57:0] are reserved (WPRI) and read as zero; writes are ignored.

  For RV32 systems, the upper 32 bits of `minstretcfg` are accessible via the `minstretcfgh` CSR (0x722).

fields:
  MINH:
    location: 62
    base: 64
    type: RW
    definedBy: M
    description: If set, then counting of events in M-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  SINH:
    location: 61
    base: 64
    type: RW
    definedBy: S
    description: If set, then counting of events in S/HS-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  UINH:
    location: 60
    base: 64
    type: RW
    definedBy: U
    description: If set, then counting of events in U-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  VSINH:
    location: 59
    base: 64
    type: RW
    definedBy: H
    description: If set, then counting of events in VS-mode is inhibited.
    reset_value: UNDEFINED_LEGAL

  VUINH:
    location: 58
    base: 64
    type: RW
    definedBy: H
    description: If set, then counting of events in VU-mode is inhibited.
    reset_value: UNDEFINED_LEGAL
