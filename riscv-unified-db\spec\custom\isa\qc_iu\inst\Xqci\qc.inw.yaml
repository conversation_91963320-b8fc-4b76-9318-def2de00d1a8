# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.inw
long_name: Input word from non-memory-mapped device
description: |
  Input 32 bits of data into register `rd` from a non-memory-mapped device.
  Such devices have own address space, unrelated to memory map.
  Device space address formed by adding `rs1` to  to a unsigned offset `imm`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqciio
assembly: xd, imm(xs1)
base: 32
encoding:
  match: -----------------101-----0001011
  variables:
    - name: imm
      location: 31-20
      left_shift: 2
    - name: rs1
      location: 19-15
    - name: rd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg device_address = X[rs1] + imm;
  X[rd] = read_device_32(device_address);
