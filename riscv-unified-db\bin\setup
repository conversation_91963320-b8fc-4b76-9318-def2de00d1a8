#!/usr/bin/env bash

ROOT=$(dirname $(dirname $(realpath ${BASH_SOURCE[0]})))
OLDWD=$PWD
cd $ROOT

CONTAINER_TAG=`cat ${ROOT}/bin/.container-tag`

# Supported container types:
#
# = devcontainer
#
# When devcontainer is used, all commands run "natively" (no container prefix), because it's assumed
# that the command itself is already in the container context
#
# = docker
#
# A docker container. All commands on the host (using ./do or ./bin/*) are run in the context of a Docker image.
#
# = singularity
#
# An apptainer/singularity container. All commands on the host (using ./do or ./bin/*) are run in the context of an Apptainer/Singularity image.
#
#
# ALL THREE CONTAINERS ARE (in theory) IDENTICAL. Which one you use is a preference of your setup and installed
# software.


# get the container type
#
# use, in priority order:
#  1. devcontainer, if DEVCONTAINER_ENV is set in the environment
#  2. the type stored in .container-type
#  3. Docker, if DOCKER is set in the environment
#  4. Singularity, if SINGULARI<PERSON> is set in the environment
#  5. The choice made by the user, which will be cached in .container-type
if [ -v DEVCONTAINER_ENV ]; then
CONTAINER_TYPE=devcontainer
elif [ -f ${ROOT}/.container-type ]; then
CONTAINER_TYPE=`cat ${ROOT}/.container-type`
elif [ -v DOCKER ]; then
CONTAINER_TYPE=docker
elif [ -v SINGULARITY ]; then
CONTAINER_TYPE=singularity
else
  echo -e "UDB tools run in a container. Both Docker and Singularity/Apptainer are supported.\\n\\n1. Docker\\n2. Singularity\\n"
  while true; do
    echo "Which would you like to use? (1/2) "
    read ans
    case $ans in
        [1]* ) CONTAINER_TYPE=docker; break;;
        [2]* ) CONTAINER_TYPE=singularity; break;;
        * ) echo -e "\\nPlease answer 1 or 2.";;
    esac
  done
fi

if [ "${CONTAINER_TYPE}" != "docker" -a "${CONTAINER_TYPE}" != "singularity" -a "${CONTAINER_TYPE}" != "devcontainer" ]; then
  echo "BAD CONTAINER TYPE: ${CONTAINER_TYPE}"
fi

if [ ! -f ${ROOT}/.container-type ]; then
  echo ${CONTAINER_TYPE} > ${ROOT}/.container-type
fi

echo "Using ${CONTAINER_TYPE} environment"

if [ -v GITHUB_ACTIONS ]; then
    echo "Running in a GitHub Action"
    CONTAINER_PATH=${ROOT}/.singularity/image.sif
    HOME_PATH=${GITHUB_WORKSPACE}
    HOME_OPT="--home ${ROOT}/.home"
    SINGULARITY_CACHE=--disable-cache

    # needed to get singularity working on Ubuntu 24.04
    # see https://github.com/lima-vm/lima/issues/2319
    sudo /bin/bash -c "echo \"kernel.apparmor_restrict_unprivileged_userns = 0\" >/etc/sysctl.d/99-userns.conf"
    sudo sysctl --system
elif [ "${CONTAINER_TYPE}" == "docker" ]; then
    if ! docker images riscvintl/udb:${CONTAINER_TAG} | grep -q udb ; then
        # TODO: pull the image if it can be found
        echo "Building Docker image..."
        docker build -t riscvintl/udb:${CONTAINER_TAG} -f .devcontainer/Dockerfile .
    fi
    if [ -t 1 -a -t 0 ]; then
      DOCKER_BASE="docker run -it -v ${ROOT}:${ROOT} -w ${ROOT} riscvintl/udb:${CONTAINER_TAG}"
    else
      DOCKER_BASE="docker run -v ${ROOT}:${ROOT} -w ${ROOT} riscvintl/udb:${CONTAINER_TAG}"
    fi
    RUN="${DOCKER_BASE}"
elif [ "${CONTAINER_TYPE}" == "singularity" ]; then
    CONTAINER_PATH=${ROOT}/.singularity/image-$CONTAINER_TAG.sif
    HOME_PATH=${HOME}
    HOME_OPT="--bind ${ROOT}/.home:${HOME_PATH}"
    SINGULARITY_CACHE=
    if [ ! -f ${CONTAINER_PATH} ]; then
      echo "Fetching container..."
      if [ ! -d "${ROOT}/.singularity" ]; then
        mkdir -p ${ROOT}/.singularity
      fi
      singularity pull ${SINGULARITY_CACHE} ${CONTAINER_PATH} oras://docker.io/riscvintl/spec-generator:$CONTAINER_TAG
    fi
elif [ "${CONTAINER_TYPE}" == "devcontainer" ]; then
    HOME_PATH=${HOME}
else
    echo "Bad container type: ${CONTAINER_TYPE}" 1>&2
    exit 1
fi

if [ -f $ROOT/.git ]; then
    # if this is a worktree, need to add the parent git repo in, too
    GIT_PATH=`git rev-parse --git-common-dir | tr -d '\n' | xargs dirname`
    HOME_OPT="${HOME_OPT} --bind ${GIT_PATH}:${GIT_PATH}"
fi

if [ "${CONTAINER_TYPE}" == "devcontainer" ]; then
    RUN=""
elif [ "${CONTAINER_TYPE}" == "docker" ]; then
    RUN="${DOCKER_BASE}"
elif [ "${CONTAINER_TYPE}" == "singularity" ]; then
    RUN="singularity run ${HOME_OPT} ${CONTAINER_PATH}"
else
    echo "Bad container type: ${CONTAINER_TYPE}" 1>&2
    exit 1
fi

if [ ! -d $ROOT/.home ]; then
    mkdir $ROOT/.home
fi

if [ ! -f $ROOT/.bundle/config ]; then
    OLDDIR=$PWD
    cd $ROOT
    ${RUN} bundle config set --local path ${ROOT}/.home/.gems
    ${RUN} bundle config set --local cache_path ${ROOT}/.home/.cache
    ${RUN} bundle config set --local with development
    cd $OLDDIR
fi

if [ ! -d $ROOT/.home/.gems ]; then
    OLDDIR=$PWD
    cd $ROOT
    ${RUN} bundle install
    cd $OLDDIR
fi

if [ ! -f $ROOT/ext/rbi-central/README.md ]; then
    git submodule update --init ext/rbi-central
fi

if [ ! -d $ROOT/.home/.venv ]; then
    ${RUN} /usr/bin/python3 -m venv ${ROOT}/.home/.venv
fi

source ${ROOT}/.home/.venv/bin/activate

if [ ! -f ${ROOT}/.home/.venv/bin/pre-commit ]; then
    ${RUN} ${ROOT}/.home/.venv/bin/pip install -r requirements.txt
fi

# if [ ! -f $ROOT/ext/riscv-opcodes/README.md ]; then
#   git submodule update --init ext/riscv-opcodes
# fi

if [[ ! -z "$DEVELOPMENT" && $DEVELOPMENT -eq 1 ]]; then
    if [ ! -d "${ROOT}/.home/.yard/gem_index" ]; then
        ${RUN} bundle exec --gemfile ${ROOT}/Gemfile yard config --gem-install-yri
        ${RUN} bundle exec --gemfile ${ROOT}/Gemfile yard gems
        touch ${ROOT}/.stamps/dev_gems
    fi
fi

if [ ! -d ${ROOT}/node_modules ]; then
    ${RUN} npm i
fi

if [ "${CONTAINER_TYPE}" == "devcontainer" ]; then
    BUNDLE="bundle"
    RUBY="bundle exec ruby"
    RAKE="bundle exec rake"
    NPM="npm"
    NPX="npx"
    NODE="node"
    PYTHON="${ROOT}/.home/.venv/bin/python3"
    PIP="${ROOT}/.home/.venv/bin/pip"
    BASH="bash"
    GPP="g++"
    GDB="gdb"
    CLANG_FORMAT="clang-format"
    CLANG_TIDY="clang-tidy"
    MAKE="make"
elif [ "${CONTAINER_TYPE}" == "docker" ]; then
    BUNDLE="${DOCKER_BASE} bundle"
    RUBY="${DOCKER_BASE} bundle exec ruby"
    RAKE="${DOCKER_BASE} bundle exec rake"
    NPM="${DOCKER_BASE} npm"
    NPX="${DOCKER_BASE} npx"
    NODE="${DOCKER_BASE} node"
    PYTHON="${DOCKER_BASE} ${ROOT}/.home/.venv/bin/python3"
    PIP="${DOCKER_BASE} ${ROOT}/.home/.venv/bin/pip"
    BASH="${DOCKER_BASE} bash"
    GPP="${DOCKER_BASE} g++"
    GDB="${DOCKER_BASE} gdb"
    CLANG_FORMAT="${DOCKER_BASE} clang-format"
    CLANG_TIDY="${DOCKER_BASE} clang-tidy"
    MAKE="${DOCKER_BASE} make"
elif [ "${CONTAINER_TYPE}" == "singularity" ]; then
    BUNDLE="singularity run ${HOME_OPT} ${CONTAINER_PATH} bundle"
    RUBY="singularity run ${HOME_OPT} ${CONTAINER_PATH} bundle exec ruby"
    RAKE="singularity run ${HOME_OPT} ${CONTAINER_PATH} bundle exec rake"
    NPM="singularity run ${HOME_OPT} ${CONTAINER_PATH} npm"
    NPX="singularity run ${HOME_OPT} ${CONTAINER_PATH} npx"
    NODE="singularity run ${HOME_OPT} ${CONTAINER_PATH} node"
    PYTHON="singularity run ${HOME_OPT} ${CONTAINER_PATH} ${ROOT}/.home/.venv/bin/python3"
    PIP="singularity run ${HOME_OPT} ${CONTAINER_PATH} ${ROOT}/.home/.venv/bin/pip"
    BASH="singularity run ${HOME_OPT} ${CONTAINER_PATH} bash"
    GPP="singularity run ${HOME_OPT} ${CONTAINER_PATH} g++"
    GDB="singularity run ${HOME_OPT} ${CONTAINER_PATH} gdb"
    CLANG_FORMAT="singularity run ${HOME_OPT} ${CONTAINER_PATH} clang-format"
    CLANG_TIDY="singularity run ${HOME_OPT} ${CONTAINER_PATH} clang-tidy"
    MAKE="singularity run ${HOME_OPT} ${CONTAINER_PATH} make"
else
    echo "Bad container type: ${CONTAINER_TYPE}" 1>&2
    exit 1
fi

git config --local commit.template ${ROOT}/.gitmessage

GIT_REPO_ROOT=`git rev-parse --path-format=absolute --git-common-dir | tr -d '\n'`
if [ ! -f $GIT_REPO_ROOT/hooks/pre-commit ]; then
  cat << HOOK > $GIT_REPO_ROOT/hooks/pre-commit
#!/usr/bin/env bash
# File generated by pre-commit: https://pre-commit.com
# ID: 138fd403232d2ddd5efb44317e38bf03

# start templated
INSTALL_PYTHON=\$(realpath ./bin/python)
ARGS=(hook-impl --config=.pre-commit-config.yaml --hook-type=pre-commit)
# end templated

HERE="$(cd "$(dirname "\$0")" && pwd)"
ARGS+=(--hook-dir "\$HERE" -- "\$@")

if [ -x "\$INSTALL_PYTHON" ]; then
    exec "\$INSTALL_PYTHON" -mpre_commit "\${ARGS[@]}"
elif command -v pre-commit > /dev/null; then
    exec pre-commit "\${ARGS[@]}"
else
    echo '\`pre-commit\` not found.  Did you forget to activate your virtualenv?' 1>&2
    exit 1
fi
HOOK
  chmod +x $GIT_REPO_ROOT/hooks/pre-commit
fi

if [ ! -f $GIT_REPO_ROOT/hooks/commit-msg ]; then
  cat << HOOK > $GIT_REPO_ROOT/hooks/commit-msg
#!/usr/bin/env bash
# File generated by pre-commit: https://pre-commit.com
# ID: 138fd403232d2ddd5efb44317e38bf03

# start templated
INSTALL_PYTHON=\$(realpath ./bin/python)
ARGS=(hook-impl --config=.pre-commit-config.yaml --hook-type=commit-msg)
# end templated

HERE="$(cd "$(dirname "\$0")" && pwd)"
ARGS+=(--hook-dir "\$HERE" -- "\$@")

if [ -x "\$INSTALL_PYTHON" ]; then
    exec "\$INSTALL_PYTHON" -mpre_commit "\${ARGS[@]}"
elif command -v pre-commit > /dev/null; then
    exec pre-commit "\${ARGS[@]}"
else
    echo '\`pre-commit\` not found.  Did you forget to activate your virtualenv?' 1>&2
    exit 1
fi
HOOK
  chmod +x $GIT_REPO_ROOT/hooks/commit-msg
fi
