# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/hpmcounterNh.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: hpmcounter22h
long_name: User-mode Hardware Performance Counter 19, high half
address: 0xC96
base: 32
description: |
  Alias for M-mode CSR `mhpmcounter22h`.

  Privilege mode access is controlled with `mcounteren.HPM22`, `scounteren.HPM22`, and `hcounteren.HPM22` as follows:

  [%autowidth,cols="1,1,1,1,1,1,1",separator="!"]
  !===
  .2+h![.rotate]#`mcounteren.HPM22`# .2+h! [.rotate]#`scounteren.HPM22`# .2+h! [.rotate]#`hcounteren.HPM22`#
  4+^.>h! `hpmcounter22h` behavior
  .^h! S-mode .^h! U-mode .^h! VS-mode .^h! VU-mode

  ! 0 ! - ! - ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction` ! `IllegalInstruction`
  ! 1 ! 0 ! 0 ! read-only ! `IllegalInstruction` ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 1 ! 0 ! read-only ! read-only ! `VirtualInstruction` ! `VirtualInstruction`
  ! 1 ! 0 ! 1 ! read-only ! `IllegalInstruction` ! read-only ! `VirtualInstruction`
  ! 1 ! 1 ! 1 ! read-only ! read-only ! read-only ! read-only
  !===
priv_mode: U
length: 32
definedBy: Sscofpmf
fields:
  COUNT:
    location: 31-0
    alias: mhpmcounter22h.COUNT[63:32]
    description: Alias of `mhpmcounter22h.COUNT`.
    type: RO-H
    reset_value: UNDEFINED_LEGAL
sw_read(): |
  # access is determined by *counteren CSRs
  if (mode() == PrivilegeMode::S) {
    # S-mode is present ->
    #   mcounteren determines access in S-mode
    if (CSR[mcounteren].HPM22 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::U) {
    if (CSR[misa].S == 1'b1) {
      # S-mode is present ->
      #   mcounteren and scounteren together determine access in U-mode
      if ((CSR[mcounteren].HPM22 & CSR[scounteren].HPM22) == 1'b0) {
        raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
    } else if (CSR[mcounteren].HPM22 == 1'b0) {
      # S-mode is not present ->
      #   mcounteren determines access in U-mode
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VS) {
    # access in VS mode
    if (CSR[hcounteren].HPM22 == 1'b0 && CSR[mcounteren].HPM22 == 1'b1) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].HPM22 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  } else if (mode() == PrivilegeMode::VU) {
    # access in VU mode
    if (((CSR[hcounteren].HPM22 & CSR[scounteren].HPM22) == 1'b0) && (CSR[mcounteren].HPM22 == 1'b1)) {
      raise(ExceptionCode::VirtualInstruction, mode(), $encoding);
    } else if (CSR[mcounteren].HPM22 == 1'b0) {
      raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
    }
  }

  return read_hpm_counter(22)[63:32];
