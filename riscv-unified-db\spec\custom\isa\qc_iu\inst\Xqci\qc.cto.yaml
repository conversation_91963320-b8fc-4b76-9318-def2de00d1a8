# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.cto
long_name: Count trailing ones
description: |
  Count the number of ones before the first zero in `rs1`, starting from the LSB
  and progressing to the MSB.
  Accordingly, if the input is ~0, the output is XLEN, and if the least-significant
  bit of the input is a 0, the output is 0.
  Output written to `rd`.
  Instruction encoded in I instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 000010100000-----011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = lowest_set_bit(~X[rs1]);
