# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.addusat
long_name: Saturating unsigned addition
description: |
  Add unsigned values `rs1` and `rs2`, saturate the unsigned result, and write to `rd`.
  Instruction encoded in R instruction format
definedBy:
  anyOf:
    - Xqci
    - Xqcia
base: 32
encoding:
  match: 0001111----------011-----0001011
  variables:
    - name: rs1
      location: 19-15
      not: 0
    - name: rs2
      location: 24-20
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  Bits<xlen()+1> sum = X[rs1] `+ X[rs2];
  Bits<xlen()+1> largest_unsigned_value = {1'b0, {xlen(){1'b1}}};

  if (sum > largest_unsigned_value) {
    X[rd] = largest_unsigned_value[(xlen() - 1):0];
  } else {
    X[rd] = sum[(xlen() - 1):0];
  }
