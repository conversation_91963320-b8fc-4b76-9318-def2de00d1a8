# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.shladd
long_name: Shift left and add (immediate)
description: |
  Left shift _rs1_ by _shamt_ and add the value in _rs2_.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - name: Xqci
      version: ">= 0.2"
    - Xqciac
base: 32
encoding:
  match: 01---------------011-----0001011
  variables:
    - name: shamt
      location: 29-25
      not: [0, 1, 2, 3]
    - name: rs2
      location: 24-20
      not: 0
    - name: rs1
      location: 19-15
      not: 0
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2, shamt"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  X[rd] = (X[rs1] << shamt) + X[rs2];
