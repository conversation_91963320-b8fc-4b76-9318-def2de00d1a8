# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: scounteren
long_name: Supervisor Counter Enable
address: 0x106
priv_mode: S
length: 32
description: |
  Delegates control of the hardware performance-monitoring counters
  to U-mode
definedBy: S
fields:
  CY:
    location: 0
    description: |
      When both `scounteren.CY` and `mcounteren.CY` are set, the `cycle` CSR (an alias of `mcycle`) is accessible to U-mode
      <%% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.CY`)<%% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[0]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[0]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  TM:
    location: 1
    description: |
      When both `scounteren.TM` and `mcounteren.TM` are set, the `time` CSR (an alias of `mtime` memory-mapped CSR) is accessible to U-mode
      <%% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.TM`)<%% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[1]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[1]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  IR:
    location: 2
    description: |
      When both `scounteren.IR` and `mcounteren.IR` are set, the `instret` CSR (an alias of memory-mapped `minstret`) is accessible to U-mode
      <%% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.IR`)<%% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[2]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[2]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  <%- (3..31).each do |hpm_num| -%>
  HPM<%= hpm_num %>:
    location: <%= hpm_num %>
    description: |
      When both `scounteren.HPM<%= hpm_num %>` and `mcounteren.HPM<%= hpm_num %>` are set, the `hpmcounter<%= hpm_num %>` CSR (an alias of `mhpmcounter<%= hpm_num %>`)
      is accessible to U-mode
      <%% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM<%= hpm_num %>`)<%% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[<%= hpm_num %>]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[<%= hpm_num %>]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  <%- end -%>
