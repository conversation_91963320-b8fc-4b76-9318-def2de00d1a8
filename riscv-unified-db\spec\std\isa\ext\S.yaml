# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: S
type: privileged
long_name: Supervisor mode
versions:
- version: "1.11.0"
  state: ratified
  ratification_date: 2019-06
  requires:
    name: U
    version: "= 1.0.0"
- version: "1.12.0"
  state: ratified
  ratification_date: 2021-12
  requires:
    name: U
    version: "= 1.0.0"
- version: "1.13.0"
  state: ratified
  ratification_date: null
  requires:
    name: U
    version: "= 1.0.0"
description: |
  This chapter describes the RISC-V supervisor-level architecture, which
  contains a common core that is used with various supervisor-level
  address translation and protection schemes.

  [NOTE]
  ====
  Supervisor mode is deliberately restricted in terms of interactions with
  underlying physical hardware, such as physical memory and device
  interrupts, to support clean virtualization. In this spirit, certain
  supervisor-level facilities, including requests for timer and
  interprocessor interrupts, are provided by implementation-specific
  mechanisms. In some systems, a supervisor execution environment (SEE)
  provides these facilities in a manner specified by a supervisor binary
  interface (SBI). Other systems supply these facilities directly, through
  some other implementation-defined mechanism.
  ====
params:
  MUTABLE_MISA_S:
    description: |
      Indicates whether or not the `S` extension can be disabled with the `misa.S` bit.
    schema:
      type: boolean
    extra_validation: |
      # If U mode can be disabled, then S mode must also be disabled since you can't
      # be in S mode without U mode
      assert MUTABLE_MISA_S if MUTABLE_MISA_U
  ASID_WIDTH:
    description: |
      Number of implemented ASID bits. Maximum is 16 for XLEN==64, and 9 for XLEN==32
    schema:
      type: integer
      minimum: 0
      maximum: 16
    extra_validation: |
      assert ASID_WIDTH <= 9 if XLEN == 32
  S_MODE_ENDIANNESS:
    description: |
      Endianness of data in S-mode. Can be one of:

       * little:  S-mode data is always little endian
       * big:     S-mode data is always big endian
       * dynamic: S-mode data can be either little or big endian,
                  depending on the CSR field `mstatus.SBE`
    schema:
      type: string
      enum: [little, big, dynamic]
  SXLEN:
    description: |
      Set of XLENs supported in S-mode. Can be one of:

        * 32:   SXLEN is always 32
        * 64:   SXLEN is always 64
        * 3264: SXLEN can be changed (via mstatus.SXL) between 32 and 64
    schema:
      type: integer
      enum: [32, 64, 3264]
    extra_validation: |
      assert SXLEN == 32 if XLEN == 32
      assert (SXLEN != 32) if UXLEN != 32
  REPORT_VA_IN_MTVAL_ON_LOAD_PAGE_FAULT:
    description: |
      When true, `mtval` is written with the virtual address of a load when it causes a
      `LoadPageFault`.

      WHen false, `mtval` is written with 0 when a load causes a `LoadPageFault`.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_STORE_AMO_PAGE_FAULT:
    description: |
      When true, `mtval` is written with the virtual address of a store when it causes a
      `StoreAmoPageFault`.

      WHen false, `mtval` is written with 0 when a store causes a `StoreAmoPageFault`.
    schema:
      type: boolean
  REPORT_VA_IN_MTVAL_ON_INSTRUCTION_PAGE_FAULT:
    description: |
      When true, `mtval` is written with the virtual PC of an instructino when fetch causes an
      `InstructionPageFault`.

      WHen false, `mtval` is written with 0 when an instruction fetch causes an
      `InstructionPageFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_BREAKPOINT:
    description: |
      When true, `stval` is written with the virtual PC of the EBREAK instruction (same information as `mepc`).

      When false, `stval` is written with 0 on an EBREAK instruction.

      Regardless, `stval` is always written with a virtual PC when an external breakpoint is generated
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_LOAD_MISALIGNED:
    description: |
      When true, `stval` is written with the virtual address of a load instruction when the
      address is misaligned and MISALIGNED_LDST is false.

      When false, `stval` is written with 0 when a load address is misaligned and
      MISALIGNED_LDST is false.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_STORE_AMO_MISALIGNED:
    description: |
      When true, `stval` is written with the virtual address of a store instruction when the
      address is misaligned and MISALIGNED_LDST is false.

      When false, `stval` is written with 0 when a store address is misaligned and
      MISALIGNED_LDST is false.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_INSTRUCTION_MISALIGNED:
    description: |
      When true, `stval` is written with the virtual PC when an instruction fetch is misaligned.

      When false, `stval` is written with 0 when an instruction fetch is misaligned.

      Note that when IALIGN=16 (i.e., when the `C` or one of the `Zc*` extensions are implemented),
      it is impossible to generate a misaligned fetch, and so this parameter has no effect.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_LOAD_ACCESS_FAULT:
    description: |
      When true, `stval` is written with the virtual address of a load when it causes a
      `LoadAccessFault`.

      WHen false, `stval` is written with 0 when a load causes a `LoadAccessFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_STORE_AMO_ACCESS_FAULT:
    description: |
      When true, `stval` is written with the virtual address of a store when it causes a
      `StoreAmoAccessFault`.

      WHen false, `stval` is written with 0 when a store causes a `StoreAmoAccessFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_INSTRUCTION_ACCESS_FAULT:
    description: |
      When true, `stval` is written with the virtual PC of an instructino when fetch causes an
      `InstructionAccessFault`.

      WHen false, `stval` is written with 0 when an instruction fetch causes an
      `InstructionAccessFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_LOAD_PAGE_FAULT:
    description: |
      When true, `stval` is written with the virtual address of a load when it causes a
      `LoadPageFault`.

      WHen false, `stval` is written with 0 when a load causes a `LoadPageFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_STORE_AMO_PAGE_FAULT:
    description: |
      When true, `stval` is written with the virtual address of a store when it causes a
      `StoreAmoPageFault`.

      WHen false, `stval` is written with 0 when a store causes a `StoreAmoPageFault`.
    schema:
      type: boolean
  REPORT_VA_IN_STVAL_ON_INSTRUCTION_PAGE_FAULT:
    description: |
      When true, `stval` is written with the virtual PC of an instructino when fetch causes an
      `InstructionPageFault`.

      WHen false, `stval` is written with 0 when an instruction fetch causes an
      `InstructionPageFault`.
    schema:
      type: boolean
  REPORT_ENCODING_IN_STVAL_ON_ILLEGAL_INSTRUCTION:
    description: |
      When true, `stval` is written with the encoding of an instruction that causes an
      `IllegalInstruction` exception.

      When false `stval` is written with 0 when an `IllegalInstruction` exception occurs.
    schema:
      type: boolean
  STVAL_WIDTH:
    description: |
      The number of implemented bits in `stval`.

      Must be greater than or equal to _max_(`PHYS_ADDR_WIDTH`, `VA_SIZE`)
    schema:
      type: integer
      maximum: 0xffffffffffffffff
  SCOUNTENABLE_EN:
    description: |
      Indicates which counters can delegated via `scounteren`

      An unimplemented counter cannot be specified, i.e., if
      HPM_COUNTER_EN[3] is false, it would be illegal to set
      SCOUNTENABLE_EN[3] to true.

      SCOUNTENABLE_EN[0:2] must all be false if `Zicntr` is not implemented.
      SCOUNTENABLE_EN[3:31] must all be false if `Zihpm` is not implemented.
    schema:
      type: array
      items:
        type: boolean
      maxItems: 32
      minItems: 32
    extra_validation: |
      SCOUNTENABLE_EN[0..2].all? { |en| !en } unless ext?(:Zicntr)
      SCOUNTENABLE_EN[3..].all? { |en| !en } unless ext?(:Zihpm)

      # SCOUNTEN_EN can only be writable if the hpm counter exists
      SCOUNTENABLE_EN.each_with_index { |scounten, idx| next if idx < 3; assert (!scounten || HPM_COUNTER_EN[idx]) }
  STVEC_MODE_DIRECT:
    description: |
      Whether or not `stvec.MODE` supports Direct (0).
    schema:
      type: boolean
    extra_validation: assert STVEC_MODE_DIRECT || STVEC_MODE_VECTORED
  STVEC_MODE_VECTORED:
    description: |
      Whether or not `stvec.MODE` supports Vectored (1).
    schema:
      type: boolean
    extra_validation: assert STVEC_MODE_DIRECT || STVEC_MODE_VECTORED
  SATP_MODE_BARE:
    description: |
      Whether or not satp.MODE == Bare is supported.
    schema:
      type: boolean
  TRAP_ON_ECALL_FROM_S:
    description: |
      Whether or not an ECALL-from-S-mode causes a synchronous exception.

      The spec states that implementations may handle ECALLs transparently
      without raising a trap, in which case the EEI must provide a builtin.
    schema:
      type: boolean
      default: true
  TRAP_ON_SFENCE_VMA_WHEN_SATP_MODE_IS_READ_ONLY:
    description: |
      For implementations that make `satp`.MODE read-only zero
      (always Bare, _i.e._, no virtual translation is implemented),
      attempts to execute an SFENCE.VMA instruction might raise an
      illegal-instruction exception.

      TRAP_ON_SFENCE_VMA_WHEN_SATP_MODE_IS_READ_ONLY indicates whether
      or not that exception occurs.

      TRAP_ON_SFENCE_VMA_WHEN_SATP_MODE_IS_READ_ONLY has no effect when
      some virtual translation mode is supported.
    schema:
      type: boolean
      default: false
    extra_validation: assert TRAP_ON_SFENCE_VMA_WHEN_SATP_MODE_IS_READ_ONLY == false if ext?(:Sv32) || ext?(:Sv39) || ext?(:Sv48) || ext?(:Sv57)
  MSTATUS_FS_WRITABLE:
    description: |
      When `S` is enabled but `F` is not, mstatus.FS is optionally writable.
    schema:
      type: boolean
    extra_validation: |
      assert MSTATUS_FS_WRITABLE == true  if ext?(:F)
      assert MSTATUS_FS_WRITABLE == false if (!ext?(:S) && !ext?(:F))
  MSTATUS_VS_WRITABLE:
    description: |
      When `S` is enabled but `V` is not, mstatus.VS is optionally writable.
    schema:
      type: boolean
    extra_validation: |
      assert MSTATUS_VS_WRITABLE == true  if ext?(:V)
      assert MSTATUS_VS_WRITABLE == false if (!ext?(:S) && !ext?(:V))
  MSTATUS_FS_LEGAL_VALUES:
    description: |
      The set of values that mstatus.FS will accept from a software write.
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1, 2, 3]
      maxItems: 4
      uniqueItems: true
    also_defined_in: F
    extra_validation: |
      assert MSTATUS_FS_LEGAL_VALUES.include?(0) && MSTATUS_FS_LEGAL_VALUES.include?(3) if ext?(:F)
  MSTATUS_VS_LEGAL_VALUES:
    description: |
      The set of values that mstatus.VS will accept from a software write.
    schema:
      type: array
      items:
        type: integer
        enum: [0, 1, 2, 3]
      maxItems: 4
      uniqueItems: true
    also_defined_in: V
    extra_validation: |
      assert MSTATUS_VS_LEGAL_VALUES.include?(0) && MSTATUS_VS_LEGAL_VALUES.include?(3) if ext?(:V)

      # if HW is writing VS, then Dirty (3) better be a supported value
      assert MSTATUS_VS_LEGAL_VALUES.include?(3) if ext?(:V) && (HW_MSTATUS_VS_DIRTY_UPDATE != "never")
  MSTATUS_TVM_IMPLEMENTED:
    description: |
      Whether or not mstatus.TVM is implemented.

      When not implemented mstatus.TVM will be read-only-zero.
    schema:
      type: boolean
  MSTATEEN_ENVCFG_TYPE:
    when:
      name: Smstateen
      version: ~> 1.0
    schema:
      type: string
      enum: [rw, read-only-0, read-only-1]
    description: |
      Behavior of the mstateen0.ENVCFG bit:

        * 'rw': read-write
        * 'read-only-0': read-only, fixed to 0
        * 'read-only-1': read-only, fixed to 1
  HSTATEEN_ENVCFG_TYPE:
    when:
      allOf:
        - name: H
          version: ~> 1.0
        - name: Ssstateen
          version: ~> 1.0
    schema:
      type: string
      enum: [rw, read-only-0, read-only-1]
    description: |
      Behavior of the hstateen0.ENVCFG bit:

        * 'rw': read-write
        * 'read-only-0': read-only, fixed to 0
        * 'read-only-1': read-only, fixed to 1
    extra_validation: |
      assert HSTATEEN_ENVCFG_TYPE == 'read-only-0' if MSTATEEN_ENVCFG_TYPE == 'read-only-0'
      assert HSTATEEN_ENVCFG_TYPE == 'read-only-1' if MSTATEEN_ENVCFG_TYPE == 'read-only-1'
