# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: <PERSON><PERSON>ha
long_name: Byte and Halfword Atomic Memory Operations
type: unprivileged
description: |
  Adds byte and halfword atomic memory operations to the RISC-V Unprivileged ISA.
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: <PERSON><PERSON><PERSON>
