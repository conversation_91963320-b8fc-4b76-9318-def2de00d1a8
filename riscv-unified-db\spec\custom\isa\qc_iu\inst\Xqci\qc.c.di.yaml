# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.c.di
long_name: Disable interrupts
description: |
  Globally disable interrupts.
  Equivalent to "csrrci `zero`, `mstatus`, 8".
  Instruction encoded in CI instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqciint
assembly: ""
base: 32
encoding:
  match: "0001101100010010"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  CSR[mstatus].MIE = 0;
  XReg pre_qc_mcause = CSR[qc.mcause].sw_read();
  CSR[qc.mcause].sw_write(pre_qc_mcause & ~(32'b1<<26));
