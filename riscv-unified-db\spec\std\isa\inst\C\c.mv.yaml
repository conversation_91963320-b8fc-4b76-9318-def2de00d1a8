# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.mv
long_name: Move Register
description: |
  C.MV (move register) performs copy of the data in register xs2 to register xd
  C.MV expands to addi xd, x0, xs2.
definedBy:
  anyOf:
    - C
    - Zca
assembly: xd, xs2
encoding:
  match: 1000----------10
  variables:
    - name: xd
      location: 11-7
      not: 0
    - name: xs2
      location: 6-2
      not: 0
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  X[xd] = X[xs2];

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let xs2_val = X(xs2);
    X(rs) = xs2_val
    RETIRE_SUCCESS
  }

# SPDX-SnippetEnd
