# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Sscofpmf
long_name: Counter Overflow and Privilege Mode Filtering
description: Counter Overflow and Privilege Mode Filtering
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-08
    url: https://drive.google.com/file/d/1KcjgbLM5L1ZKY8934aJl8aQwGlMz6Cbo/view?usp=drive_link
    requires:
      name: Smhpm
interrupt_codes:
  - num: 13
    name: Local counter overflow interrupt
    var: LocalCounterOverflow
