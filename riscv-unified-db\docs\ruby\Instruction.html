<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Instruction
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Instruction";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (I)</a> &raquo;
    
    
    <span class="title">Instruction</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Instruction
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></li>
          
            <li class="next">Instruction</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>model of a specific instruction in a specific base (RV32/RV64)</p>


  </div>
</div>
<div class="tags">
  

</div><h2>Defined Under Namespace</h2>
<p class="children">
  
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="Instruction/Encoding.html" title="Instruction::Encoding (class)">Encoding</a></span>
    
  
</p>




  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#arch_def-instance_method" title="#arch_def (instance method)">#<strong>arch_def</strong>  &#x21d2; ArchDef </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The architecture definition.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#access_detail%3F-instance_method" title="#access_detail? (instance method)">#<strong>access_detail?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>True if the instruction has an ‘access_detail’ field.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#decode_variables-instance_method" title="#decode_variables (instance method)">#<strong>decode_variables</strong>(base)  &#x21d2; Array&lt;DecodeVariable&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The decode variables.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#defined_by%3F-instance_method" title="#defined_by? (instance method)">#<strong>defined_by?</strong>(*args)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#encoding-instance_method" title="#encoding (instance method)">#<strong>encoding</strong>(base)  &#x21d2; Encoding </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The encoding.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#encoding_width-instance_method" title="#encoding_width (instance method)">#<strong>encoding_width</strong>  &#x21d2; Integer </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The width of the encoding.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#excluded_by%3F-instance_method" title="#excluded_by? (instance method)">#<strong>excluded_by?</strong>(*args)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'></div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#exists_in_cfg%3F-instance_method" title="#exists_in_design? (instance method)">#<strong>exists_in_design?</strong>(possible_xlens, extensions)  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not the instruction is implemented given the supplies config options.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extension_exclusions-instance_method" title="#extension_exclusions (instance method)">#<strong>extension_exclusions</strong>  &#x21d2; Array&lt;ExtensionRequirement&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Extension exclusions for the instruction.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#extension_requirements-instance_method" title="#extension_requirements (instance method)">#<strong>extension_requirements</strong>  &#x21d2; Array&lt;ExtensionRequirement&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Extension requirements for the instruction.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#multi_encoding%3F-instance_method" title="#multi_encoding? (instance method)">#<strong>multi_encoding?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this instruction has different encodings depending on XLEN.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#operation_ast-instance_method" title="#operation_ast (instance method)">#<strong>operation_ast</strong>(idl_compiler)  &#x21d2; FunctionBodyAst </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The abstract syntax tree of the instruction operation.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#pruned_operation_ast-instance_method" title="#pruned_operation_ast (instance method)">#<strong>pruned_operation_ast</strong>(global_symtab)  &#x21d2; Idl::FunctionBodyAst </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A pruned abstract syntax tree.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#rv32%3F-instance_method" title="#rv32? (instance method)">#<strong>rv32?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this instruction is defined for RV32.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#rv64%3F-instance_method" title="#rv64? (instance method)">#<strong>rv64?</strong>  &#x21d2; Boolean </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Whether or not this instruction is defined for RV64.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#type_check_operation-instance_method" title="#type_check_operation (instance method)">#<strong>type_check_operation</strong>(global_symtab)  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>type check the instruction operation in the context of symtab.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#wavedrom_desc-instance_method" title="#wavedrom_desc (instance method)">#<strong>wavedrom_desc</strong>(base)  &#x21d2; String </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Generates a wavedrom description of the instruction encoding.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <p class="notice">This class inherits a constructor from <span class='object_link'><a href="ArchDefObject.html#initialize-instance_method" title="ArchDefObject#initialize (method)">ArchDefObject</a></span></p>
  
</div>
<div id="method_missing_details" class="method_details_list">
  <h2>Dynamic Method Handling</h2>
  <p class="notice super">
    This class handles dynamic methods through the <tt>method_missing</tt> method
    
      in the class <span class='object_link'><a href="ArchDefObject.html#method_missing-instance_method" title="ArchDefObject#method_missing (method)">ArchDefObject</a></span>
    
  </p>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="arch_def-instance_method">
  
    #<strong>arch_def</strong>  &#x21d2; <tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The architecture definition.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The architecture definition</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


903
904
905</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 903</span>

<span class='kw'>def</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
  <span class='ivar'>@arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="access_detail?-instance_method">
  
    #<strong>access_detail?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns true if the instruction has an ‘access_detail’ field.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>true if the instruction has an ‘access_detail’ field</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1031
1032
1033</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1031</span>

<span class='kw'>def</span> <span class='id identifier rubyid_access_detail?'>access_detail?</span>
  <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>access_detail</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="decode_variables-instance_method">
  
    #<strong>decode_variables</strong>(base)  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="DecodeVariable.html" title="DecodeVariable (class)">DecodeVariable</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The decode variables.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="DecodeVariable.html" title="DecodeVariable (class)">DecodeVariable</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The decode variables</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1026
1027
1028</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1026</span>

<span class='kw'>def</span> <span class='id identifier rubyid_decode_variables'>decode_variables</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_decode_variables'>decode_variables</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="defined_by?-instance_method">
  
    
      <span class="overload">#<strong>defined_by?</strong>(ext_name, ext_version)  &#x21d2; <tt>Boolean</tt> </span>
    
      <span class="overload">#<strong>defined_by?</strong>(ext_version)  &#x21d2; <tt>Boolean</tt> </span>
    
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
  <p class="tag_title">Overloads:</p>
  <ul class="overload">
    
      
      <li class="overload_item">
        <span class="signature">#<strong>defined_by?</strong>(ext_name, ext_version)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the instruction is defined by extesion ‘ext`, version `version`.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An extension name</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>ext_version</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A specific extension version</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the instruction is defined by extesion ‘ext`, version `version`</p>
</div>
      
    </li>
  
</ul>

</div>
      </li>
    
      
      <li class="overload_item">
        <span class="signature">#<strong>defined_by?</strong>(ext_version)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the instruction is defined by ext_version.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_version</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An extension version</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the instruction is defined by ext_version</p>
</div>
      
    </li>
  
</ul>

</div>
      </li>
    
  </ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1134
1135
1136
1137
1138
1139
1140
1141
1142
1143
1144
1145
1146
1147
1148
1149</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1134</span>

<span class='kw'>def</span> <span class='id identifier rubyid_defined_by?'>defined_by?</span><span class='lparen'>(</span><span class='op'>*</span><span class='id identifier rubyid_args'>args</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_args'>args</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Parameter must be an ExtensionVersion</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></span><span class='rparen'>)</span>

    <span class='id identifier rubyid_extension_requirements'>extension_requirements</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
      <span class='id identifier rubyid_r'>r</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>elsif</span> <span class='id identifier rubyid_args'>args</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>2</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>First parameter must be an extension name</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_respond_to?'>respond_to?</span><span class='lparen'>(</span><span class='symbol'>:to_s</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Second parameter must be an extension version</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_respond_to?'>respond_to?</span><span class='lparen'>(</span><span class='symbol'>:to_s</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_extension_requirements'>extension_requirements</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
      <span class='id identifier rubyid_r'>r</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='comma'>,</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="encoding-instance_method">
  
    #<strong>encoding</strong>(base)  &#x21d2; <tt><span class='object_link'><a href="Instruction/Encoding.html" title="Instruction::Encoding (class)">Encoding</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the encoding.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>base</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>32 or 64</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Instruction/Encoding.html" title="Instruction::Encoding (class)">Encoding</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the encoding</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1012
1013
1014
1015
1016</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1012</span>

<span class='kw'>def</span> <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_load_encoding'>load_encoding</span> <span class='kw'>if</span> <span class='ivar'>@encodings</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@encodings</span><span class='lbracket'>[</span><span class='id identifier rubyid_base'>base</span><span class='rbracket'>]</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="encoding_width-instance_method">
  
    #<strong>encoding_width</strong>  &#x21d2; <tt>Integer</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns the width of the encoding.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>the width of the encoding</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1019
1020
1021
1022
1023</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1019</span>

<span class='kw'>def</span> <span class='id identifier rubyid_encoding_width'>encoding_width</span>
  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected: encodings are different sizes</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='int'>32</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='int'>64</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>

  <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='int'>64</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="excluded_by?-instance_method">
  
    
      <span class="overload">#<strong>excluded_by?</strong>(ext_name, ext_version)  &#x21d2; <tt>Boolean</tt> </span>
    
      <span class="overload">#<strong>excluded_by?</strong>(ext_version)  &#x21d2; <tt>Boolean</tt> </span>
    
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    

  </div>
</div>
<div class="tags">
  
  <p class="tag_title">Overloads:</p>
  <ul class="overload">
    
      
      <li class="overload_item">
        <span class="signature">#<strong>excluded_by?</strong>(ext_name, ext_version)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the instruction is excluded by extesion ‘ext`, version `version`.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_name</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An extension name</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>ext_version</span>
      
      
        <span class='type'>(<tt>#to_s</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A specific extension version</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the instruction is excluded by extesion ‘ext`, version `version`</p>
</div>
      
    </li>
  
</ul>

</div>
      </li>
    
      
      <li class="overload_item">
        <span class="signature">#<strong>excluded_by?</strong>(ext_version)  &#x21d2; <tt>Boolean</tt> </span>
        <div class="docstring">
  <div class="discussion">
    
<p>Returns Whether or not the instruction is excluded by ext_version.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_version</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>An extension version</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Whether or not the instruction is excluded by ext_version</p>
</div>
      
    </li>
  
</ul>

</div>
      </li>
    
  </ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1158
1159
1160
1161
1162
1163
1164
1165
1166
1167
1168
1169
1170
1171
1172
1173</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1158</span>

<span class='kw'>def</span> <span class='id identifier rubyid_excluded_by?'>excluded_by?</span><span class='lparen'>(</span><span class='op'>*</span><span class='id identifier rubyid_args'>args</span><span class='rparen'>)</span>
  <span class='kw'>if</span> <span class='id identifier rubyid_args'>args</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>1</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Parameter must be an ExtensionVersion</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></span><span class='rparen'>)</span>

    <span class='id identifier rubyid_extension_exclusions'>extension_exclusions</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
      <span class='id identifier rubyid_r'>r</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>elsif</span> <span class='id identifier rubyid_args'>args</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span> <span class='op'>==</span> <span class='int'>2</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>First parameter must be an extension name</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_respond_to?'>respond_to?</span><span class='lparen'>(</span><span class='symbol'>:to_s</span><span class='rparen'>)</span>
    <span class='id identifier rubyid_raise'>raise</span> <span class='const'>ArgumentError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Second parameter must be an extension version</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_respond_to?'>respond_to?</span><span class='lparen'>(</span><span class='symbol'>:to_s</span><span class='rparen'>)</span>

    <span class='id identifier rubyid_extension_exclusions'>extension_exclusions</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
      <span class='id identifier rubyid_r'>r</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='comma'>,</span> <span class='id identifier rubyid_args'>args</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_to_s'>to_s</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="exists_in_design?-instance_method">
  
    #<strong>exists_in_design?</strong>(possible_xlens, extensions)  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not the instruction is implemented given the supplies config options.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>possible_xlens</span>
      
      
        <span class='type'>(<tt>Array&lt;Integer&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of xlens that be used in any implemented mode</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>extensions</span>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>List of extensions implemented</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not the instruction is implemented given the supplies config options</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1178
1179
1180
1181
1182</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1178</span>

<span class='kw'>def</span> <span class='id identifier rubyid_exists_in_cfg?'>exists_in_design?</span><span class='lparen'>(</span><span class='id identifier rubyid_possible_xlens'>possible_xlens</span><span class='comma'>,</span> <span class='id identifier rubyid_extensions'>extensions</span><span class='rparen'>)</span>
  <span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span> <span class='op'>||</span> <span class='lparen'>(</span><span class='id identifier rubyid_possible_xlens'>possible_xlens</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rparen'>)</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span>
    <span class='id identifier rubyid_extensions'>extensions</span><span class='period'>.</span><span class='id identifier rubyid_any?'>any?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='id identifier rubyid_defined_by?'>defined_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='rparen'>)</span> <span class='rbrace'>}</span> <span class='op'>&amp;&amp;</span>
    <span class='id identifier rubyid_extensions'>extensions</span><span class='period'>.</span><span class='id identifier rubyid_none?'>none?</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='id identifier rubyid_excluded_by?'>excluded_by?</span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extension_exclusions-instance_method">
  
    #<strong>extension_exclusions</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="ExtensionRequirement.html" title="ExtensionRequirement (class)">ExtensionRequirement</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Extension exclusions for the instruction. If <strong>any</strong> exclusion is met, the instruction is not defined.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="ExtensionRequirement.html" title="ExtensionRequirement (class)">ExtensionRequirement</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension exclusions for the instruction. If <strong>any</strong> exclusion is met, the instruction is not defined</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1104
1105
1106
1107
1108
1109
1110
1111
1112
1113
1114
1115
1116
1117
1118
1119
1120
1121
1122
1123
1124
1125</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1104</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extension_exclusions'>extension_exclusions</span>
  <span class='kw'>return</span> <span class='ivar'>@extension_exclusions</span> <span class='kw'>unless</span> <span class='ivar'>@extension_excludions</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@extension_exclusions</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='kw'>if</span> <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>excludedBy</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
    <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>exludedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
      <span class='comment'># could be either a single extension with exclusion, or a list of exclusions
</span>      <span class='kw'>if</span> <span class='id identifier rubyid_extension_exclusion?'>extension_exclusion?</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
        <span class='ivar'>@extension_exclusions</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_requirement'>to_extension_requirement</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>excludedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
      <span class='kw'>else</span>
        <span class='comment'># this is a list
</span>        <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>excludeddBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
          <span class='ivar'>@extension_exclusions</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_exclusion'>to_extension_exclusion</span><span class='lparen'>(</span><span class='id identifier rubyid_r'>r</span><span class='rparen'>)</span>
        <span class='kw'>end</span>
      <span class='kw'>end</span>
    <span class='kw'>else</span>
      <span class='ivar'>@extension_exclusions</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_requirement'>to_extension_requirement</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>excludedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>

  <span class='ivar'>@extension_exclusions</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="extension_requirements-instance_method">
  
    #<strong>extension_requirements</strong>  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="ExtensionRequirement.html" title="ExtensionRequirement (class)">ExtensionRequirement</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Extension requirements for the instruction. If <strong>any</strong> requirement is met, the instruction is defined.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="ExtensionRequirement.html" title="ExtensionRequirement (class)">ExtensionRequirement</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Extension requirements for the instruction. If <strong>any</strong> requirement is met, the instruction is defined</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1080
1081
1082
1083
1084
1085
1086
1087
1088
1089
1090
1091
1092
1093
1094
1095
1096
1097
1098
1099
1100
1101</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1080</span>

<span class='kw'>def</span> <span class='id identifier rubyid_extension_requirements'>extension_requirements</span>
  <span class='kw'>return</span> <span class='ivar'>@extension_requirements</span> <span class='kw'>unless</span> <span class='ivar'>@extension_requirements</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='ivar'>@extension_requirements</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
    <span class='comment'># could be either a single extension with requirement, or a list of requirements
</span>    <span class='kw'>if</span> <span class='id identifier rubyid_extension_requirement?'>extension_requirement?</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
      <span class='ivar'>@extension_requirements</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_requirement'>to_extension_requirement</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='rparen'>)</span>
    <span class='kw'>else</span>
      <span class='comment'># this is a list
</span>      <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_r'>r</span><span class='op'>|</span>
        <span class='ivar'>@extension_requirements</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_requirement'>to_extension_requirement</span><span class='lparen'>(</span><span class='id identifier rubyid_r'>r</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
  <span class='kw'>else</span>
    <span class='ivar'>@extension_requirements</span> <span class='op'>&lt;&lt;</span> <span class='id identifier rubyid_to_extension_requirement'>to_extension_requirement</span><span class='lparen'>(</span><span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>definedBy</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>empty requirements</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>if</span> <span class='ivar'>@extension_requirements</span><span class='period'>.</span><span class='id identifier rubyid_empty?'>empty?</span>

  <span class='ivar'>@extension_requirements</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="multi_encoding?-instance_method">
  
    #<strong>multi_encoding?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this instruction has different encodings depending on XLEN.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this instruction has different encodings depending on XLEN</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


989
990
991</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 989</span>

<span class='kw'>def</span> <span class='id identifier rubyid_multi_encoding?'>multi_encoding?</span>
  <span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>encoding</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>RV32</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="operation_ast-instance_method">
  
    #<strong>operation_ast</strong>(idl_compiler)  &#x21d2; <tt>FunctionBodyAst</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The abstract syntax tree of the instruction operation.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>FunctionBodyAst</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The abstract syntax tree of the instruction operation</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


***********
***********
1000
1001
1002
1003
1004
1005
1006
1007
1008</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 994</span>

<span class='kw'>def</span> <span class='id identifier rubyid_operation_ast'>operation_ast</span><span class='lparen'>(</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='rparen'>)</span>
  <span class='kw'>return</span> <span class='ivar'>@operation_ast</span> <span class='kw'>unless</span> <span class='ivar'>@operation_ast</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>
  <span class='kw'>return</span> <span class='kw'>nil</span> <span class='kw'>if</span> <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>operation()</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_nil?'>nil?</span>

  <span class='comment'># now, parse the operation
</span>
  <span class='ivar'>@operation_ast</span> <span class='op'>=</span> <span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='period'>.</span><span class='id identifier rubyid_compile_inst_operation'>compile_inst_operation</span><span class='lparen'>(</span>
    <span class='kw'>self</span><span class='comma'>,</span>
    <span class='label'>input_file:</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>Instruction </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='rparen'>)</span>

  <span class='id identifier rubyid_raise'>raise</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>unexpected </span><span class='embexpr_beg'>#{</span><span class='ivar'>@operation_ast</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span> <span class='kw'>unless</span> <span class='ivar'>@operation_ast</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'><span class='object_link'><a href="Idl.html" title="Idl (module)">Idl</a></span></span><span class='op'>::</span><span class='const'><span class='object_link'><a href="Idl/FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">FunctionBodyAst</a></span></span><span class='rparen'>)</span>

  <span class='ivar'>@operation_ast</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="pruned_operation_ast-instance_method">
  
    #<strong>pruned_operation_ast</strong>(global_symtab)  &#x21d2; <tt><span class='object_link'><a href="Idl/FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">Idl::FunctionBodyAst</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns A pruned abstract syntax tree.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>global_symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Symbol table with global scope populated and a configuration loaded</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">Idl::FunctionBodyAst</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A pruned abstract syntax tree</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


***********
899
900</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 896</span>

<span class='kw'>def</span> <span class='id identifier rubyid_pruned_operation_ast'>pruned_operation_ast</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_type_check_operation'>type_check_operation</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_puts'>puts</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>PRUNING        </span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_end'>&quot;</span></span>
  <span class='id identifier rubyid_operation_ast'>operation_ast</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_prune'>prune</span><span class='lparen'>(</span><span class='id identifier rubyid_fill_symtab'>fill_symtab</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='rparen'>)</span><span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="rv32?-instance_method">
  
    #<strong>rv32?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this instruction is defined for RV32.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this instruction is defined for RV32</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1055
1056
1057</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1055</span>

<span class='kw'>def</span> <span class='id identifier rubyid_rv32?'>rv32?</span>
  <span class='op'>!</span><span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_base'>base</span> <span class='op'>==</span> <span class='int'>32</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="rv64?-instance_method">
  
    #<strong>rv64?</strong>  &#x21d2; <tt>Boolean</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns whether or not this instruction is defined for RV64.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Boolean</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>whether or not this instruction is defined for RV64</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1060
1061
1062</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1060</span>

<span class='kw'>def</span> <span class='id identifier rubyid_rv64?'>rv64?</span>
  <span class='op'>!</span><span class='ivar'>@data</span><span class='period'>.</span><span class='id identifier rubyid_key?'>key?</span><span class='lparen'>(</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>base</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span> <span class='op'>||</span> <span class='id identifier rubyid_base'>base</span> <span class='op'>==</span> <span class='int'>64</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="type_check_operation-instance_method">
  
    #<strong>type_check_operation</strong>(global_symtab)  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>type check the instruction operation in the context of symtab</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>global_symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">Idl::SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>A symbol table with global scope populated</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>Idl::AstNode::TypeError if there is a type problem</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


886
887
888
889
890
891
892</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 886</span>

<span class='kw'>def</span> <span class='id identifier rubyid_type_check_operation'>type_check_operation</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='period'>.</span><span class='id identifier rubyid_type_check'>type_check</span><span class='lparen'>(</span>
    <span class='id identifier rubyid_operation_ast'>operation_ast</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='period'>.</span><span class='id identifier rubyid_archdef'>archdef</span><span class='period'>.</span><span class='id identifier rubyid_idl_compiler'>idl_compiler</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='id identifier rubyid_fill_symtab'>fill_symtab</span><span class='lparen'>(</span><span class='id identifier rubyid_global_symtab'>global_symtab</span><span class='rparen'>)</span><span class='comma'>,</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'>.operation()</span><span class='tstring_end'>&quot;</span></span>
  <span class='rparen'>)</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="wavedrom_desc-instance_method">
  
    #<strong>wavedrom_desc</strong>(base)  &#x21d2; <tt>String</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Generates a wavedrom description of the instruction encoding</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>base</span>
      
      
        <span class='type'>(<tt>Integer</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The XLEN (32 or 64), needed if the instruction is <span class='object_link'><a href="#multi_encoding%3F-instance_method" title="Instruction#multi_encoding? (method)">#multi_encoding?</a></span></p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The wavedrom JSON description</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1039
1040
1041
1042
1043
1044
1045
1046
1047
1048
1049
1050
1051
1052</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1039</span>

<span class='kw'>def</span> <span class='id identifier rubyid_wavedrom_desc'>wavedrom_desc</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span>
  <span class='id identifier rubyid_desc'>desc</span> <span class='op'>=</span> <span class='lbrace'>{</span>
    <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='rbrace'>}</span>

  <span class='id identifier rubyid_display_fields'>display_fields</span> <span class='op'>=</span> <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_opcode_fields'>opcode_fields</span>
  <span class='id identifier rubyid_display_fields'>display_fields</span> <span class='op'>+=</span> <span class='id identifier rubyid_encoding'>encoding</span><span class='lparen'>(</span><span class='id identifier rubyid_base'>base</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_decode_variables'>decode_variables</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span><span class='lparen'>(</span><span class='op'>&amp;</span><span class='symbol'>:grouped_encoding_fields</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_flatten'>flatten</span>

  <span class='id identifier rubyid_display_fields'>display_fields</span><span class='period'>.</span><span class='id identifier rubyid_sort'>sort</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_a'>a</span><span class='comma'>,</span> <span class='id identifier rubyid_b'>b</span><span class='op'>|</span> <span class='id identifier rubyid_b'>b</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='op'>&lt;=&gt;</span> <span class='id identifier rubyid_a'>a</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_last'>last</span> <span class='rbrace'>}</span><span class='period'>.</span><span class='id identifier rubyid_reverse'>reverse</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span>
    <span class='id identifier rubyid_desc'>desc</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>reg</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span> <span class='op'>&lt;&lt;</span> <span class='lbrace'>{</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>bits</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_range'>range</span><span class='period'>.</span><span class='id identifier rubyid_size'>size</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>name</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>type</span><span class='tstring_end'>&quot;</span></span> <span class='op'>=&gt;</span> <span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='period'>.</span><span class='id identifier rubyid_opcode?'>opcode?</span> <span class='op'>?</span> <span class='int'>2</span> <span class='op'>:</span> <span class='int'>4</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
  <span class='kw'>end</span>

  <span class='id identifier rubyid_desc'>desc</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>