# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/Zihpm/mhpmcounterN.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: mhpmcounter31
long_name: Machine Hardware Performance Counter 31
address: 0xB1F
priv_mode: M
length: 64
description: Programmable hardware performance counter.
definedBy: Smhpm
fields:
  COUNT:
    location: 63-0
    description: |
      [when="HPM_COUNTER_EN[31] == true"]
      --
      Performance counter for event selected in `mhpmevent31.EVENT`.

      Increments every time event occurs unless:

        * `mcountinhibit.HPM31` <%- if ext?(:Smcdeleg) -%>or its alias `scountinhibit.HPM31`<%- end -%> is set
        <%- if ext?(:Sscofpmf) -%>
        * `mhpmevent31.MINH` is set and the current privilege level is M
        <%- if ext?(:S) -%>
        * `mhpmevent31.SINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent31..SINH`<%- end -%> is set and the current privilege level is (H)S
        <%- end -%>
        <%- if ext?(:U) -%>
        * `mhpmevent31.UINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent31.SINH`<%- end -%> is set and the current privilege level is U
        <%- end -%>
        <%- if ext?(:H) -%>
        * `mhpmevent31.VSINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent31.SINH`<%- end -%> is set and the current privilege level is VS
        * `mhpmevent31.VUINH` <%- if ext?(:Ssccfg) -%>or its alias `hpmevent31.SINH`<%- end -%> is set and the current privilege level is VU
        <%- end -%>
        <%- end -%>
      --

      [when="HPM_COUNTER_EN[31] == false"]
      Unimplemented performance counter. Must be read-only 0 (access does not cause trap).

    type(): "return (HPM_COUNTER_EN[31]) ? CsrFieldType::RWH : CsrFieldType::RO;"
    reset_value(): "return (HPM_COUNTER_EN[31]) ? UNDEFINED_LEGAL : 0;"
sw_read(): |
  if (HPM_COUNTER_EN[31]) {
    return read_hpm_counter(31);
  } else {
    return 0;
  }
