[ext:$<%= ext.name %>-def]
= <%= ext.name %> Extension
<%= ext.long_name %>

Implemented Version:: <%= ext_version.version_str %>

== Versions

<%- ext.versions.each do |v| -%>
<%- implemented = cfg_arch.transitive_implemented_extension_versions.include?(v) -%>
<%= v.version_str %>::
  Ratification date:::
    <%= v.ratification_date %>
  <%- unless v.changes.empty? -%>
  Changes:::

    <% v.changes.each do |c| -%>
    * <%= c %>
    <% end -%>

  <%- end -%>
  <%- unless v.url.nil? -%>
  Ratification document:::
    <%= v.url %>
  <%- end -%>
  <%- unless v.implications.empty? -%>
  Implies:::
    <%- v.implications.each do |i| -%>
    <%-   next unless i.cond.satisfied_by? { |ext_req| cfg_arch.transitive_implemented_extension_versions.any? { |ext_ver| ext_req.satisfied_by?(ext_ver)}} -%>
    * `<%= i.ext_ver.name %>` version <%= i.ext_ver.version_str %>
    <%- end -%>
  <%- end -%>
<%- end -%>

== Synopsis

<%= ext.description %>

<%- insts = ext.instructions -%>
<%- unless insts.empty? -%>
== Instructions

The following instructions are added by this extension in the <%= cfg_arch.name %> configuration:

[cols="1,3"]
|===
<%- insts.each do |inst| -%>
 | <%= "`#{inst.name}`" %> | *<%= inst.long_name %>*
<%- end -%>
|===
<%- end -%>

<%- unless ext.params.empty? -%>
== Parameters

This extension has the following implementation options:

<%- ext.params.sort_by { |p| p.name }.each do |param| -%>
<%= param.name %>::
+
--
<%= param.desc %>
--
<%- end -%>

<%- end -%>
