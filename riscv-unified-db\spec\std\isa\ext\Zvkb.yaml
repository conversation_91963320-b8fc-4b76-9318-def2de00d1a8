# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvkb
long_name: Vector Cryptography Bit-manipulation
description: |
  Vector bit-manipulation instructions that are essential for implementing common cryptographic workloads securely & efficiently.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
