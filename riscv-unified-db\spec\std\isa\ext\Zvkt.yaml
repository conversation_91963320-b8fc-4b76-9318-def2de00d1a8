# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvkt
long_name: Vector Data-Independent Execution Latency
description: |
  The Zvkt extension requires all implemented instructions from the following list to be executed
  with data-independent execution latency.

  Data-independent execution latency (DIEL) applies to all data operands of an instruction, even
  those that are not a part of the body or that are inactive.
  However, DIEL does not apply to other values such as vl, vtype, and the mask (when used to control
  execution of a masked vector instruction).
  Also, DIEL does not apply to constant values specified in the instruction encoding such as the
  use of the zero register (x0), and, in the case of immediate forms of an instruction, the values
  in the immediate fields (i.e., imm, and uimm).

  In some cases --- which are explicitly specified in the lists below --- operands that are used as
  control rather than data are exempt from DIEL.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
