# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hstateen0
long_name: Hypervisor State Enable 0 Register
address: 0x60C
priv_mode: S
length: 64
description:
  - id: csr-hstateen0-purpose
    normative: true
    text: |
      Each bit of a `stateen` CSR controls less-privileged access to an extension’s state,
      for an extension that was not deemed "worthy" of a full XS field in `sstatus` like the
      FS and VS fields for the F and V extensions.
  - id: csr-hstateen0-num-justification
    normative: false
    text: |
      The number of registers provided at each level is four because it is believed that
      4 * 64 = 256 bits for machine and hypervisor levels, and 4 * 32 = 128 bits for
      supervisor level, will be adequate for many years to come, perhaps for as long as
      the RISC-V ISA is in use.
      The exact number four is an attempted compromise between providing too few bits on
      the one hand and going overboard with CSRs that will never be used on the other.
  - id: csr-hstateen0-scope
    normative: true
    text: |
      The `stateen` registers at each level control access to state at all less-privileged
      levels, but not at its own level.
  - id: csr-hstateen0-effect
    normative: true
    text: |
      When a `stateen` CSR prevents access to state for a privilege mode, attempting to execute
      in that privilege mode an instruction that implicitly updates the state without reading
      it may or may not raise an illegal instruction or virtual instruction exception.
      Such cases must be disambiguated by being explicitly specified one way or the other.
      In some cases, the bits of the `stateen` CSRs will have a dual purpose as enables for the
      ISA extensions that introduce the controlled state.
  - id: csr-hstateen0-encodings
    normative: true
    text: |
      With the hypervisor extension, the `hstateen` CSRs have identical encodings to the `mstateen` CSRs,
      except controlling accesses for a virtual machine (from VS and VU modes).
  - id: csr-hstateen0-zero
    normative: true
    text: |
      For every bit in an `hstateen` CSR that is zero (whether read-only zero or set to zero),
      the same bit appears as read-only zero in `sstateen` when accessed in VS-mode.
  - id: csr-hstateen0-read-only
    normative: true
    text: |
      A bit in an `hstateen` CSR cannot be read-only one unless the same bit is read-only one
      in the matching `mstateen` CSR.

definedBy:
  allOf:
    - H
    - Smstateen
    - Ssstateen
fields:
  SE0:
    long_name: sstateen0 access control
    location: 63
    description: |
      The SE0 bit in `hstateen0` controls access to the `sstateen0` CSR.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].SE0 == 1'b0){
        return 0;
      }
      return csr_value.SE0;
  ENVCFG:
    long_name: senvcfg access control
    location: 62
    definedBy:
      name: S
      version: ">= 1.11"
    description: |
      The ENVCFG bit in `hstateen0` controls access to the `senvcfg` CSRs.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].ENVCFG == 1'b0){
        return 0;
      }
      return csr_value.ENVCFG;
  CSRIND:
    long_name: siselect and sireg* access control
    location: 60
    definedBy: Sscsrind
    description: |
      The CSRIND bit in `hstateen0` controls access to the `siselect` and the
      `sireg*`, (really `vsiselect` and `vsireg*`) CSRs provided by the Sscsrind
      extensions.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].CSRIND == 1'b0){
        return 0;
      }
      return csr_value.CSRIND;
  AIA:
    long_name: Ssaia state access control
    location: 59
    definedBy: Ssaia
    description: |
      The AIA bit in `hstateen0` controls access to all state introduced by
      the Ssaia extension and is not controlled by either the CSRIND or the
      IMSIC bits of `hstateen0`.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].AIA == 1'b0){
        return 0;
      }
      return csr_value.AIA;
  IMSIC:
    long_name: IMSIC state access control
    location: 58
    definedBy: Ssaia
    description: |
      The IMSIC bit in `hstateen0` controls access to the guest IMSIC state,
      including CSRs `stopei` (really `vstopei`), provided by the Ssaia extension.

      Setting the IMSIC bit in `hstateen0` to zero prevents a virtual machine
      from accessing the hart’s IMSIC the same as setting `hstatus.`VGEIN = 0.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].IMSIC == 1'b0){
        return 0;
      }
      return csr_value.IMSIC;
  CONTEXT:
    long_name: scontext access control
    location: 57
    definedBy: Sdtrig
    description: |
      The CONTEXT bit in `hstateen0` controls access to the `scontext` CSR provided
      by the Sdtrig extension.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].CONTEXT == 1'b0){
        return 0;
      }
      return csr_value.CONTEXT;
  CTR:
    long_name: ctr access control
    location: 54
    description: |
      If the H extension is implemented and `mstateen0.CTR=1`, the `hstateen0.CTR` bit controls access to
      supervisor CTR state when V=1. This state includes `sctrctl` (really `vsctrctl`), `sctrstatus`, and `sireg*`
      (really `vsireg*`) when `siselect` (really `vsiselect`) is in 0x200..0x2FF. `hstateen0.CTR` is read-only 0 when
      `mstateen0.CTR=0`.
    type: RW
    reset_value: 0
    sw_write(csr_value): |
      if (CSR[mstateen0].CTR == 1'b0){
        return 0;
      }
      return csr_value.CTR;
  JVT:
    long_name: jvt access control
    location: 2
    definedBy: Zcmt
    description: |
      The JVT bit controls access to the `jvt` CSR provided by the Zcmt extension.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].JVT == 1'b0){
        return 0;
      }
      return csr_value.JVT;
  FCSR:
    long_name: fcsr access control
    location: 1
    #definedBy:
    #anyOf: [Zhinx, Zfinx, Zdinx]
    description: |
      The FCSR bit controls access to `fcsr` for the case when floating-point instructions
      operate on `x` registers instead of `f` registers as specified by the Zfinx and related
      extensions (Zdinx, etc.). Whenever `misa.F` = 1, FCSR bit of `mstateen0` is read-only
      zero (and hence read-only zero in `hstateen0` and `sstateen0` too). For convenience,
      when the `stateen` CSRs are implemented and `misa.F` = 0, then if the FCSR bit of a
      controlling `stateen0` CSR is zero, all floating-point instructions cause an illegal
      instruction trap (or virtual instruction trap, if relevant), as though they all access
      `fcsr`, regardless of whether they really do.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].FCSR == 1'b0){
        return 0;
      }
      return csr_value.FCSR;
  C:
    long_name: custom state access control
    location: 0
    description: |
      The C bit controls access to any and all custom state. The C bit of these registers is
      not custom state itself; it is a standard field of a standard CSR, either `mstateen0`,
      `hstateen0`, or `sstateen0`.
    type: RW
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      if (CSR[mstateen0].C == 1'b0){
        return 0;
      }
      return csr_value.C;
sw_read(): |
  # for every bit in an mstateen CSR that is zero, the same bit
  # appears as read-only zero in the matching hstateen CSR

  Bits<64> mstateen0_mask = $bits(CSR[mstateen0]);
  Bits<64> hstateen0_value = $bits(CSR[hstateen0]) & mstateen0_mask;
  return hstateen0_value;
