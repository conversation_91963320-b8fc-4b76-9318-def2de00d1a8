# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/ext_schema.json

$schema: ext_schema.json#
kind: extension
name: Xqciac
type: unprivileged
long_name: Qualcomm address calculation
versions:
- version: "0.1.0"
  state: development
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
- version: "0.2.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Add information about instruction formats of each instruction
    - Fix description and functionality of qc.shladd instruction
  requires: { name: <PERSON><PERSON>, version: ">= 1.0.0" }
- version: "0.3.0"
  state: frozen
  ratification_date: null
  contributors:
  - name: <PERSON>
    company: Qualcomm Technologies, Inc.
    email: <EMAIL><PERSON>m.com
  - name: <PERSON>r
    company: Qualcomm Technologies, Inc.
    email: <EMAIL>
  changes:
    - Fix description and functionality of qc.shladd instruction
    - Renaming instructions qc.muladdi to qc.muliadd and qc.c.muladdi to qc.c.muliadd
  requires: { name: Zca, version: ">= 1.0.0" }
description: |
  The Xqciac extension includes three instructions to accelerate common
  address calculations.
conflicts:
  anyOf:
    - allOf: [C, D]
    - Zcd
doc_license:
  name: Creative Commons Attribution 4.0 International License
  url: https://creativecommons.org/licenses/by/4.0/
company:
  name: Qualcomm Technologies, Inc.
  url: https://qualcomm.com
