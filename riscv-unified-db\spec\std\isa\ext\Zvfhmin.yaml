# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvfhmin
long_name: Vector Extension for Minimal Half-Precision Floating-Point
description: |
  This extension provides minimal support for vectors of IEEE 754-2008
  binary16 values, adding conversions to and from binary32.
  When the Zvfhmin extension is implemented, the `vfwcvt.f.f.v` and
  `vfncvt.f.f.w` instructions become defined when SEW=16.
  The EEW=16 floating-point operands of these instructions use the binary16
  format.

  This extension depends on the Zve32f extension.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires: Zve32f
