# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zvfbfmin
long_name: Vector BF16 Converts
description: |
  This extension provides the minimal set of instructions needed to enable vector support of the
  BF16 format.
  It enables BF16 as an interchange format as it provides conversion between BF16 values and FP32 values.

  This extension depends upon either the `V` extension or the `Zve32f` embedded vector extension.
type: unprivileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
    requires:
      anyOf:
        - V
        - Zve32f
