<%= portfolio_design.include_erb("beginning.adoc.erb") %>

= <%= proc_cert_model.name %> Processor Certification Requirements Document

[Preface]
== CRD Revision History

<%= proc_cert_design.include_erb("rev_history.adoc.erb") %>

[Preface]
== Typographic Conventions

<%= proc_cert_design.include_erb("typographic.adoc.erb") %>

[Preface]
== Glossary

<%= proc_cert_design.include_erb("glossary.adoc.erb") %>

== Introduction

<%= proc_cert_model.introduction %>

<%= proc_cert_class.introduction %>

=== What's a CRD?

Certification Requirements Documents (CRDs) list requirements an implementation must meet
to obtain an associated RVI (RISC-V International) certificate.
CRDs are developed by the RVI CSC (Certification Steering Committee) organization in collaboration
with the RVI TSC (Technical Steering Committee) organization who creates RISC-V standards.

The CRDs refer to and augment information provided in existing ratified RVI standards.

There are a variety of certificates offered by RVI to accomodate the various RVI standards.
There are certificates for processors, non-processor system IP (e.g., IOMMU),
and system platforms (processor + system IP) hardware standards.
There are multiple classes of processor certificates available to accomodate the wide range of
RISC-V implementations from basic microcontrollers to advanced Applications-class processors.

Each CRD has a list of mandatory behaviors along with a list of optional behaviors.
Note that not all behaviors allowed in RISC-V standards are supported by a particular CRD.

=== Naming Scheme

==== CRD Naming

<%= proc_cert_design.include_erb("rvcp_naming_scheme.adoc.erb") %>

The specific rules for updating the version number of a CRD are as follows:

* The <major> release number is updated for changes that *could* cause a previously certified
   implementation to no longer meet requirements. An example is requiring a new version of a standard.
* The <minor> release number is updated for increased support of optional behaviors.
* The <patch> release number is updated for documentation fixes/improvements.
  These changes *cannot* cause a previously certified implementation to no longer meet requirements.

==== Processor Naming

<%= proc_cert_design.include_erb("proc_naming_scheme.adoc.erb") %>

=== Requirements Terminology

.Requirement Types
[%autowidth]
|===
| Term | Meaning

| MANDATORY | You have to implement it to get a certificate and the certificate tests will cover it
| OPTIONAL | It's up to you if you implement or not. If you claim to implement it, certificate tests will cover it
| IN-SCOPE | Either MANDATORY or OPTIONAL
| OUT-OF-SCOPE | It's up to you if you implement or not. If you implement it, it won't be certified but make sure you don't mess up anything we are certifying.
| INCOMPATIBLE | If you implement it you won't get a certificate
|===

.Definition of CSR Fields
[%autowidth]
|===
| Field Type | Read Value After Writing Illegal Value | Read Value Function Of | Illegal Instruction Exception | Priv ISA Manual Quote

| WLRL | Any deterministic legal or illegal value | Value before write and illegal value written | Optional
| Implementations are permitted but not required to raise an illegal-instruction exception if an instruction attempts to write a non-supported value to a WLRL field. Implementations can return arbitrary bit patterns on the read of a WLRL field when the last write was of an illegal value, but the value returned should deterministically depend on the illegal written value and the value of the field prior to the write.
| WARL | Any deterministic legal value | Any architectural hart state | Prohibited
| Implementations will not raise an exception on writes of unsupported values to a WARL field. Implementations can return any legal value on the read of a WARL field when the last write was of an illegal value, but the legal value returned should deterministically depend on the illegal written value and the architectural state of the hart.
| WPRI | 0 | Nothing | Not specified
| Some whole read/write fields are reserved for future use. Implementations that do not furnish these fields must make them read-only zero.
|===

*WARL (Write Anything, Read Legal)*:

The Priv ISA requires reads of WARL fields to return some implementation-dependent deterministic legal value
after the field is written with an illegal value.
Certifying such behaviors is expensive and provides low value for a certificate since software can't rely
on a particular behavior from one implementation to another.

Processor CRDs define writes to WARL fields of illegal values to be OUT-OF-SCOPE unless otherwise stated
(i.e., certification tests will only ever write legal values to WARL fields except for the special cases listed below).
When not OUT-OF-SCOPE, the required behavior is defined as this might be more constrained in implementations than
in the standard.

The following special cases for WARL are supported when explicitly listed in the corresponding CRD CSR field requirements:

1. Probing for Field Width

* Some WARL fields are variable length such as the ASID field in the virtual memory extension.
* Here's the algorithm recommended to discover the ASID width:
** The number of implemented ASID bits, termed ASIDLEN, may be determined by writing one to every bit position in
   the ASID field, then reading back the value in the satp CSR to see which bit positions in the ASID field hold a one.
* The RVCP-provided certification materials (certification tests, certification reference models) can map writes of
  illegal values to the ASID field to the corresponding read value as long as they are provided the ASIDLEN value
  for an implementation.

2. Probing for Options

* E.g., Writable misa bits

3. Allowed values are a function of extension presence and/or their parameters

* E.g., satp.mode legal write values

*WLRL (Write Legal, Read Legal)*:

The Priv ISA requires reads of WLRL fields to return some implementation-dependent deterministic arbitrary value
after the field is written with an illegal value.
Certifying such behaviors is expensive and provides low value for a certificate since software can't rely
on a particular behavior.
Processor CRDs define writes to WLRL fields of illegal values to be OUT-OF-SCOPE unless otherwise stated
(i.e., certification tests will only ever write legal values to WLRL fields).

*WPRI (Write Preserve, Read Ignore)*:

The Priv ISA requires reads of WPRI fields to return a value of 0.
Such WPRI fields are always unimplemented by definition.
Certification tests are aware of which fields in the CSRs are WPRI and normally write them with 0 but will
also write them with ~0 (all ones) and ensure that reads return 0 in both cases.
It is OUT-OF-SCOPE for certification tests to write all possible values of WPRI fields
(especially if they are more than just a few bits) and certification tests aren't intended to be comprehensive
verification test suites anyways.

<%= proc_cert_design.include_erb("related_specs.adoc.erb") %>
<%= proc_cert_design.include_erb("priv_modes.adoc.erb") %>
<<<
== Extensions

Any RISC-V extensions not listed in this section are OUT-OF-SCOPE.
The <%= proc_cert_model.name %> certificate doesn't cover their behaviors.

<% Udb::Presence.presence_types_obj.each do |presence_obj| -%>

=== <%= presence_obj.to_s.capitalize %> Extensions

<% ext_reqs = proc_cert_model.in_scope_ext_reqs(presence_obj) -%>
<% if ext_reqs.empty? -%>
None
<% else -%>
The <%= proc_cert_model.name %> certificate has <%= ext_reqs.size %> <%= presence_obj.to_s %> extensions.

[%autowidth]
|===
| Requirement ID | Extension | Version | Long Name | Note

<% ext_reqs.each do |ext_req| -%>
<% ext = arch.extension(ext_req.name) -%>
| <%= ext_req.req_id %>
| <%= link_to_udb_doc_ext(ext_req.name) %>
| <%= ext_req.requirement_specs.map(&:to_s).join(", ") %>
| <%= ext.nil? ? "" : ext.long_name %>
| <%= ext_req.note.nil? ? "" : ext_req.note %>
<% end # each ext_req -%>
|===
<% end # if empty ext_reqs -%>

<% proc_cert_model.extra_notes_for_presence(presence_obj)&.each do |extra_note| -%>
NOTE: <%= extra_note.text %>

<% end # each extra_note -%>

<% end # each possible presence -%>

<% unless proc_cert_model.recommendations.empty? -%>
=== Recommendations

Recommendations are not strictly mandated but are included to guide implementers.

<% proc_cert_model.recommendations.each do |recommendation| -%>
<%= recommendation.text %>
<% end # each recommendation -%>
<% end # unless recommendations empty -%>

<<<
== Implementation-dependencies

RISC-V standards support many implementation-defined parameters. In many cases, there
are no names associated with these parameters. Names are defined in this section when
not provided in the associated standard.

=== IN-SCOPE Parameters

These implementation-dependent options defined by MANDATORY or OPTIONAL extensions are IN-SCOPE.
An implementation must abide by the "Allowed Value(s)" to obtain a certificate.
If the "Allowed Value(s)" is "Any" then any value allowed by the type is acceptable.

<% if portfolio_design.all_in_scope_params.empty? -%>
None
<% else -%>
The <%= proc_cert_model.name %> certificate has <%= portfolio_design.all_in_scope_params.size %> IN-SCOPE parameters.

[cols="4,2,1,1,2"]
|===
| Parameter | Type | Allowed Value(s) | Extension(s) | Note

<% portfolio_design.all_in_scope_params.each do |in_scope_param| -%>
<% param = in_scope_param.param -%>
<% in_scope_exts = portfolio_design.all_in_scope_exts_with_param(param) -%>
| <%= param.name_potentially_with_link(in_scope_exts) %>
| <%= param.schema_type %>
| <%= in_scope_param.allowed_values %>
| <%= in_scope_exts.map { |ext| link_to_udb_doc_ext_param(ext.name, param.name, ext.name) }.join(", ") %>
a| <%= in_scope_param.note %>
<% end # do -%>
|===
<% end # if table -%>

=== OUT-OF-SCOPE Parameters

These implementation-dependent options defined by MANDATORY or OPTIONAL extensions are OUT-OF-SCOPE.
There are no restrictions on their values for certification purposes because the certificate
doesn't cover the behavior of the associated RISC-V standard as a function of these parameters.

<% if portfolio_design.all_out_of_scope_params.empty? -%>
None
<% else -%>
The <%= proc_cert_model.name %> certificate has <%= portfolio_design.all_out_of_scope_params.size %> OUT-OF-SCOPE parameters.

[%autowidth]
|===
| Parameters | Type | Extension(s)

<% portfolio_design.all_out_of_scope_params.each do |param| -%>
<% exts = portfolio_design.all_in_scope_exts_without_param(param) -%>
| <%= param.name_potentially_with_link(exts) %>
| <%= param.schema_type %>
| <%= exts.map { |ext| link_to_udb_doc_ext_param(ext.name, param.name, ext.name) }.join(", ") %>

<% end # do -%>
|===
<% end # if table -%>

== Traps

RISC-V supports both synchronous exceptions and asynchronous interrupts.
TODO: List only traps that exist in this processor certificate model (currently lists all possible in present extensions).
See https://github.com/riscv-software-src/riscv-unified-db/issues/291 and https://github.com/riscv-software-src/riscv-unified-db/issues/324
TODO: Show traps per privilege mode

=== Synchronous Exceptions

|===
| `xcause.CODE` CSR Field Value | Name
<% portfolio_design.in_scope_exception_codes.sort_by{ |code| code.num }.each do |code| -%>
| <%= code.num %> | <%= code.name %>
<% end -%>
|===

=== Asynchronous Interrupts

|===
| `xcause.CODE` CSR Field Value | Name
<% portfolio_design.in_scope_interrupt_codes.sort_by{ |code| code.num }.each do |code| -%>
| <%= code.num %> | <%= code.name %>
<% end -%>
|===

== Instruction Summary

The <%= proc_cert_model.name %> certificate has up to <%= portfolio_design.in_scope_instructions.size %> instructions
(exact number depends on an implementation's options).

[%autowidth]
|===
| Name | Long Name

<% portfolio_design.in_scope_instructions.each do |inst| -%>
| <%= link_to_udb_doc_inst(inst.name) %>
| <%= inst.long_name %>
<% end # do -%>
|===

== CSR Summary

The <%= proc_cert_model.name %> certificate has up to <%= portfolio_design.in_scope_csrs.size %> CSRs
(exact number depends on an implementation's options).

=== By Name

[%autowidth]
|===
| Name | Long Name | Address | Mode | Primary Extension

<% portfolio_design.in_scope_csrs.sort_by!(&:name).each do |csr| -%>
| <%= link_to_udb_doc_csr(csr.name) %>
| <%= csr.long_name %>
| <%= "0x#{csr.address.to_s(16)}" %>
| <%= csr.priv_mode %>
| <%= csr.primary_defined_by %>
<% end # do -%>
|===

=== By Address

[%autowidth]
|===
| Address | Mode | Name | Long Name | Primary Extension

<% portfolio_design.in_scope_csrs.sort_by!(&:address).each do |csr| -%>
| <%= "0x#{csr.address.to_s(16)}" %>
| <%= csr.priv_mode %>
| <%= link_to_udb_doc_csr(csr.name) %>
| <%= csr.long_name %>
| <%= csr.primary_defined_by %>
<% end # do -%>
|===

<% unless proc_cert_model.requirement_groups.empty? -%>
== Additional Requirements

This section contains requirements in addition to those already specified related to extensions and parameters.
These additional requirements are organized as groups of related requirements.

<% proc_cert_model.requirement_groups.each do |group| -%>
=== <%= group.name %>

<%= group.description %>

<% unless group.when.nil? -%>
[IMPORTANT]
<%= group.name %> requirements only apply when <%= group.when_pretty %>.
<% end -%>

[%autowidth]
|===
| Req Number | Description

<% group.requirements.each do |req| -%>
| <%= req.name %>
a| <%= req.description %>
<% unless req.when.nil? -%>
[IMPORTANT]
Requirement <%= req.name %> only apply when <%= req.when_pretty %>.
<% end -%>
<% end -%>
|===

<% end -%>
<% end # unless requirement_groups.empty? -%>

// Appendices
<%= portfolio_design.include_erb("ext_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("inst_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("csr_appendix.adoc.erb") %>
<%= portfolio_design.include_erb("idl_func_appendix.adoc.erb") %>
