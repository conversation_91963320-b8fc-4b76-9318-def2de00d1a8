{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Instruction Variable Schema", "description": "Schema for instruction decode variable definitions", "$defs": {"fully_resolved_data": {"type": "object", "additionalProperties": false, "required": ["location", "type"], "properties": {"location": {"$ref": "schema_defs.json#/$defs/possibly_split_field_location"}, "type": {"type": "object", "required": ["$ref"], "additionalProperties": false, "properties": {"$ref": {"type": "string", "pattern": "^inst_var_type/[^/]*\\.yaml#$"}}}, "$parent_of": {"$ref": "schema_defs.json#/$defs/ref_url_list"}, "$child_of": {"$ref": "schema_defs.json#/$defs/ref_url_list"}}}, "polymorphic_data": {"type": "object", "additionalProperties": false, "properties": {"location": {"$ref": "schema_defs.json#/$defs/possibly_split_field_location"}, "$inherits": {"oneOf": [{"type": "string", "pattern": "^inst_.*/.*\\.yaml#/.*$"}, {"type": "array", "items": {"type": "string", "pattern": "^inst_.*/.*\\.yaml#/.*$"}}]}, "type": {"type": "object", "required": ["$ref"], "additionalProperties": false, "properties": {"$ref": {"type": "string", "pattern": "^inst_var_type/[^/]*\\.yaml#$"}}}}}}, "type": "object", "required": ["$schema", "kind", "name", "data"], "properties": {"$schema": {"const": "inst_var_schema.json#"}, "kind": {"const": "instruction_variable"}, "name": {"type": "string"}, "data": {"oneOf": [{"$ref": "#/$defs/fully_resolved_data"}, {"$ref": "#/$defs/polymorphic_data"}]}}}