<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Module: Idl
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (I)</a> &raquo;
    
    
    <span class="title">Idl</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Module: Idl
  
  
  
</h1>
<div class="box_info">
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl.rb<span class="defines">,<br />
  lib/idl/ast.rb,<br /> lib/idl/type.rb,<br /> lib/idl/passes/prune.rb,<br /> lib/idl/symbol_table.rb,<br /> lib/idl/passes/gen_adoc.rb,<br /> lib/idl/passes/find_return_values.rb</span>
</dd>
  </dl>
  
</div>

<h2>Defined Under Namespace</h2>
<p class="children">
  
    
      <strong class="modules">Modules:</strong> <span class='object_link'><a href="Idl/AstNodeFuncs.html" title="Idl::AstNodeFuncs (module)">AstNodeFuncs</a></span>, <span class='object_link'><a href="Idl/Declaration.html" title="Idl::Declaration (module)">Declaration</a></span>, <span class='object_link'><a href="Idl/Executable.html" title="Idl::Executable (module)">Executable</a></span>, <span class='object_link'><a href="Idl/IntLiteralSyntaxNode.html" title="Idl::IntLiteralSyntaxNode (module)">IntLiteralSyntaxNode</a></span>, <span class='object_link'><a href="Idl/Returns.html" title="Idl::Returns (module)">Returns</a></span>, <span class='object_link'><a href="Idl/Rvalue.html" title="Idl::Rvalue (module)">Rvalue</a></span>
    
  
    
      <strong class="classes">Classes:</strong> <span class='object_link'><a href="Idl/ArrayLiteralAst.html" title="Idl::ArrayLiteralAst (class)">ArrayLiteralAst</a></span>, <span class='object_link'><a href="Idl/AryAccessSyntaxNode.html" title="Idl::AryAccessSyntaxNode (class)">AryAccessSyntaxNode</a></span>, <span class='object_link'><a href="Idl/AryElementAccessAst.html" title="Idl::AryElementAccessAst (class)">AryElementAccessAst</a></span>, <span class='object_link'><a href="Idl/AryElementAssignmentAst.html" title="Idl::AryElementAssignmentAst (class)">AryElementAssignmentAst</a></span>, <span class='object_link'><a href="Idl/AryElementAssignmentSyntaxNode.html" title="Idl::AryElementAssignmentSyntaxNode (class)">AryElementAssignmentSyntaxNode</a></span>, <span class='object_link'><a href="Idl/AryRangeAccessAst.html" title="Idl::AryRangeAccessAst (class)">AryRangeAccessAst</a></span>, <span class='object_link'><a href="Idl/AryRangeAssignmentAst.html" title="Idl::AryRangeAssignmentAst (class)">AryRangeAssignmentAst</a></span>, <span class='object_link'><a href="Idl/AssignmentAst.html" title="Idl::AssignmentAst (class)">AssignmentAst</a></span>, <span class='object_link'><a href="Idl/AstNode.html" title="Idl::AstNode (class)">AstNode</a></span>, <span class='object_link'><a href="Idl/BinaryExpressionAst.html" title="Idl::BinaryExpressionAst (class)">BinaryExpressionAst</a></span>, <span class='object_link'><a href="Idl/BinaryExpressionRightSyntaxNode.html" title="Idl::BinaryExpressionRightSyntaxNode (class)">BinaryExpressionRightSyntaxNode</a></span>, <span class='object_link'><a href="Idl/BitfieldAccessExpressionAst.html" title="Idl::BitfieldAccessExpressionAst (class)">BitfieldAccessExpressionAst</a></span>, <span class='object_link'><a href="Idl/BitfieldDefinitionAst.html" title="Idl::BitfieldDefinitionAst (class)">BitfieldDefinitionAst</a></span>, <span class='object_link'><a href="Idl/BitfieldType.html" title="Idl::BitfieldType (class)">BitfieldType</a></span>, <span class='object_link'><a href="Idl/BitsCastAst.html" title="Idl::BitsCastAst (class)">BitsCastAst</a></span>, <span class='object_link'><a href="Idl/BuiltinEnumDefinitionAst.html" title="Idl::BuiltinEnumDefinitionAst (class)">BuiltinEnumDefinitionAst</a></span>, <span class='object_link'><a href="Idl/BuiltinTypeNameAst.html" title="Idl::BuiltinTypeNameAst (class)">BuiltinTypeNameAst</a></span>, <span class='object_link'><a href="Idl/BuiltinTypeNameSyntaxNode.html" title="Idl::BuiltinTypeNameSyntaxNode (class)">BuiltinTypeNameSyntaxNode</a></span>, <span class='object_link'><a href="Idl/BuiltinVariableAst.html" title="Idl::BuiltinVariableAst (class)">BuiltinVariableAst</a></span>, <span class='object_link'><a href="Idl/BuiltinVariableSyntaxNode.html" title="Idl::BuiltinVariableSyntaxNode (class)">BuiltinVariableSyntaxNode</a></span>, <span class='object_link'><a href="Idl/Compiler.html" title="Idl::Compiler (class)">Compiler</a></span>, <span class='object_link'><a href="Idl/ConcatenationExpressionAst.html" title="Idl::ConcatenationExpressionAst (class)">ConcatenationExpressionAst</a></span>, <span class='object_link'><a href="Idl/ConditionalReturnStatementAst.html" title="Idl::ConditionalReturnStatementAst (class)">ConditionalReturnStatementAst</a></span>, <span class='object_link'><a href="Idl/ConditionalStatementAst.html" title="Idl::ConditionalStatementAst (class)">ConditionalStatementAst</a></span>, <span class='object_link'><a href="Idl/ConditionalStatementSyntaxNode.html" title="Idl::ConditionalStatementSyntaxNode (class)">ConditionalStatementSyntaxNode</a></span>, <span class='object_link'><a href="Idl/CsrFieldReadExpressionAst.html" title="Idl::CsrFieldReadExpressionAst (class)">CsrFieldReadExpressionAst</a></span>, <span class='object_link'><a href="Idl/CsrReadExpressionAst.html" title="Idl::CsrReadExpressionAst (class)">CsrReadExpressionAst</a></span>, <span class='object_link'><a href="Idl/CsrSoftwareReadAst.html" title="Idl::CsrSoftwareReadAst (class)">CsrSoftwareReadAst</a></span>, <span class='object_link'><a href="Idl/CsrSoftwareWriteAst.html" title="Idl::CsrSoftwareWriteAst (class)">CsrSoftwareWriteAst</a></span>, <span class='object_link'><a href="Idl/CsrType.html" title="Idl::CsrType (class)">CsrType</a></span>, <span class='object_link'><a href="Idl/CsrWriteAst.html" title="Idl::CsrWriteAst (class)">CsrWriteAst</a></span>, <span class='object_link'><a href="Idl/DontCareLvalueAst.html" title="Idl::DontCareLvalueAst (class)">DontCareLvalueAst</a></span>, <span class='object_link'><a href="Idl/DontCareReturnAst.html" title="Idl::DontCareReturnAst (class)">DontCareReturnAst</a></span>, <span class='object_link'><a href="Idl/DontCareReturnSyntaxNode.html" title="Idl::DontCareReturnSyntaxNode (class)">DontCareReturnSyntaxNode</a></span>, <span class='object_link'><a href="Idl/ElseIfAst.html" title="Idl::ElseIfAst (class)">ElseIfAst</a></span>, <span class='object_link'><a href="Idl/EnumDefinitionAst.html" title="Idl::EnumDefinitionAst (class)">EnumDefinitionAst</a></span>, <span class='object_link'><a href="Idl/EnumRefAst.html" title="Idl::EnumRefAst (class)">EnumRefAst</a></span>, <span class='object_link'><a href="Idl/EnumRefSyntaxNode.html" title="Idl::EnumRefSyntaxNode (class)">EnumRefSyntaxNode</a></span>, <span class='object_link'><a href="Idl/EnumerationType.html" title="Idl::EnumerationType (class)">EnumerationType</a></span>, <span class='object_link'><a href="Idl/ExecutionCommentAst.html" title="Idl::ExecutionCommentAst (class)">ExecutionCommentAst</a></span>, <span class='object_link'><a href="Idl/FieldAssignmentAst.html" title="Idl::FieldAssignmentAst (class)">FieldAssignmentAst</a></span>, <span class='object_link'><a href="Idl/FieldNameAst.html" title="Idl::FieldNameAst (class)">FieldNameAst</a></span>, <span class='object_link'><a href="Idl/ForLoopAst.html" title="Idl::ForLoopAst (class)">ForLoopAst</a></span>, <span class='object_link'><a href="Idl/ForLoopSyntaxNode.html" title="Idl::ForLoopSyntaxNode (class)">ForLoopSyntaxNode</a></span>, <span class='object_link'><a href="Idl/FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">FunctionBodyAst</a></span>, <span class='object_link'><a href="Idl/FunctionBodySyntaxNode.html" title="Idl::FunctionBodySyntaxNode (class)">FunctionBodySyntaxNode</a></span>, <span class='object_link'><a href="Idl/FunctionCallExpressionAst.html" title="Idl::FunctionCallExpressionAst (class)">FunctionCallExpressionAst</a></span>, <span class='object_link'><a href="Idl/FunctionCallExpressionSyntaxNode.html" title="Idl::FunctionCallExpressionSyntaxNode (class)">FunctionCallExpressionSyntaxNode</a></span>, <span class='object_link'><a href="Idl/FunctionDefAst.html" title="Idl::FunctionDefAst (class)">FunctionDefAst</a></span>, <span class='object_link'><a href="Idl/FunctionType.html" title="Idl::FunctionType (class)">FunctionType</a></span>, <span class='object_link'><a href="Idl/GlobalAst.html" title="Idl::GlobalAst (class)">GlobalAst</a></span>, <span class='object_link'><a href="Idl/GlobalWithInitializationAst.html" title="Idl::GlobalWithInitializationAst (class)">GlobalWithInitializationAst</a></span>, <span class='object_link'><a href="Idl/IdAst.html" title="Idl::IdAst (class)">IdAst</a></span>, <span class='object_link'><a href="Idl/IdSyntaxNode.html" title="Idl::IdSyntaxNode (class)">IdSyntaxNode</a></span>, <span class='object_link'><a href="Idl/IfAst.html" title="Idl::IfAst (class)">IfAst</a></span>, <span class='object_link'><a href="Idl/IfBodyAst.html" title="Idl::IfBodyAst (class)">IfBodyAst</a></span>, <span class='object_link'><a href="Idl/IfSyntaxNode.html" title="Idl::IfSyntaxNode (class)">IfSyntaxNode</a></span>, <span class='object_link'><a href="Idl/InstructionOperationAst.html" title="Idl::InstructionOperationAst (class)">InstructionOperationAst</a></span>, <span class='object_link'><a href="Idl/InstructionOperationSyntaxNode.html" title="Idl::InstructionOperationSyntaxNode (class)">InstructionOperationSyntaxNode</a></span>, <span class='object_link'><a href="Idl/IntLiteralAst.html" title="Idl::IntLiteralAst (class)">IntLiteralAst</a></span>, <span class='object_link'><a href="Idl/IsaAst.html" title="Idl::IsaAst (class)">IsaAst</a></span>, <span class='object_link'><a href="Idl/MultiVariableAssignmentAst.html" title="Idl::MultiVariableAssignmentAst (class)">MultiVariableAssignmentAst</a></span>, <span class='object_link'><a href="Idl/MultiVariableDeclarationAst.html" title="Idl::MultiVariableDeclarationAst (class)">MultiVariableDeclarationAst</a></span>, <span class='object_link'><a href="Idl/NoopAst.html" title="Idl::NoopAst (class)">NoopAst</a></span>, <span class='object_link'><a href="Idl/ParenExpressionAst.html" title="Idl::ParenExpressionAst (class)">ParenExpressionAst</a></span>, <span class='object_link'><a href="Idl/ParenExpressionSyntaxNode.html" title="Idl::ParenExpressionSyntaxNode (class)">ParenExpressionSyntaxNode</a></span>, <span class='object_link'><a href="Idl/PostDecrementExpressionAst.html" title="Idl::PostDecrementExpressionAst (class)">PostDecrementExpressionAst</a></span>, <span class='object_link'><a href="Idl/PostIncrementExpressionAst.html" title="Idl::PostIncrementExpressionAst (class)">PostIncrementExpressionAst</a></span>, <span class='object_link'><a href="Idl/ReplicationExpressionAst.html" title="Idl::ReplicationExpressionAst (class)">ReplicationExpressionAst</a></span>, <span class='object_link'><a href="Idl/ReplicationExpressionSyntaxNode.html" title="Idl::ReplicationExpressionSyntaxNode (class)">ReplicationExpressionSyntaxNode</a></span>, <span class='object_link'><a href="Idl/ReturnStatementAst.html" title="Idl::ReturnStatementAst (class)">ReturnStatementAst</a></span>, <span class='object_link'><a href="Idl/SignCastAst.html" title="Idl::SignCastAst (class)">SignCastAst</a></span>, <span class='object_link'><a href="Idl/SignCastSyntaxNode.html" title="Idl::SignCastSyntaxNode (class)">SignCastSyntaxNode</a></span>, <span class='object_link'><a href="Idl/StatementAst.html" title="Idl::StatementAst (class)">StatementAst</a></span>, <span class='object_link'><a href="Idl/StatementSyntaxNode.html" title="Idl::StatementSyntaxNode (class)">StatementSyntaxNode</a></span>, <span class='object_link'><a href="Idl/SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span>, <span class='object_link'><a href="Idl/TernaryOperatorExpressionAst.html" title="Idl::TernaryOperatorExpressionAst (class)">TernaryOperatorExpressionAst</a></span>, <span class='object_link'><a href="Idl/TernaryOperatorExpressionSyntaxNode.html" title="Idl::TernaryOperatorExpressionSyntaxNode (class)">TernaryOperatorExpressionSyntaxNode</a></span>, <span class='object_link'><a href="Idl/Type.html" title="Idl::Type (class)">Type</a></span>, <span class='object_link'><a href="Idl/UnaryOperatorExpressionAst.html" title="Idl::UnaryOperatorExpressionAst (class)">UnaryOperatorExpressionAst</a></span>, <span class='object_link'><a href="Idl/UserTypeNameAst.html" title="Idl::UserTypeNameAst (class)">UserTypeNameAst</a></span>, <span class='object_link'><a href="Idl/Var.html" title="Idl::Var (class)">Var</a></span>, <span class='object_link'><a href="Idl/VariableAssignmentAst.html" title="Idl::VariableAssignmentAst (class)">VariableAssignmentAst</a></span>, <span class='object_link'><a href="Idl/VariableAssignmentSyntaxNode.html" title="Idl::VariableAssignmentSyntaxNode (class)">VariableAssignmentSyntaxNode</a></span>, <span class='object_link'><a href="Idl/VariableDeclarationAst.html" title="Idl::VariableDeclarationAst (class)">VariableDeclarationAst</a></span>, <span class='object_link'><a href="Idl/VariableDeclarationWithInitializationAst.html" title="Idl::VariableDeclarationWithInitializationAst (class)">VariableDeclarationWithInitializationAst</a></span>, <span class='object_link'><a href="Idl/VariableDeclarationWithInitializationSyntaxNode.html" title="Idl::VariableDeclarationWithInitializationSyntaxNode (class)">VariableDeclarationWithInitializationSyntaxNode</a></span>, <span class='object_link'><a href="Idl/XregType.html" title="Idl::XregType (class)">XregType</a></span>
    
  
</p>









</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>