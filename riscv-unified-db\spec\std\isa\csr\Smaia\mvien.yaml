# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: mvien
long_name: Machine Virtual Interrupt Enable
address: 0x308
writable: true
priv_mode: M
length: MXLEN
definedBy: Smaia
description: |
  The `mvien` register enables virtual interrupts in M-mode. This register
  controls which virtual interrupts can be taken when the hart is in M-mode.
  
  The `mvien` register is a WARL register that must be able to hold the value zero.
  
  When a bit in `mvien` is clear, the corresponding virtual interrupt is disabled
  and will not cause a trap to M-mode. When a bit in `mvien` is set, the
  corresponding virtual interrupt is enabled and may cause a trap to M-mode
  if the interrupt is also pending and not masked by global interrupt enables.
fields:
  VSSIE:
    location: 2
    description: |
      Virtual Supervisor Software Interrupt Enable.
      
      When set, enables virtual supervisor software interrupts in M-mode.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Smaia
  VSTIE:
    location: 6
    description: |
      Virtual Supervisor Timer Interrupt Enable.

      When set, enables virtual supervisor timer interrupts in M-mode.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Smaia
  VSEIE:
    location: 10
    description: |
      Virtual Supervisor External Interrupt Enable.

      When set, enables virtual supervisor external interrupts in M-mode.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Smaia
  SGEI:
    location: 12
    description: |
      Supervisor Guest External Interrupt Enable.

      When set, enables supervisor guest external interrupts in M-mode.
    type: RW
    reset_value: UNDEFINED_LEGAL
    definedBy: Smaia
