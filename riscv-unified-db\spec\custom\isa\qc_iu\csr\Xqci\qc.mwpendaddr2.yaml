# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mwpendaddr2
long_name: Watchpoint end address for region 2
address: 0x7d6
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Watchpoint end address for region 2
fields:
  ADDR:
    type: RW
    reset_value: 0
    location: 31-0
    description: Watchpoint end address
