# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Ziccif
long_name: Main memory fetch requirement for RVA profiles
type: unprivileged
description: |
  Main memory regions with both the cacheability and coherence PMAs must support instruction
  fetch, and any instruction fetches of naturally aligned power-of-2 sizes up to
  min(ILEN,XLEN) (i.e., 32 bits for RVA22) are atomic.

  [NOTE]
  This extension was ratified as part of the RVA20 profile.
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: null
