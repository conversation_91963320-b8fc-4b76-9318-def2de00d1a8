# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: c.lw
long_name: Load word
description: |
  Loads a 32-bit value from memory into register xd.
  It computes an effective address by adding the zero-extended offset, scaled by 4,
  to the base address in register xs1.
  It expands to `lw` `xd, offset(xs1)`.
definedBy:
  anyOf:
    - C
    - Zca
assembly: xd, imm(xs1)
encoding:
  match: 010-----------00
  variables:
    - name: imm
      location: 5|12-10|6
      left_shift: 2
    - name: xd
      location: 4-2
    - name: xs1
      location: 9-7
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::C) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  XReg virtual_address = X[creg2reg(xs1)] + imm;

  X[creg2reg(xd)] = sext(read_memory<32>(virtual_address, $encoding), 32);

# SPDX-SnippetBegin
# SPDX-FileCopyrightText: 2017-2025 Contributors to the RISCV Sail Model <https://github.com/riscv/sail-riscv/blob/master/LICENCE>
# SPDX-License-Identifier: BSD-2-Clause
sail(): |
  {
    let offset : xlenbits = sign_extend(imm);
    /* Get the address, X(rs1) + offset.
       Some extensions perform additional checks on address validity. */
    match ext_data_get_addr(rs1, offset, Read(Data), width) {
      Ext_DataAddr_Error(e)  => { ext_handle_data_check_error(e); RETIRE_FAIL },
      Ext_DataAddr_OK(vaddr) =>
        if   check_misaligned(vaddr, width)
        then { handle_mem_exception(vaddr, E_Load_Addr_Align()); RETIRE_FAIL }
        else match translateAddr(vaddr, Read(Data)) {
          TR_Failure(e, _) => { handle_mem_exception(vaddr, e); RETIRE_FAIL },
          TR_Address(paddr, _) =>
            match (width) {
              BYTE =>
                process_load(rd, vaddr, mem_read(Read(Data), paddr, 1, aq, rl, false), is_unsigned),
              HALF =>
                process_load(rd, vaddr, mem_read(Read(Data), paddr, 2, aq, rl, false), is_unsigned),
              WORD =>
                process_load(rd, vaddr, mem_read(Read(Data), paddr, 4, aq, rl, false), is_unsigned),
              DOUBLE if sizeof(xlen) >= 64 =>
                process_load(rd, vaddr, mem_read(Read(Data), paddr, 8, aq, rl, false), is_unsigned),
              _ => report_invalid_width(__FILE__, __LINE__, width, "load")
            }
        }
    }
  }

# SPDX-SnippetEnd
