# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fcvt.h.q
long_name: Floating-point Convert Quad-precision to Half-precision
description:
  - id: inst-fcvt.h.q-behaviour
    normative: false
    text: |
      `fcvt.h.q` converts a Quad-precision Floating-point number to a Half-precision Floating-point number.
definedBy:
  allOf: [Q, Zfh]
assembly: fd, fs1, rm
encoding:
  match: 010001000011-------------1010011
  variables:
    - name: fs1
      location: 19-15
    - name: rm
      location: 14-12
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
operation(): |
