# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/proc_cert_model_schema.json

$schema: proc_cert_model_schema.json#
kind: processor certificate model
name: MockProcessor
long_name: Mock Processor Certificate Model Long Name
class:
  $ref: proc_cert_class/MockProcessor.yaml#

  # MXLEN used by rakefile
base: 64

in_scope_priv_modes:
  - M

# Semantic versions within the model
versions:
  - version: "1.0.0"
  - version: "1.1.0"

revision_history:
  - revision: "0.1.0"
    date: "2024-10-04"
    changes:
      - Created to test CRDs
  - revision: "0.2.0"
    date: "2024-10-05"
    changes:
      - Also created to test CRDs

introduction: |
  Mock Certificate Model introduction:

  * Hello
  * Bob!

# Specification versions
tsc_profile_release:
  $ref: profile_release/Mock.yaml#
unpriv_isa_manual_revision: "20191213"
priv_isa_manual_revision: "20190608-Priv-MSU-Ratified"
debug_manual_revision: "0.13.2"

# XXX - Remove version information since specifying priv/unpriv ISA manual should imply this.
extensions:
  $inherits:
    - "profile/MP-S-64.yaml#/extensions"
  I:
    note: Just added this note to I extension
  Xmock:
    version: "~> 1.0.0"
    presence: mandatory
    param_constraints:
      MOCK_ENUM_2_INTS: {}
      MOCK_ENUM_2_STRINGS: {}
      MOCK_BOOL_1: {}
      MOCK_BOOL_2:
        schema:
          const: true
      MOCK_1_BIT_INT: {}
      MOCK_2_BIT_INT: {}
      MOCK_25_BIT_INT: {}
      MOCK_32_BIT_INT:
        schema:
          const: 0xdeadbeef
      MOCK_64_BIT_INT: {}
      MOCK_INT_RANGE_0_TO_127: {}
      MOCK_INT_RANGE_0_TO_128: {}
      MOCK_INT_RANGE_1_TO_128: {}
      MOCK_INT_RANGE_0_TO_999: {}
      MOCK_INT_RANGE_0_TO_1023: {}
      MOCK_INT_RANGE_1000_TO_2048: {}
      MOCK_ARRAY_INT_ENUM: {}
      MOCK_ARRAY_BOOL_ARRAY_OF_8_FIRST_2_FALSE: {}
      MOCK_ARRAY_STRING_ENUM1:
        schema:
          const: DEF
      MOCK_ARRAY_STRING_ENUM2:
        schema:
          contains: { const: DEF }
  C:
    presence: mandatory
    param_constraints:
      MUTABLE_MISA_C:
        schema:
          const: false
    note: |
      Here's a multi-line note +
      for the C extension.
  M:
    presence: mandatory
  Zicsr:
    presence: mandatory
  Zicntr:
    presence: mandatory
    param_constraints:
      TIME_CSR_IMPLEMENTED: {} # Unconstrained
  Sm:
    version: "~> 1.12.0"
    presence: mandatory
    param_constraints:
      MTVEC_BASE_ALIGNMENT_DIRECT: {} # Unconstrained
      MTVEC_BASE_ALIGNMENT_VECTORED: {} # Unconstrained
      ARCH_ID: {} # Unconstrained
      IMP_ID: {} # Unconstrained
      VENDOR_ID_BANK: {} # Unconstrained
      VENDOR_ID_OFFSET: {} # Unconstrained
      MISA_CSR_IMPLEMENTED: {} # Unconstrained
      MTVAL_WIDTH: {} # Unconstrained
      MTVEC_MODES:
        note: Here's a note for MTVEC_MODES parameter.
        schema:
          contains: { const: 0 }
      PHYS_ADDR_WIDTH: {} # Unconstrained
      PRECISE_SYNCHRONOUS_EXCEPTIONS:
        schema:
          const: true
      TRAP_ON_ECALL_FROM_M:
        schema:
          const: true
      TRAP_ON_EBREAK:
        schema:
          const: true
      REPORT_VA_IN_MTVAL_ON_BREAKPOINT:
        schema:
          const: true
      REPORT_VA_IN_MTVAL_ON_INSTRUCTION_ACCESS_FAULT:
        schema:
          const: true
      REPORT_VA_IN_MTVAL_ON_LOAD_ACCESS_FAULT:
        schema:
          const: true
      REPORT_VA_IN_MTVAL_ON_STORE_AMO_ACCESS_FAULT:
        schema:
          const: true
      REPORT_ENCODING_IN_MTVAL_ON_ILLEGAL_INSTRUCTION:
        schema:
          const: true
      M_MODE_ENDIANNESS:
        schema:
          const: little
        # TODO: Uncomment when GitHub issue # is fixed.
        #schema:
        #- when:
        #    version: "=1.0.0"
        #    then:
        #      const: little
        #- when:
        #    version: "=1.1.0"
        #    then:
        #      enum: [little, big]
      MXLEN:
        schema:
          const: 64
      CONFIG_PTR_ADDRESS:
        schema:
          const: 0xdeadbeef
        note: "This parameter and its associated CSR shouldn't be here. See GitHub issue #53."
  Zifencei:
    presence: optional
    note: "Here's a note for Zifencei"
  Zicbop:
    presence: optional
    note: "Testing CACHE_BLOCK_SIZE parameter which is also defined by Zicbom."
    param_constraints:
      CACHE_BLOCK_SIZE:
        schema:
          const: 64
  Zicbom:
    presence: optional
    note: "Testing CACHE_BLOCK_SIZE parameter which is also defined by Zicbop."
    param_constraints:
      CACHE_BLOCK_SIZE:
        schema:
          const: 64
  B:
    presence: mandatory
    note: "Added this as mandatory to see if Zba, Zbb, and Zbs are included."

requirement_groups:
  any_xlen:
    name: Req-Grp-Any-MXLEN
    description: |
      A bunch of additional requirements not associated with an extension.
    requirements:
      - name: REQ-ANY-MXLEN-001
        description: Must pay your taxes on time
      - name: REQ-ANY-MXLEN-002
        description: Don't count your chickens before they're hatched!
  xlen32:
    name: Req-Grp-XLEN32
    when:
      xlen: 32
    description: |
      A bunch of additional requirements only that should show up for MXLEN=32
    requirements:
      - name: REQ-XLEN32-001
        description: Need lots of extra CSRs with `h` suffix
  xlen64:
    name: Req-Grp-XLEN64
    when:
      xlen: 64
    description: |
      A bunch of additional requirements only that should show up for MXLEN=64
    requirements:
      - name: REQ-XLEN64-001
        description: Can avoid adding extra CSRs with `h` suffix
extra_notes:
  - presence: optional
    text: Here's the first extra note for the optional extensions section.
  - presence: mandatory
    text: |
      Here's the first extra note for the mandatory extensions section.
      This note is multiple lines.
  - presence: optional
    text: Here's the second extra note for the optional extensions section.
recommendations:
  - text: |
      Implementations are strongly recommended to raise illegal-instruction
      exceptions on attempts to execute unimplemented opcodes.
  - text: Micky should give Pluto an extra treat
