# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hstatus
long_name: Hypervisor Status
address: 0x600
writable: true
priv_mode: S
length: SXLEN
description: |
  The hstatus register tracks and controls a VS-mode guest.

  Unlike fields in `sstatus`, which are all aliases of fields `mstatus`,
  bits in `hstatus` are independent bits and do not have aliases.
definedBy: H
fields:
  VSXL:
    long_name: VS-mode XLen
    location: 33-32
    base: 64
    description:
      - id: csr-hstatus-vsxl-values
        normative: false
        text: |
          Determines the effective XLEN in VS-mode. Valid values are:

          [separator="!"]
          !===
          ! Value ! VSXLEN

          ! 0     ! 32
          ! 1     ! 64
          !===
        when(): return VSXLEN == 3264;
      - id: csr-hstatus-vsxl-rv32
        normative: false
        text: |
          Because the implementation only supports a single VSXLEN == 32, this field is read-only-0.
        when(): return VSXLEN == 32;
      - id: csr-hstatus-vsxl-rv64
        normative: false
        text: |
          Because the implementation only supports a single VSXLEN == 64, this field is read-only-1.
        when(): return VSXLEN == 64;

    type(): |
      if ((VSXLEN == 32) || (VSXLEN == 64)) {
        return CsrFieldType::RO;
      } else {
        return CsrFieldType::RW;
      }
    reset_value(): |
      if (VSXLEN == 32) {
        # immutable, always 0
        return 0;
      } else if (VSXLEN == 64) {
        # immutable, always 1
        return 1;
      } else {
        # mutable, undefined reset state
        return UNDEFINED_LEGAL;
      }
    sw_write(csr_value): |
      if (CSR[mstatus].SXL < csr_value.VSXL) {
        # cannot set VSXLEN less than SXLEN
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else if ((csr_value.VSXL & 0b10) != 0) {
        # MSB of VSXL represents XLEN > 64, which isn't legal
        return UNDEFINED_LEGAL_DETERMINISTIC;
      } else {
        return csr_value.VSXL;
      }
  VTSR:
    location: 22
    long_name: Virtual Trap SRET
    description:
      - id: csr-hstatus-vtsr-behavior
        normative: false
        text: |
          When `hstatus.VTSR` is set, executing the `sret` instruction in VS-mode
          raises a `Virtual Instruction` exception.

          When `hstatus.VTSR` is clear, an `sret` instruction in VS-mode returns control
          to the mode stored in `vsstatus.SPP`.
    type: RW
    reset_value: UNDEFINED_LEGAL
  VTW:
    location: 21
    long_name: Virtual Trap WFI
    description:
      - id: csr-hstatus-vtw-behavior
        normative: false
        text: |
          When `hstatus.VTW` is set, a `wfi` instruction executed in VS-mode raises
          a `Virtual Instruction` exception after waiting an implementation-defined
          amount of time (which can be 0).

          When both `hstatus.VTW` and `mstatus.TW` are clear, a `wfi` instruction
          executes in VS-mode without a timeout period.

          The `wfi` instruction is also affected by `mstatus.TW`, as shown below:

          [separator="!",%autowidth,%footer]
          !===
          .2+! [.rotate]#`mstatus.TW`# .2+! [.rotate]#`hstatus.VTW`# 4+^.>! `wfi` behavior
          h! HS-mode h! U-mode h! VS-mode h! VU-mode

          ! 0 ! 0 ! Wait ! Trap (I) ! Wait ! Trap (V)
          ! 0 ! 1 ! Wait ! Trap (I) ! Trap (V) ! Trap (V)
          ! 1 ! - ! Trap (I) ! Trap (I) ! Trap (I) ! Trap (I)

          6+! Trap (I) - Trap with `Illegal Instruction` code +
          Trap (V) - Trap with `Virtual Instruction` code
          !===
    type: RW
    reset_value: UNDEFINED_LEGAL
  VTVM:
    location: 20
    long_name: Virtual Trap Virtual Memory
    description:
      - id: csr-hstatus-vtvm-behavior
        normative: false
        text: |
          When set, a 'Virtual Instruction` trap occurs when executing an `sfence.vma`, `sinval.vma`,
          or an explicit CSR access of the `satp` (really `vsatp`) register when in VS-mode.

          When clear, the instructions execute as normal in VS-mode.

          Notably, `hstatus.VTVM` does *not* cause `hfence.vvma`, `sfence.w.inval`, or `sfence.inval.ir` to trap.

          `mstatus.TVM` does not affect the VS-mode instructions controlled by `hstatus.TVTM`.
    type: RW
    reset_value: UNDEFINED_LEGAL
  VGEIN:
    location: 17-12
    long_name: Virtual Guest External Interrupt Number
    description:
      - id: csr-hstatus-vgein-behavior
        normative: false
        text: |
          Selects the guest external interrupt source for VS-level external interrupts.

          When `hstatus.VGEIN` == 0, no external interrupt source is selected.

          When `hstatus.VGEIN` != 0, it selects which bit of `hgeip` is currently active in VS-mode.

    type(): |
      # if NUM_EXTERNAL_GUEST_INTERRUPTS+1 is 63 (because indexing in `hgeip` starts at 1),
      # then the field accepts any value.
      # Otherwise, it accepts a restricted set of values
      if (NUM_EXTERNAL_GUEST_INTERRUPTS == 63) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RWR;
      }
    reset_value: UNDEFINED_LEGAL
    legal?(csr_value): |
      return csr_value.VGEIN <= NUM_EXTERNAL_GUEST_INTERRUPTS;
    sw_write(csr_value): |
      if (csr_value.VGEIN <= NUM_EXTERNAL_GUEST_INTERRUPTS) {
        return csr_value.VGEIN;
      } else {
        return ILLEGAL_WLRL;
      }
  HU:
    location: 9
    long_name: Hypervisor in U-mode
    description:
      - id: csr-hstatus-hu-behavior
        normative: false
        text: |
          When set, the hypervisor load/store instructions (`hlv`, `hlvx`, and `hsv`) can be
          executed in U-mode.

          When clear, the hypervisor load/store instructions cause an `Illegal Instruction` trap.
    type: RW
    reset_value: UNDEFINED_LEGAL
  SPVP:
    location: 8
    long_name: Supervisor Previous Virtual Privilege
    description:
      - id: csr-hstatus-spvp-behavior
        normative: false
        text: |
          Written by hardware:

          * When taking a trap into HS-mode from VS-mode or VU-mode, `hstatus.SPVP` is written with the nominal privilege mode

          Notably, unlike its analog `mstatus.SPP`, `hstatus.SPVP` is *not* cleared when returning from a trap.

          Can also be written by software without immediate side-effect.

          Affects execution by:

          * Controls the effective privilege level applied to the hypervisor load/store instructions, `hlv`, `hlvx`, and `hsv`.
    type: RW
    reset_value: UNDEFINED_LEGAL
  SPV:
    location: 7
    long_name: Supervisor Previous Virtualization Mode
    description:
      - id: csr-hstatus-spv-behavior
        normative: false
        text: |
          Written by hardware:

          * On a trap into HS-mode, hardware writes 1 when the prior mode was VS-mode or VU-mode, and 0 otherwise.

          Can also be written by software without immediate side-effect.

          Affects execution by:

          * When an `sret` instruction in executed in HS-mode or M-mode,
            control returns to VS-mode or VU-mode (as selected by `mstatus.SPP`) when
            `hstatus.SPV` is 1 and to HS-mode or U-mode otherwise.
    type: RW
    reset_value: UNDEFINED_LEGAL
  GVA:
    location: 6
    long_name: Guest Virtual Address
    description:
      - id: csr-hstatus-gva-behavior
        normative: false
        text: |
          Written by hardware whenever a trap is taken into HS-mode:

          * Writes 1 when a trap causes a guest virtual address to be written into `stval` (`Breakpoint`, `* Address Misaligned`, `* Access Fault`, `* Page Fault`, or `* Guest-Page Fault`).
          * Writes 0 otherwise

          Does not affect execution.
    type: RW
    reset_value: UNDEFINED_LEGAL
  VSBE:
    location: 5
    long_name: VS-mode Big Endian
    description:
      - id: csr-hstatus-vgein-behavior
        normative: false
        text: |
          Controls the endianness of data VS-mode (0 = little, 1 = big).
          Instructions are always little endian, regardless of the data setting.

      - id: csr-hstatus-vgein-little-endian
        normative: false
        text: |
          Since the CPU does not support big endian in VS-mode, this is hardwired to 0.
        when(): return VS_MODE_ENDIANNESS == "little";

      - id: csr-hstatus-vgein-big-endian
        normative: false
        text: |
          Since the CPU does not support little endian in VS-mode, this is hardwired to 1.
        when(): return VS_MODE_ENDIANNESS == "big";
    type(): |
      if (VS_MODE_ENDIANNESS == "dynamic") {
        # mode is mutable
        return CsrFieldType::RW;
      } else {
        # mode is fixed as either little or big endian
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (VS_MODE_ENDIANNESS == "little") {
        # little endian
        return 0;
      } else if (VS_MODE_ENDIANNESS == "big") {
        # big endian
        return 1;
      } else {
        # mutable
        return UNDEFINED_LEGAL;
      }
