# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/csr_schema.json

kind: csr
name: qc.mclicilvl21
long_name: IRQ Level 21
address: 0xbd5
length: 32
priv_mode: M
base: 32
definedBy:
  anyOf:
    - name: Xqci
      version: ">=0.7"
    - name: Xqciint
      version: ">=0.4"
description: |
  Level bits for IRQs 168-175
fields:
  IRQ168:
    type: RW
    reset_value: 0
    location: 3 - 0
    description: IRQ168 level
  IRQ169:
    type: RW
    reset_value: 0
    location: 7 - 4
    description: IRQ169 level
  IRQ170:
    type: RW
    reset_value: 0
    location: 11 - 8
    description: IRQ170 level
  IRQ171:
    type: RW
    reset_value: 0
    location: 15 - 12
    description: IRQ171 level
  IRQ172:
    type: RW
    reset_value: 0
    location: 19 - 16
    description: IRQ172 level
  IRQ173:
    type: RW
    reset_value: 0
    location: 23 - 20
    description: IRQ173 level
  IRQ174:
    type: RW
    reset_value: 0
    location: 27 - 24
    description: IRQ174 level
  IRQ175:
    type: RW
    reset_value: 0
    location: 31 - 28
    description: IRQ175 level
