# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.insbhr
long_name: Insert bits in 64-bit higher part (Register)
description: |
  Insertion of a subset of bits from `rs1` into `rd`.
  Instruction intended for insertion bits into bitfield within 64-bits,
  when bitfield crosses 32-bit bundary.
  Lower part of 64-bit destination is inserted using QC32.INSBR,
  higher part using QC32.INSBHR.
  The width of the subset is determined by `rs2` bits [21:16] (0..32),
  and the offset of the subset is determined by `rs2` bits [4:0].
  In case when width + offset < 33 or width == 0, the destination register is left unchanged.
  In case when `rs2` bit [21] == 1 width is enforced to 32.
  Instruction encoded in R instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcibm
base: 32
encoding:
  match: 0000001----------011-----0001011
  variables:
    - name: rs2
      location: 24-20
      not: 0
    - name: rs1
      location: 19-15
    - name: rd
      location: 11-7
      not: 0
assembly: " xd, xs1, xs2"
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  XReg width_bits = X[rs2][21:16];
  XReg width = (width_bits > 32) ? 32 : width_bits;
  XReg shamt = X[rs2][4:0];
  if ((width + shamt > 32) && (width > 0)) {
    XReg shifted_one = 32'b1 << (width + shamt - 32);
    XReg mask = (shifted_one - 1);
    XReg orig_val = X[rd];
    XReg shifted_rs1 = X[rs1] >> (32 - shamt);
    X[rd] = (orig_val & ~mask) | (shifted_rs1 & mask);
  }
