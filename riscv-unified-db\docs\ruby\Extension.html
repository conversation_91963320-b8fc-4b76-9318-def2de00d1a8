<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Class: Extension
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="css/style.css" type="text/css" />

  <link rel="stylesheet" href="css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Extension";
  relpath = '';
</script>


  <script type="text/javascript" charset="utf-8" src="js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="_index.html">Index (E)</a> &raquo;
    
    
    <span class="title">Extension</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Class: Extension
  
  
  
</h1>
<div class="box_info">
  
  <dl>
    <dt>Inherits:</dt>
    <dd>
      <span class="inheritName"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></span>
      
        <ul class="fullTree">
          <li>Object</li>
          
            <li class="next"><span class='object_link'><a href="ArchDefObject.html" title="ArchDefObject (class)">ArchDefObject</a></span></li>
          
            <li class="next">Extension</li>
          
        </ul>
        <a href="#" class="inheritanceTree">show all</a>
      
    </dd>
  </dl>
  

  
  
  
  
  

  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/arch_def.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>Extension definition</p>


  </div>
</div>
<div class="tags">
  

</div>



  <h2>Instance Attribute Summary <small><a href="#" class="summary_toggle">collapse</a></small></h2>
  <ul class="summary">
    
      <li class="public ">
  <span class="summary_signature">
    
      <a href="#arch_def-instance_method" title="#arch_def (instance method)">#<strong>arch_def</strong>  &#x21d2; ArchDef </a>
    

    
  </span>
  
  
  
    
      <span class="note title readonly">readonly</span>
    
    
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>The architecture defintion.</p>
</div></span>
  
</li>

    
  </ul>




  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#implies-instance_method" title="#implies (instance method)">#<strong>implies</strong>(version_requirement = &quot;&gt;= 0&quot;)  &#x21d2; Array&lt;ExtensionVersion&gt; </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>Array of extensions implied by any version of this extension meeting version_requirement.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#initialize-instance_method" title="#initialize (instance method)">#<strong>initialize</strong>(ext_data, arch_def)  &#x21d2; Extension </a>
    

    
  </span>
  
  
    <span class="note title constructor">constructor</span>
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>A new instance of Extension.</p>
</div></span>
  
</li>

      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#instructions-instance_method" title="#instructions (instance method)">#<strong>instructions</strong>  &#x21d2; Object </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>returns the list of instructions implemented by this extension.</p>
</div></span>
  
</li>

      
    </ul>
  


  
  
  <div id="constructor_details" class="method_details_list">
  <h2>Constructor Details</h2>
  
    <div class="method_details first">
  <h3 class="signature first" id="initialize-instance_method">
  
    #<strong>initialize</strong>(ext_data, arch_def)  &#x21d2; <tt><span class='object_link'><a href="" title="Extension (class)">Extension</a></span></tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns a new instance of Extension.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>ext_data</span>
      
      
        <span class='type'>(<tt>Hash&lt;String, Object&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The extension data from the architecture spec</p>
</div>
      
    </li>
  
    <li>
      
        <span class='name'>arch_def</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The architecture defintion</p>
</div>
      
    </li>
  
</ul>


</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1192
1193
1194
1195</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1192</span>

<span class='kw'>def</span> <span class='id identifier rubyid_initialize'>initialize</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_data'>ext_data</span><span class='comma'>,</span> <span class='id identifier rubyid_arch_def'>arch_def</span><span class='rparen'>)</span>
  <span class='kw'>super</span><span class='lparen'>(</span><span class='id identifier rubyid_ext_data'>ext_data</span><span class='rparen'>)</span>
  <span class='ivar'>@arch_def</span> <span class='op'>=</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
  
</div>
<div id="method_missing_details" class="method_details_list">
  <h2>Dynamic Method Handling</h2>
  <p class="notice super">
    This class handles dynamic methods through the <tt>method_missing</tt> method
    
      in the class <span class='object_link'><a href="ArchDefObject.html#method_missing-instance_method" title="ArchDefObject#method_missing (method)">ArchDefObject</a></span>
    
  </p>
  
</div>

  <div id="instance_attr_details" class="attr_details">
    <h2>Instance Attribute Details</h2>
    
      
      <span id=""></span>
      <div class="method_details first">
  <h3 class="signature first" id="arch_def-instance_method">
  
    #<strong>arch_def</strong>  &#x21d2; <tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>  <span class="extras">(readonly)</span>
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns The architecture defintion.</p>


  </div>
</div>
<div class="tags">
  
<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="ArchDef.html" title="ArchDef (class)">ArchDef</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The architecture defintion</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1188
1189
1190</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1188</span>

<span class='kw'>def</span> <span class='id identifier rubyid_arch_def'>arch_def</span>
  <span class='ivar'>@arch_def</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>


  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="implies-instance_method">
  
    #<strong>implies</strong>(version_requirement = &quot;&gt;= 0&quot;)  &#x21d2; <tt>Array&lt;<span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span>&gt;</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>Returns Array of extensions implied by any version of this extension meeting version_requirement.</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>version_requirement</span>
      
      
        <span class='type'>(<tt>String</tt>)</span>
      
      
        <em class="default">(defaults to: <tt>&quot;&gt;= 0&quot;</tt>)</em>
      
      
        &mdash;
        <div class='inline'>
<p>Version requirement</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Returns:</p>
<ul class="return">
  
    <li>
      
      
        <span class='type'>(<tt>Array&lt;<span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span>&gt;</tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>Array of extensions implied by any version of this extension meeting version_requirement</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1199
1200
1201
1202
1203
1204
1205
1206
1207
1208
1209
1210
1211
1212
1213
1214
1215
1216</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1199</span>

<span class='kw'>def</span> <span class='id identifier rubyid_implies'>implies</span><span class='lparen'>(</span><span class='id identifier rubyid_version_requirement'>version_requirement</span> <span class='op'>=</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>&gt;= 0</span><span class='tstring_end'>&quot;</span></span><span class='rparen'>)</span>
  <span class='id identifier rubyid_implications'>implications</span> <span class='op'>=</span> <span class='lbracket'>[</span><span class='rbracket'>]</span>
  <span class='ivar'>@data</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>versions</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_each'>each</span> <span class='kw'>do</span> <span class='op'>|</span><span class='id identifier rubyid_v'>v</span><span class='op'>|</span>
    <span class='kw'>next</span> <span class='kw'>unless</span> <span class='const'>Gem</span><span class='op'>::</span><span class='const'>Requirement</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='id identifier rubyid_version_requirement'>version_requirement</span><span class='rparen'>)</span><span class='period'>.</span><span class='id identifier rubyid_satisfied_by?'>satisfied_by?</span><span class='lparen'>(</span><span class='const'>Gem</span><span class='op'>::</span><span class='const'>Version</span><span class='period'>.</span><span class='id identifier rubyid_new'>new</span><span class='lparen'>(</span><span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>version</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rparen'>)</span>

    <span class='kw'>case</span> <span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implies</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span>
    <span class='kw'>when</span> <span class='kw'>nil</span>
      <span class='kw'>next</span>
    <span class='kw'>when</span> <span class='const'>Array</span>
      <span class='kw'>if</span> <span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implies</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span>
        <span class='id identifier rubyid_implications'>implications</span> <span class='op'>+=</span> <span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implies</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='period'>.</span><span class='id identifier rubyid_map'>map</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_e'>e</span><span class='op'>|</span> <span class='const'><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="ExtensionVersion.html#initialize-instance_method" title="ExtensionVersion#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_e'>e</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span><span class='rparen'>)</span><span class='rbrace'>}</span>
      <span class='kw'>else</span>
        <span class='id identifier rubyid_implications'>implications</span> <span class='op'>&lt;&lt;</span> <span class='const'><span class='object_link'><a href="ExtensionVersion.html" title="ExtensionVersion (class)">ExtensionVersion</a></span></span><span class='period'>.</span><span class='id identifier rubyid_new'><span class='object_link'><a href="ExtensionVersion.html#initialize-instance_method" title="ExtensionVersion#initialize (method)">new</a></span></span><span class='lparen'>(</span><span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implies</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>0</span><span class='rbracket'>]</span><span class='comma'>,</span> <span class='id identifier rubyid_v'>v</span><span class='lbracket'>[</span><span class='tstring'><span class='tstring_beg'>&quot;</span><span class='tstring_content'>implies</span><span class='tstring_end'>&quot;</span></span><span class='rbracket'>]</span><span class='lbracket'>[</span><span class='int'>1</span><span class='rbracket'>]</span><span class='rparen'>)</span>
      <span class='kw'>end</span>
    <span class='kw'>end</span>
  <span class='kw'>end</span>
  <span class='id identifier rubyid_implications'>implications</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
      <div class="method_details ">
  <h3 class="signature " id="instructions-instance_method">
  
    #<strong>instructions</strong>  &#x21d2; <tt>Object</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    
<p>returns the list of instructions implemented by this extension</p>


  </div>
</div>
<div class="tags">
  

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


1219
1220
1221</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/arch_def.rb', line 1219</span>

<span class='kw'>def</span> <span class='id identifier rubyid_instructions'>instructions</span>
  <span class='id identifier rubyid_arch_def'>arch_def</span><span class='period'>.</span><span class='id identifier rubyid_instructions'>instructions</span><span class='period'>.</span><span class='id identifier rubyid_select'>select</span> <span class='lbrace'>{</span> <span class='op'>|</span><span class='id identifier rubyid_i'>i</span><span class='op'>|</span> <span class='id identifier rubyid_i'>i</span><span class='period'>.</span><span class='id identifier rubyid_definedBy'>definedBy</span> <span class='op'>==</span> <span class='id identifier rubyid_name'>name</span> <span class='op'>||</span> <span class='lparen'>(</span><span class='id identifier rubyid_i'>i</span><span class='period'>.</span><span class='id identifier rubyid_definedBy'>definedBy</span><span class='period'>.</span><span class='id identifier rubyid_is_a?'>is_a?</span><span class='lparen'>(</span><span class='const'>Array</span><span class='rparen'>)</span> <span class='op'>&amp;&amp;</span> <span class='id identifier rubyid_i'>i</span><span class='period'>.</span><span class='id identifier rubyid_definedBy'>definedBy</span><span class='period'>.</span><span class='id identifier rubyid_include?'>include?</span><span class='lparen'>(</span><span class='id identifier rubyid_name'>name</span><span class='rparen'>)</span><span class='rparen'>)</span> <span class='rbrace'>}</span>
<span class='kw'>end</span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:47 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>