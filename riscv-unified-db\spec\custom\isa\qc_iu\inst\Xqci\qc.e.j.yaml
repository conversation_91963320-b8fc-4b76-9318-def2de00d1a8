# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../schemas/inst_schema.json

$schema: inst_schema.json#
kind: instruction
name: qc.e.j
long_name: Jump
description: |
  Jump to a PC-relative offset.
  Instruction encoded in QC.EJ instruction format.
definedBy:
  anyOf:
    - Xqci
    - Xqcilb
assembly: " imm"
base: 32
encoding:
  match: -----------------------00000---00100-----0011111
  variables:
    - name: imm
      location: 47-32|19-17|31|7|30-25|11-8
      left_shift: 1
      sign_extend: true
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  jump_halfword($pc + imm);
