# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: fsgnjn.q
long_name: No synopsis available
description: |
  No description available.
definedBy: Q
assembly: fd, fs1, fs2
encoding:
  match: 0010011----------001-----1010011
  variables:
    - name: fs2
      location: 24-20
    - name: fs1
      location: 19-15
    - name: fd
      location: 11-7
access:
  s: always
  u: always
  vs: always
  vu: always
data_independent_timing: false
pseudoinstructions:
  - when: (fs2 == fs1)
    to: fneg.q fd, fs1
operation(): |
