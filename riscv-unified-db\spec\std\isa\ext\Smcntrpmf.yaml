# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Smcntrpmf
long_name: Cycle and Instret Privilege Mode Filtering
description: Cycle and Instret Privilege Mode Filtering
type: privileged
versions:
  - version: "1.0.0"
    state: ratified
    ratification_date: 2023-08
    url: https://github.com/riscv/riscv-smcntrpmf/releases/download/v1.0_rc4-frozen/riscv-smcntrpmf-v1.0_rc4.pdf
