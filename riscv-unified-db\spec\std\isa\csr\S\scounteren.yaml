# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.

# WARNING: This file is auto-generated from spec/std/isa/csr/S/scounteren.layout# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: csr_schema.json#
kind: csr
name: scounteren
long_name: Supervisor Counter Enable
address: 0x106
priv_mode: S
length: 32
description: |
  Delegates control of the hardware performance-monitoring counters
  to U-mode
definedBy: S
fields:
  CY:
    location: 0
    description: |
      When both `scounteren.CY` and `mcounteren.CY` are set, the `cycle` CSR (an alias of `mcycle`) is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.CY`)<% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[0]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[0]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  TM:
    location: 1
    description: |
      When both `scounteren.TM` and `mcounteren.TM` are set, the `time` CSR (an alias of `mtime` memory-mapped CSR) is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.TM`)<% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[1]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[1]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  IR:
    location: 2
    description: |
      When both `scounteren.IR` and `mcounteren.IR` are set, the `instret` CSR (an alias of memory-mapped `minstret`) is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.IR`)<% end %>.
    definedBy: Zicntr
    type(): |
      if (SCOUNTENABLE_EN[2]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[2]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM3:
    location: 3
    description: |
      When both `scounteren.HPM3` and `mcounteren.HPM3` are set, the `hpmcounter3` CSR (an alias of `mhpmcounter3`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM3`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[3]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[3]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM4:
    location: 4
    description: |
      When both `scounteren.HPM4` and `mcounteren.HPM4` are set, the `hpmcounter4` CSR (an alias of `mhpmcounter4`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM4`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[4]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[4]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM5:
    location: 5
    description: |
      When both `scounteren.HPM5` and `mcounteren.HPM5` are set, the `hpmcounter5` CSR (an alias of `mhpmcounter5`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM5`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[5]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[5]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM6:
    location: 6
    description: |
      When both `scounteren.HPM6` and `mcounteren.HPM6` are set, the `hpmcounter6` CSR (an alias of `mhpmcounter6`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM6`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[6]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[6]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM7:
    location: 7
    description: |
      When both `scounteren.HPM7` and `mcounteren.HPM7` are set, the `hpmcounter7` CSR (an alias of `mhpmcounter7`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM7`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[7]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[7]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM8:
    location: 8
    description: |
      When both `scounteren.HPM8` and `mcounteren.HPM8` are set, the `hpmcounter8` CSR (an alias of `mhpmcounter8`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM8`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[8]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[8]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM9:
    location: 9
    description: |
      When both `scounteren.HPM9` and `mcounteren.HPM9` are set, the `hpmcounter9` CSR (an alias of `mhpmcounter9`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM9`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[9]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[9]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM10:
    location: 10
    description: |
      When both `scounteren.HPM10` and `mcounteren.HPM10` are set, the `hpmcounter10` CSR (an alias of `mhpmcounter10`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM10`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[10]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[10]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM11:
    location: 11
    description: |
      When both `scounteren.HPM11` and `mcounteren.HPM11` are set, the `hpmcounter11` CSR (an alias of `mhpmcounter11`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM11`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[11]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[11]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM12:
    location: 12
    description: |
      When both `scounteren.HPM12` and `mcounteren.HPM12` are set, the `hpmcounter12` CSR (an alias of `mhpmcounter12`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM12`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[12]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[12]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM13:
    location: 13
    description: |
      When both `scounteren.HPM13` and `mcounteren.HPM13` are set, the `hpmcounter13` CSR (an alias of `mhpmcounter13`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM13`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[13]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[13]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM14:
    location: 14
    description: |
      When both `scounteren.HPM14` and `mcounteren.HPM14` are set, the `hpmcounter14` CSR (an alias of `mhpmcounter14`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM14`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[14]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[14]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM15:
    location: 15
    description: |
      When both `scounteren.HPM15` and `mcounteren.HPM15` are set, the `hpmcounter15` CSR (an alias of `mhpmcounter15`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM15`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[15]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[15]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM16:
    location: 16
    description: |
      When both `scounteren.HPM16` and `mcounteren.HPM16` are set, the `hpmcounter16` CSR (an alias of `mhpmcounter16`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM16`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[16]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[16]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM17:
    location: 17
    description: |
      When both `scounteren.HPM17` and `mcounteren.HPM17` are set, the `hpmcounter17` CSR (an alias of `mhpmcounter17`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM17`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[17]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[17]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM18:
    location: 18
    description: |
      When both `scounteren.HPM18` and `mcounteren.HPM18` are set, the `hpmcounter18` CSR (an alias of `mhpmcounter18`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM18`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[18]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[18]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM19:
    location: 19
    description: |
      When both `scounteren.HPM19` and `mcounteren.HPM19` are set, the `hpmcounter19` CSR (an alias of `mhpmcounter19`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM19`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[19]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[19]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM20:
    location: 20
    description: |
      When both `scounteren.HPM20` and `mcounteren.HPM20` are set, the `hpmcounter20` CSR (an alias of `mhpmcounter20`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM20`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[20]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[20]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM21:
    location: 21
    description: |
      When both `scounteren.HPM21` and `mcounteren.HPM21` are set, the `hpmcounter21` CSR (an alias of `mhpmcounter21`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM21`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[21]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[21]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM22:
    location: 22
    description: |
      When both `scounteren.HPM22` and `mcounteren.HPM22` are set, the `hpmcounter22` CSR (an alias of `mhpmcounter22`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM22`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[22]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[22]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM23:
    location: 23
    description: |
      When both `scounteren.HPM23` and `mcounteren.HPM23` are set, the `hpmcounter23` CSR (an alias of `mhpmcounter23`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM23`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[23]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[23]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM24:
    location: 24
    description: |
      When both `scounteren.HPM24` and `mcounteren.HPM24` are set, the `hpmcounter24` CSR (an alias of `mhpmcounter24`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM24`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[24]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[24]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM25:
    location: 25
    description: |
      When both `scounteren.HPM25` and `mcounteren.HPM25` are set, the `hpmcounter25` CSR (an alias of `mhpmcounter25`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM25`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[25]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[25]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM26:
    location: 26
    description: |
      When both `scounteren.HPM26` and `mcounteren.HPM26` are set, the `hpmcounter26` CSR (an alias of `mhpmcounter26`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM26`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[26]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[26]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM27:
    location: 27
    description: |
      When both `scounteren.HPM27` and `mcounteren.HPM27` are set, the `hpmcounter27` CSR (an alias of `mhpmcounter27`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM27`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[27]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[27]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM28:
    location: 28
    description: |
      When both `scounteren.HPM28` and `mcounteren.HPM28` are set, the `hpmcounter28` CSR (an alias of `mhpmcounter28`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM28`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[28]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[28]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM29:
    location: 29
    description: |
      When both `scounteren.HPM29` and `mcounteren.HPM29` are set, the `hpmcounter29` CSR (an alias of `mhpmcounter29`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM29`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[29]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[29]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM30:
    location: 30
    description: |
      When both `scounteren.HPM30` and `mcounteren.HPM30` are set, the `hpmcounter30` CSR (an alias of `mhpmcounter30`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM30`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[30]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[30]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
  HPM31:
    location: 31
    description: |
      When both `scounteren.HPM31` and `mcounteren.HPM31` are set, the `hpmcounter31` CSR (an alias of `mhpmcounter31`)
      is accessible to U-mode
      <% if ext?(:H) %>(delegation to VS/VU mode is further handled by `hcounteren.HPM31`)<% end %>.
    definedBy: Zihpm
    type(): |
      if (SCOUNTENABLE_EN[31]) {
        return CsrFieldType::RW;
      } else {
        return CsrFieldType::RO;
      }
    reset_value(): |
      if (SCOUNTENABLE_EN[31]) {
        return UNDEFINED_LEGAL;
      } else {
        return 0;
      }
