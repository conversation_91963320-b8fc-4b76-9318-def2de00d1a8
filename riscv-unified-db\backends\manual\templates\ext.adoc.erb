<%= anchor_for_udb_doc_ext(ext.name) %>
= <%= ext.name %> Extension
<%= ext.long_name %>

== Versions

<%- ext.versions.each do |v| -%>
<%= v.canonical_version %>::
  State:::
    <%= v.state %>
  <%- if v.state == "ratified" -%>
  Ratification date:::
    <%= v.ratification_date %>
  <%- end -%>
  <%- unless v.changes.empty? -%>
  Changes:::

    <%- v.changes.each do |change| %>
    * <%= change %>
    <%- end -%>

  <%- end -%>
  <%- unless v.url.nil? -%>
  Ratification document:::
    <%= v.url %>
  <%- end -%>
  <%- unless v.implications -%>
  Implies:::
    <%- v.implications.each do |i| -%>
    * `<%= i.name %>` version <%= i.version %>
    <%- end -%>
  <%- end -%>
<%- end -%>

== Synopsis

<%= ext.description %>

<%- insts = ext.instructions.select { |inst| ext.versions.any? { |ext_ver| inst.defined_by_condition.possibly_satisfied_by?(ext_ver) } } -%>
<%- unless insts.empty? -%>
== Instructions

The following instructions are affected by this extension:

[cols="1,3"]
|===
<%- insts.each do |inst| -%>
 | <%= "`#{inst.name}`" %> | *<%= inst.long_name %>*
<%- end -%>
|===
<%- end -%>

<%- unless ext.params.empty? -%>
== Parameters

This extension has the following implementation options (AKA parameters):

<%- ext.params.sort_by { |p| p.name }.each do |param| -%>
<%= anchor_for_udb_doc_ext_param(ext.name, param.name) %>
<%= param.name %>::
+
--
|===
h| Type | <%= param.schema.type_pretty %>
h| Valid Values | <%= param.schema.to_pretty_s %>
h| Description a| <%= param.desc %>
|===
--
<%- end -%>

<%- end -%>
