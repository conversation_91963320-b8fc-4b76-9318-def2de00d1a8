# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/csr_schema.json

$schema: "csr_schema.json#"
kind: csr
name: hedeleg
long_name: Hypervisor Exception Delegation
address: 0x602
writable: true
priv_mode: S
length: 64
description: |
  Controls exception delegation from HS-mode to VS-mode.

  By default, all traps at any privilege level are handled in M-mode, though M-mode usually uses
  the `medeleg` and `mideleg` CSRs to delegate some traps to HS-mode. The `hedeleg` and `hideleg`
  CSRs allow these traps to be further delegated to a VS-mode guest; their layout is the same as
  `medeleg` and `mideleg`.

  A synchronous trap that has been delegated to HS-mode (using `medeleg`)
  is further delegated to VS-mode if V=1 before the trap and the
  corresponding `hedeleg` bit is set. Each bit of `hedeleg` shall be
  either writable or read-only zero. Many bits of `hedeleg` are required
  specifically to be writable or zero. Bit 0, corresponding to
  instruction address misaligned exceptions, must be writable if
  IALIGN=32.

  [NOTE]
  ====
  Requiring that certain bits of `hedeleg` be writable reduces some of the
  burden on a hypervisor to handle variations of implementation.
  ====

  When XLEN=32, `hedelegh` is a 32-bit read/write register
  that aliases bits 63:32 of `hedeleg`.
  Register `hedelegh` does not exist when XLEN=64.

definedBy: H
fields:
  IAM:
    location: 0
    description: |
      *Instruction Address Misaligned*

      Controls delegation of Instruction Address Misaligned exceptions to VS-mode.

      See `medeleg.IAM` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  IAF:
    location: 1
    description: |
      *Instruction Access Fault*

      Controls delegation of Instruction Access Fault exceptions to VS-mode.

      See `medeleg.IAF` for details.

    type: RW
    reset_value: UNDEFINED_LEGAL
  II:
    location: 2
    description: |
      *Illegal Instruction*

      Controls delegation of Illegal Instruction exceptions to VS-mode.

      See `medeleg.II` for details.

    type: RW
    reset_value: UNDEFINED_LEGAL
  B:
    location: 3
    description: |
      *Breakpoint*

      Controls delegation of Breakpoint exceptions to VS-mode.

      See `medeleg.B` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  LAM:
    location: 4
    description: |
      *Load Address Misaligned*

      Controls delegation of Load Address Misaligned exceptions to VS-mode.

      See `medeleg.LAM` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  LAF:
    location: 5
    description: |
      *Load Access Fault*

      Controls delegation of Load Access Fault exceptions to VS-mode.

      See `medeleg.LAF` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  SAM:
    location: 6
    description: |
      *Store/AMO Address Misaligned*

      Controls delegation of Store/AMO Address Misaligned exceptions to VS-mode.

      See `medeleg.SAM` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  SAF:
    location: 7
    description: |
      *Store/AMO Access Fault*

      Controls delegation of Store/AMO Access Fault exceptions to VS-mode.

      See `medeleg.SAF` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  EU:
    location: 8
    description: |
      *Environment Call from VU-mode*

      Controls delegation of Environment Call from VU-mode exceptions to VS-mode.

      See `medeleg.EU` for details.

    type: RW
    reset_value: UNDEFINED_LEGAL
  ES:
    location: 9
    description: |
      *Environment Call from HS-mode*

      Environment Call from HS-mode exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.ES` for details.
    type: RO
    reset_value: 0
  EVS:
    location: 10
    description: |
      *Environment Call from VS-mode*

      Environment Call from VS-mode exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.EVS` for details.
    type: RO
    reset_value: 0
  EM:
    location: 11
    description: |
      *Environment Call from M-mode*

      Environment Call from M-mode exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.EM` for details.
    type: RO
    reset_value: 0
  IPF:
    location: 12
    description: |
      *Instruction Page Fault*

      Controls delegation of Instruction Page Fault exceptions to VS-mode.

      See `medeleg.IPF` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  LPF:
    location: 13
    description: |
      *Load Page Fault*

      Controls delegation of Load Page Fault exceptions to VS-mode.

      See `medeleg.LPF` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  SPF:
    location: 15
    description: |
      *Store/AMO Page Fault*

      Controls delegation of Store/AMO Page Fault exceptions to VS-mode.

      See `medeleg.SPF` for details.
    type: RW
    reset_value: UNDEFINED_LEGAL
  IGPF:
    location: 20
    description: |
      *Instruction Guest Page Fault*

      Instruction Guest Page Fault exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.IGPF` for details.
    type: RO
    reset_value: 0
  LGPF:
    location: 21
    description: |
      *Load Guest Page Fault*

      Load Guest Page Fault exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.LGPF` for details.
    type: RO
    reset_value: 0
  VI:
    location: 22
    description: |
      *Virtual Instruction*

      Virtual Instruction exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.VI` for details.
    type: RO
    reset_value: 0
  SGPF:
    location: 23
    description: |
      *Store/AMO Guest Page Fault*

      Store/AMO Guest Page Fault exceptions _cannot be delegated to VS-mode_,
      so this field is read-only 0.

      See `medeleg.SGPF` for details.
    type: RO
    reset_value: 0
