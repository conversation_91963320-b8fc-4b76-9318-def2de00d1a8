# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/inst_schema.json

$schema: "inst_schema.json#"
kind: instruction
name: qc.cm.pushfp
long_name: Create function call stack frame with frame pointer
description: |
  Create stack frame: store `ra` and 0 to 12 saved registers to the stack frame, optionally allocate additional stack space.
  As well, instruction initializes frame pointer (`fp`, aka `x8`) to the Canonical Frame Address (`sp` aka `x2`) on function entry.
  This instruction pushes (stores) the registers in rlist to the memory below the stack pointer,
  and then creates the stack frame by decrementing the stack pointer by stack_adj, including any additional stack space requested by the value of `spimm`.

  Restrictions on stack_adj:

  * it must be enough to store all of the listed registers
  * it must be a multiple of 16 (bytes):
  ** for RV32 the allowed values are: 16, 32, 48, 64, 80, 96, 112
  ** for RV64 the allowed values are: 16, 32, 48, 64, 80, 96, 112, 128, 144, 160
definedBy: Xqccmp
assembly: reg_list, -stack_adj
encoding:
  match: 10111001------10
  variables:
    - name: rlist
      location: 7-4
      not: [0, 1, 2, 3, 4]
    - name: spimm
      location: 3-2
      left_shift: 4
access:
  s: always
  u: always
  vs: always
  vu: always
operation(): |
  if (implemented?(ExtensionName::Xqccmp) && (CSR[misa].C == 1'b0)) {
    raise(ExceptionCode::IllegalInstruction, mode(), $encoding);
  }

  XReg virtual_address_sp = get_and_validate_stack_pointer(X[2], $encoding);
  XReg size = xlen() / 8;
  XReg nreg = (rlist == 15) ? 13 : (rlist - 3);
  XReg stack_aligned_adj = (nreg * size + 15) & ~0xF;
  XReg virtual_address_new_sp = virtual_address_sp - stack_aligned_adj - (spimm `<< 4);
  XReg virtual_address_base = virtual_address_sp - size;

  write_memory_xlen(virtual_address_base -  0*size, X[ 1], $encoding);
  if (nreg > 1) {
    write_memory_xlen(virtual_address_base -  1*size, X[ 8], $encoding);
  }
  if (nreg > 2) {
    write_memory_xlen(virtual_address_base -  2*size, X[ 9], $encoding);
  }
  if (nreg > 3) {
    write_memory_xlen(virtual_address_base -  3*size, X[18], $encoding);
  }
  if (nreg > 4) {
    write_memory_xlen(virtual_address_base -  4*size, X[19], $encoding);
  }
  if (nreg > 5) {
    write_memory_xlen(virtual_address_base -  5*size, X[20], $encoding);
  }
  if (nreg > 6) {
    write_memory_xlen(virtual_address_base -  6*size, X[21], $encoding);
  }
  if (nreg > 7) {
    write_memory_xlen(virtual_address_base -  7*size, X[22], $encoding);
  }
  if (nreg > 8) {
    write_memory_xlen(virtual_address_base -  8*size, X[23], $encoding);
  }
  if (nreg > 9) {
    write_memory_xlen(virtual_address_base -  9*size, X[24], $encoding);
  }
  if (nreg > 10) {
    write_memory_xlen(virtual_address_base - 10*size, X[25], $encoding);
  }
  if (nreg > 11) {
    write_memory_xlen(virtual_address_base - 11*size, X[26], $encoding);
    write_memory_xlen(virtual_address_base - 12*size, X[27], $encoding);
  }

  X[8] = virtual_address_sp;
  X[2] = virtual_address_new_sp;
