<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
  Module: Idl::Executable
  
    &mdash; Documentation by YARD 0.9.36
  
</title>

  <link rel="stylesheet" href="../css/style.css" type="text/css" />

  <link rel="stylesheet" href="../css/common.css" type="text/css" />

<script type="text/javascript">
  pathId = "Idl::Executable";
  relpath = '../';
</script>


  <script type="text/javascript" charset="utf-8" src="../js/jquery.js"></script>

  <script type="text/javascript" charset="utf-8" src="../js/app.js"></script>


  </head>
  <body>
    <div class="nav_wrap">
      <iframe id="nav" src="../class_list.html?1"></iframe>
      <div id="resizer"></div>
    </div>

    <div id="main" tabindex="-1">
      <div id="header">
        <div id="menu">
  
    <a href="../_index.html">Index (E)</a> &raquo;
    <span class='title'><span class='object_link'><a href="../Idl.html" title="Idl (module)">Idl</a></span></span>
     &raquo; 
    <span class="title">Executable</span>
  
</div>

        <div id="search">
  
    <a class="full_list_link" id="class_list_link"
        href="../class_list.html">

        <svg width="24" height="24">
          <rect x="0" y="4" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="12" width="24" height="4" rx="1" ry="1"></rect>
          <rect x="0" y="20" width="24" height="4" rx="1" ry="1"></rect>
        </svg>
    </a>
  
</div>
        <div class="clear"></div>
      </div>

      <div id="content"><h1>Module: Idl::Executable
  
  
  
</h1>
<div class="box_info">
  

  
  
  
  
  

  
  <dl>
    <dt>Included in:</dt>
    <dd><span class='object_link'><a href="AssignmentAst.html" title="Idl::AssignmentAst (class)">AssignmentAst</a></span>, <span class='object_link'><a href="CsrSoftwareWriteAst.html" title="Idl::CsrSoftwareWriteAst (class)">CsrSoftwareWriteAst</a></span>, <span class='object_link'><a href="CsrWriteAst.html" title="Idl::CsrWriteAst (class)">CsrWriteAst</a></span>, <span class='object_link'><a href="ForLoopAst.html" title="Idl::ForLoopAst (class)">ForLoopAst</a></span>, <span class='object_link'><a href="FunctionBodyAst.html" title="Idl::FunctionBodyAst (class)">FunctionBodyAst</a></span>, <span class='object_link'><a href="FunctionCallExpressionAst.html" title="Idl::FunctionCallExpressionAst (class)">FunctionCallExpressionAst</a></span>, <span class='object_link'><a href="GlobalAst.html" title="Idl::GlobalAst (class)">GlobalAst</a></span>, <span class='object_link'><a href="GlobalWithInitializationAst.html" title="Idl::GlobalWithInitializationAst (class)">GlobalWithInitializationAst</a></span>, <span class='object_link'><a href="IfAst.html" title="Idl::IfAst (class)">IfAst</a></span>, <span class='object_link'><a href="IfBodyAst.html" title="Idl::IfBodyAst (class)">IfBodyAst</a></span>, <span class='object_link'><a href="InstructionOperationAst.html" title="Idl::InstructionOperationAst (class)">InstructionOperationAst</a></span>, <span class='object_link'><a href="PostDecrementExpressionAst.html" title="Idl::PostDecrementExpressionAst (class)">PostDecrementExpressionAst</a></span>, <span class='object_link'><a href="PostIncrementExpressionAst.html" title="Idl::PostIncrementExpressionAst (class)">PostIncrementExpressionAst</a></span>, <span class='object_link'><a href="StatementAst.html" title="Idl::StatementAst (class)">StatementAst</a></span>, <span class='object_link'><a href="VariableDeclarationWithInitializationAst.html" title="Idl::VariableDeclarationWithInitializationAst (class)">VariableDeclarationWithInitializationAst</a></span></dd>
  </dl>
  

  
  <dl>
    <dt>Defined in:</dt>
    <dd>lib/idl/ast.rb</dd>
  </dl>
  
</div>

<h2>Overview</h2><div class="docstring">
  <div class="discussion">
    
<p>interface for nodes that can be executed, but don’t have a value (e.g., statements)</p>


  </div>
</div>
<div class="tags">
  

</div>






  
    <h2>
      Instance Method Summary
      <small><a href="#" class="summary_toggle">collapse</a></small>
    </h2>

    <ul class="summary">
      
        <li class="public ">
  <span class="summary_signature">
    
      <a href="#execute-instance_method" title="#execute (instance method)">#<strong>execute</strong>(symtab)  &#x21d2; void </a>
    

    
  </span>
  
  
  
  
  
  
  

  
    <span class="summary_desc"><div class='inline'>
<p>“execute” the statement by updating the variables in the symbol table.</p>
</div></span>
  
</li>

      
    </ul>
  



  <div id="instance_method_details" class="method_details_list">
    <h2>Instance Method Details</h2>

    
      <div class="method_details first">
  <h3 class="signature first" id="execute-instance_method">
  
    #<strong>execute</strong>(symtab)  &#x21d2; <tt>void</tt> 
  

  

  
</h3><div class="docstring">
  <div class="discussion">
    <p class="note returns_void">This method returns an undefined value.</p>
<p>“execute” the statement by updating the variables in the symbol table</p>


  </div>
</div>
<div class="tags">
  <p class="tag_title">Parameters:</p>
<ul class="param">
  
    <li>
      
        <span class='name'>symtab</span>
      
      
        <span class='type'>(<tt><span class='object_link'><a href="SymbolTable.html" title="Idl::SymbolTable (class)">SymbolTable</a></span></tt>)</span>
      
      
      
        &mdash;
        <div class='inline'>
<p>The symbol table for the context</p>
</div>
      
    </li>
  
</ul>

<p class="tag_title">Raises:</p>
<ul class="raise">
  
    <li>
      
      
        <span class='type'></span>
      
      
      
        
        <div class='inline'>
<p>ValueError if some part of the statement cannot be executed at compile time</p>
</div>
      
    </li>
  
</ul>

</div><table class="source_code">
  <tr>
    <td>
      <pre class="lines">


318</pre>
    </td>
    <td>
      <pre class="code"><span class="info file"># File 'lib/idl/ast.rb', line 318</span>

<span class='kw'>def</span> <span class='id identifier rubyid_execute'>execute</span><span class='lparen'>(</span><span class='id identifier rubyid_symtab'>symtab</span><span class='rparen'>)</span> <span class='op'>=</span> <span class='id identifier rubyid_raise'>raise</span> <span class='const'>NotImplementedError</span><span class='comma'>,</span> <span class='tstring'><span class='tstring_beg'>&quot;</span><span class='embexpr_beg'>#{</span><span class='kw'>self</span><span class='period'>.</span><span class='id identifier rubyid_class'>class</span><span class='period'>.</span><span class='id identifier rubyid_name'>name</span><span class='embexpr_end'>}</span><span class='tstring_content'> must implement execute</span><span class='tstring_end'>&quot;</span></span></pre>
    </td>
  </tr>
</table>
</div>
    
  </div>

</div>

      <div id="footer">
  Generated on Thu Jul 18 13:55:44 2024 by
  <a href="https://yardoc.org" title="Yay! A Ruby Documentation Tool" target="_parent">yard</a>
  0.9.36 (ruby-3.2.3).
</div>

    </div>
  </body>
</html>