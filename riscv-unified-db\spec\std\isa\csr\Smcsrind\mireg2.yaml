# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../../../schemas/csr_schema.json
$schema: csr_schema.json#
kind: csr
name: mireg2
long_name: Machine Indirect Register Alias 2
address: 0x352
priv_mode: M
length: MXLEN
definedBy: Smcsrind
description:
  - id: csr-mireg2-purpose
    normative: true
    text: |
      The `mireg2` machine indirect alias CSR is used to access register state indirectly
      upon a read or write, as determined by the value of `miselect`.

  - id: csr-mireg2-unimplemented-miselect
    normative: true
    text: |
      The behavior upon accessing `mireg2` from M-mode, while `miselect` holds a value
      that is not implemented, is UNSPECIFIED.

  - id: csr-mireg2-unimplemented-miselect-typical
    normative: false
    text: |
      It is expected that implementations will typically raise an illegal instruction exception for
      such accesses, so that, for example, they can be identified as software bugs. Platform
      specs, profile specs, and/or the Privileged ISA spec may place more restrictions on
      behavior for such accesses.

  - id: csr-mireg2-implemented-miselect
    normative: true
    text: |
      Attempts to access `mireg2` while `miselect` holds a number in an allocated and implemented
      range results in a specific behavior that, for each combination of `miselect` and `mireg2`, is
      defined by the extension to which the `miselect` value is allocated.

  - id: csr-mireg2-behavior-typical
    normative: false
    text: |
      Ordinarily, `mireg2` will access register state, access read-only 0 state, or raise an
      illegal instruction exception.

  - id: csr-mireg2-rv32-64bit-access
    normative: false
    text: |
      For RV32, if an extension defines an indirectly accessed register as 64 bits wide, it is
      recommended that the lower 32 bits of the register are accessed through `mireg2`,
      while the upper 32 bits are accessed through `mireg5`.

fields:
  VALUE:
    long_name: Indirect Register Value
    location_rv32: 31-0
    location_rv64: 63-0
    type: RW
    description:
      - id: csr-mireg2-value-purpose
        normative: true
        text: Register state of the CSR selected by the current `miselect` value
    reset_value: UNDEFINED_LEGAL
    sw_write(csr_value): |
      Csr handle = indirect_csr_lookup(CSR[miselect].VALUE, 2);
      if (!handle.valid) {
        unimplemented_csr($encoding);
      }
      if (!handle.writable) {
        raise (ExceptionCode::IllegalInstruction, mode(), $encoding);
      }
      csr_sw_write(handle, csr_value.VALUE);
      return csr_hw_read(handle);
sw_read(): |
  Csr handle = indirect_csr_lookup(CSR[miselect].VALUE, 2);
  if (!handle.valid) {
    unimplemented_csr($encoding);
  }
  return csr_sw_read(handle);
