# Copyright (c) Qualcomm Technologies, Inc. and/or its subsidiaries.
# SPDX-License-Identifier: BSD-3-Clause-Clear

# yaml-language-server: $schema=../../schemas/ext_schema.json

$schema: "ext_schema.json#"
kind: extension
name: Zicsr
long_name: Control and status registers
description: Control and status registers
type: unprivileged
versions:
  - version: "2.0.0"
    state: ratified
    ratification_date: null
